# استكشاف أخطاء حل مشكلة رمز الأمان

## إذا ظهر خطأ "Critical Error"

### 1. تحقق من سجلات الأخطاء
```bash
# في cPanel أو لوحة الاستضافة، ابحث عن:
/public_html/wp-content/debug.log
# أو
/error_logs/
```

### 2. تفعيل تسجيل الأخطاء
أضف هذه الأسطر في ملف `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### 3. الأخطاء الشائعة وحلولها

#### خطأ: "Call to undefined function"
**السبب**: دالة غير موجودة أو ملف غير محمل
**الحل**: تأكد من تحميل جميع الملفات المطلوبة

#### خطأ: "Parse error" أو "Syntax error"
**السبب**: خطأ نحوي في الكود
**الحل**: تحقق من:
- إغلاق جميع الأقواس `{}`
- إغلاق جميع علامات PHP `?>`
- عدم وجود فواصل منقوطة مفقودة

#### خطأ: "Cannot redeclare function"
**السبب**: دالة معرفة أكثر من مرة
**الحل**: تحقق من عدم تكرار تعريف الدوال

### 4. خطوات الإصلاح السريع

#### الخطوة 1: إلغاء تفعيل الإضافة
```php
// في wp-config.php أضف:
define('WP_DEBUG', true);
// ثم ادخل لوحة الإدارة وألغ تفعيل الإضافة
```

#### الخطوة 2: تحقق من الملفات المعدلة
تأكد من أن هذه الملفات لا تحتوي على أخطاء:
- `includes/class-pexlat-form-admin.php`
- `includes/class-pexlat-form-form-handler.php`
- `includes/class-custom-cart-system.php`
- `includes/class-pexlat-form.php`

#### الخطوة 3: استعادة نسخة احتياطية
إذا كان لديك نسخة احتياطية من الملفات الأصلية:
1. استبدل الملفات المعدلة بالنسخة الأصلية
2. فعل الإضافة مرة أخرى
3. طبق التعديلات تدريجياً

### 5. اختبار الحلول

#### اختبار 1: تحقق من دالة تجديد رمز الأمان
```javascript
// في console المتصفح:
jQuery.ajax({
    url: '/wp-admin/admin-ajax.php',
    type: 'POST',
    data: { action: 'pexlat_form_refresh_nonce' },
    success: function(response) {
        console.log('نتيجة الاختبار:', response);
    }
});
```

#### اختبار 2: تحقق من تحميل JavaScript
```javascript
// في console المتصفح:
console.log('formElrakami متوفر:', typeof formElrakami !== 'undefined');
console.log('ajaxurl متوفر:', typeof ajaxurl !== 'undefined');
```

### 6. حلول بديلة مؤقتة

#### الحل 1: تعطيل التجديد التلقائي
في ملف `public/js/pexlat-form-public.js`، علق على هذا السطر:
```javascript
// setInterval(function() { refreshNonceForAllForms(); }, 20 * 60 * 1000);
```

#### الحل 2: زيادة مدة صلاحية رمز الأمان
أضف في ملف `functions.php` للقالب:
```php
add_filter('nonce_life', function() {
    return 12 * HOUR_IN_SECONDS; // 12 ساعة بدلاً من 24
});
```

#### الحل 3: تعطيل فحص رمز الأمان مؤقتاً (غير آمن)
في ملف `includes/class-pexlat-form-form-handler.php`:
```php
private function verify_security_token() {
    return true; // تعطيل مؤقت - احذف هذا السطر بعد الإصلاح
}
```

### 7. التحقق من البيئة

#### متطلبات الخادم
- PHP 7.4 أو أحدث
- WordPress 5.0 أو أحدث
- WooCommerce 4.0 أو أحدث

#### إعدادات PHP المطلوبة
```ini
memory_limit = 256M
max_execution_time = 300
max_input_vars = 3000
```

### 8. التواصل للدعم

إذا استمرت المشكلة، أرسل هذه المعلومات:

1. **رسالة الخطأ الكاملة** من سجل الأخطاء
2. **إصدار WordPress** و **إصدار WooCommerce**
3. **إصدار PHP** المستخدم
4. **اسم الاستضافة** ونوع الخادم
5. **قائمة الإضافات** المفعلة
6. **اسم القالب** المستخدم

### 9. نصائح الوقاية

1. **عمل نسخة احتياطية** قبل أي تعديل
2. **اختبار التعديلات** في بيئة تطوير أولاً
3. **مراقبة سجلات الأخطاء** بانتظام
4. **تحديث WordPress وWooCommerce** بانتظام
5. **استخدام قالب فرعي** لتجنب فقدان التعديلات

### 10. أدوات مفيدة

- **Query Monitor**: لمراقبة الأداء والأخطاء
- **Debug Bar**: لعرض معلومات التصحيح
- **WP Debugging**: لتفعيل وضع التصحيح
- **Health Check**: لاختبار صحة الموقع

---

**ملاحظة مهمة**: هذه الحلول مصممة لمعالجة مشكلة "رمز الأمان غير صحيح" في إضافة Pexlat Form. إذا كنت تواجه مشاكل أخرى، قد تحتاج لحلول مختلفة.
