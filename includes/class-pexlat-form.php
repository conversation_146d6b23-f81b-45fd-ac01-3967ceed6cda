<?php
/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * @since      1.0.0
 * @package    Pexlat_Form
 */
class Pexlat_Form {

    /**
     * The loader that's responsible for maintaining and registering all hooks that power
     * the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      Pexlat_Form_Loader    $loader    Maintains and registers all hooks for the plugin.
     */
    protected $loader;

    /**
     * The unique identifier of this plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $plugin_name    The string used to uniquely identify this plugin.
     */
    protected $plugin_name;

    /**
     * The current version of the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $version    The current version of the plugin.
     */
    protected $version;

    /**
     * Define the core functionality of the plugin.
     *
     * Set the plugin name and the plugin version that can be used throughout the plugin.
     * Load the dependencies, define the locale, and set the hooks for the admin area and
     * the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function __construct() {
        if (defined('PEXLAT_FORM_VERSION')) {
            $this->version = PEXLAT_FORM_VERSION;
        } else {
            $this->version = '1.0.0';
        }
        $this->plugin_name = 'pexlat-form';

        $this->load_dependencies();
        $this->set_locale();
        $this->define_admin_hooks();
        $this->define_public_hooks();
        $this->define_woocommerce_hooks();
    }

    /**
     * Load the required dependencies for this plugin.
     *
     * Include the following files that make up the plugin:
     *
     * - Pexlat_Form_Loader. Orchestrates the hooks of the plugin.
     * - Pexlat_Form_Admin. Defines all hooks for the admin area.
     * - Pexlat_Form_Form_Handler. Defines all hooks for form handling.
     * - Woo_Integration. Defines all hooks for WooCommerce integration.
     *
     * Create an instance of the loader which will be used to register the hooks
     * with WordPress.
     *
     * @since    1.0.0
     * @access   private
     */
    private function load_dependencies() {

        /**
         * The class responsible for orchestrating the actions and filters of the
         * core plugin.
         */
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-pexlat-form-loader.php';

        /**
         * The class responsible for defining all actions that occur in the admin area.
         */
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-pexlat-form-admin.php';

        /**
         * The class responsible for managing shipping zones.
         */
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-shipping-zones-manager.php';

        /**
         * The class responsible for handling form submissions and processing.
         */
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-pexlat-form-form-handler.php';

        /**
         * The class responsible for WooCommerce integration.
         */
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-woo-integration.php';


        $this->loader = new Pexlat_Form_Loader();

    }

    /**
     * Define the locale for this plugin for internationalization.
     *
     * Uses the Pexlat_Form_i18n class in order to set the domain and to register the hook
     * with WordPress.
     *
     * @since    1.0.0
     * @access   private
     */
    private function set_locale() {
        // Load the internationalization class
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-pexlat-form-i18n.php';

        // Initialize the i18n class
        $plugin_i18n = new Pexlat_Form_i18n();

        // Register the hook to load the plugin text domain
        $this->loader->add_action('plugins_loaded', $plugin_i18n, 'load_plugin_textdomain');
    }

    /**
     * Register all of the hooks related to the admin area functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_admin_hooks() {

        $plugin_admin = new Pexlat_Form_Admin($this->get_plugin_name(), $this->get_version());

        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_styles');
        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts');

        // Admin menus
        $this->loader->add_action('admin_menu', $plugin_admin, 'add_admin_menu');

        // Form handling in admin
        $this->loader->add_action('admin_init', $plugin_admin, 'handle_form_actions');

        // Admin-Ajax endpoints
        $this->loader->add_action('wp_ajax_pexlat_form_update_field_visibility', $plugin_admin, 'update_field_visibility_ajax');
        $this->loader->add_action('wp_ajax_pexlat_form_update_field_required', $plugin_admin, 'update_field_required_ajax');
        $this->loader->add_action('wp_ajax_pexlat_form_update_field_settings', $plugin_admin, 'update_field_settings_ajax');

        // Add settings link on plugin page
        $plugin_basename = plugin_basename(plugin_dir_path(dirname(__FILE__)) . $this->plugin_name . '.php');
        $this->loader->add_filter('plugin_action_links_' . $plugin_basename, $plugin_admin, 'add_action_links');

        // تهيئة ميتا بوكس تعطيل النموذج
        new Form_Disable_Metabox();
    }

    /**
     * Register all of the hooks related to the public-facing functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_public_hooks() {

        // Public scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_public_styles'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_public_scripts'));

        // إضافة متغيرات CSS لطرق الشحن بعد تحميل أنماط CSS
        add_action('wp_enqueue_scripts', array($this, 'add_shipping_methods_css_variables'), 99);

        // Form handler
        $form_handler = new Pexlat_Form_Form_Handler($this->get_plugin_name(), $this->get_version());

        // Shortcodes for displaying forms
        add_shortcode('pexlat_form', array($form_handler, 'render_form_shortcode'));

        // Shortcode for displaying WooCommerce order details
        add_shortcode('woocommerce_order_details', array($this, 'render_order_details_shortcode'));

        // AJAX form submission
        $this->loader->add_action('wp_ajax_pexlat_form_submit', $form_handler, 'handle_form_submission');
        $this->loader->add_action('wp_ajax_nopriv_pexlat_form_submit', $form_handler, 'handle_form_submission');

        // AJAX add to cart (for product forms)
        $this->loader->add_action('wp_ajax_pexlat_form_add_to_cart', $form_handler, 'handle_add_to_cart');
        $this->loader->add_action('wp_ajax_nopriv_pexlat_form_add_to_cart', $form_handler, 'handle_add_to_cart');

        // AJAX save draft
        $this->loader->add_action('wp_ajax_pexlat_form_save_draft', $form_handler, 'handle_save_draft');
        $this->loader->add_action('wp_ajax_nopriv_pexlat_form_save_draft', $form_handler, 'handle_save_draft');

        // إضافة مهمة تنظيف الطلبات المتروكة
        $this->loader->add_action('wp', $this, 'schedule_abandoned_orders_cleanup');
        $this->loader->add_action('pexlat_form_cleanup_abandoned_orders', $this, 'cleanup_abandoned_orders');

        // AJAX get product variations
        $this->loader->add_action('wp_ajax_pexlat_form_get_variations', $form_handler, 'get_product_variations');
        $this->loader->add_action('wp_ajax_nopriv_pexlat_form_get_variations', $form_handler, 'get_product_variations');

        // AJAX refresh nonce (handled by admin class)
        $plugin_admin = new Pexlat_Form_Admin($this->get_plugin_name(), $this->get_version());
        $this->loader->add_action('wp_ajax_pexlat_form_refresh_nonce', $plugin_admin, 'refresh_nonce_ajax');
        $this->loader->add_action('wp_ajax_nopriv_pexlat_form_refresh_nonce', $plugin_admin, 'refresh_nonce_ajax');

        // معالجة صفحة الدفع لضمان تحديد طريقة الدفع الصحيحة
        $this->loader->add_action('wp', $form_handler, 'set_payment_method_for_order_pay');

        // تهيئة مدير طرق التوصيل
        if (is_admin()) {
            $shipping_manager = new Pexlat_Form_Shipping_Zones_Manager();
        }
    }

    /**
     * Register all of the hooks related to WooCommerce integration.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_woocommerce_hooks() {
        $woo_integration = new Woo_Integration($this->get_plugin_name(), $this->get_version());

        // إضافة النموذج تحت عنوان المنتج مباشرة
        $this->loader->add_action('woocommerce_single_product_summary', $woo_integration, 'add_form_after_product_title', 6);

        // Save form data with order
        $this->loader->add_action('woocommerce_checkout_create_order', $woo_integration, 'save_form_data_to_order', 10, 2);

        // Display form data in admin order view
        $this->loader->add_action('woocommerce_admin_order_data_after_billing_address', $woo_integration, 'display_form_data_in_admin_order', 10, 1);

        // Display form data in customer order emails
        $this->loader->add_action('woocommerce_email_after_order_table', $woo_integration, 'display_form_data_in_emails', 10, 4);

        // Display form data in order details on my account page
        $this->loader->add_action('woocommerce_order_details_after_order_table', $woo_integration, 'display_form_data_in_order_details', 10, 1);


    }

    /**
     * Add inline CSS variables for shipping methods styling.
     *
     * @since    1.0.0
     */
    public function add_shipping_methods_css_variables() {
        // فقط إذا تم تشغيل الصفحة
        if (!is_page() && !is_single() && !is_product()) {
            return;
        }

        // استرجاع إعدادات كل النماذج النشطة
        $forms = get_posts([
            'post_type' => 'pexlat_form',
            'post_status' => 'publish',
            'posts_per_page' => -1,
        ]);

        $css = '';

        foreach ($forms as $form) {
            $form_id = $form->ID;
            $settings = get_post_meta($form_id, '_form_settings', true);

            if (!$settings || !is_array($settings)) {
                continue;
            }

            // إنشاء CSS متغيرات لكل نموذج
            $form_css = ".pexlat-form-form[data-form-id=\"{$form_id}\"] {\n";

            // تنسيق طرق الشحن
            if (isset($settings['shipping_padding'])) {
                $form_css .= "  --shipping-methods-padding: {$settings['shipping_padding']}px;\n";
            }

            if (isset($settings['shipping_border_radius'])) {
                $form_css .= "  --shipping-methods-border-radius: {$settings['shipping_border_radius']}px;\n";
            }

            if (isset($settings['shipping_bg_color'])) {
                $form_css .= "  --shipping-methods-bg-color: {$settings['shipping_bg_color']};\n";
            }

            if (isset($settings['shipping_border_color'])) {
                $form_css .= "  --shipping-methods-border-color: {$settings['shipping_border_color']};\n";
            }

            if (isset($settings['shipping_border_width'])) {
                $form_css .= "  --shipping-methods-border-width: {$settings['shipping_border_width']}px;\n";
            }

            if (isset($settings['shipping_title_color'])) {
                $form_css .= "  --shipping-methods-title-color: {$settings['shipping_title_color']};\n";
            }

            if (isset($settings['shipping_price_color'])) {
                $form_css .= "  --shipping-methods-price-color: {$settings['shipping_price_color']};\n";
            }

            if (isset($settings['shipping_selected_bg_color'])) {
                $form_css .= "  --shipping-methods-selected-bg-color: {$settings['shipping_selected_bg_color']};\n";
            }

            if (isset($settings['shipping_selected_border_color'])) {
                $form_css .= "  --shipping-methods-selected-border-color: {$settings['shipping_selected_border_color']};\n";
            }

            if (isset($settings['shipping_mobile_columns'])) {
                $form_css .= "  --shipping-methods-mobile-columns: repeat({$settings['shipping_mobile_columns']}, 1fr);\n";
            }

            if (isset($settings['shipping_columns'])) {
                $form_css .= "  --shipping-methods-columns: repeat({$settings['shipping_columns']}, 1fr);\n";
            }

            $form_css .= "}\n";
            $css .= $form_css;
        }

        // إضافة CSS المتغيرات لـ head
        if (!empty($css)) {
            wp_add_inline_style($this->plugin_name, $css);
        }
    }

    /**
     * Enqueue public-facing stylesheets.
     *
     * @since    1.0.0
     */
    public function enqueue_public_styles() {
        // تحميل ملف CSS الأساسي
        wp_enqueue_style(
            $this->plugin_name,
            plugin_dir_url(dirname(__FILE__)) . 'public/css/pexlat-form-public.css',
            array(),
            $this->version,
            'all'
        );

        // تحميل ملف CSS الجديد بتصميم مشابه لـ custom-order-form
        wp_enqueue_style(
            $this->plugin_name . '-style',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/pexlat-form-style.css',
            array($this->plugin_name),
            $this->version,
            'all'
        );

        // تحميل ملف CSS الخاص بتحديثات التخطيط
        wp_enqueue_style(
            $this->plugin_name . '-layout',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/form-layout-updates.css',
            array($this->plugin_name, $this->plugin_name . '-style'),
            $this->version,
            'all'
        );

        // تحميل ملف CSS الخاص بتوحيد ارتفاع الحقول
        wp_enqueue_style(
            $this->plugin_name . '-input-consistency',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/form-input-consistency.css',
            array($this->plugin_name, $this->plugin_name . '-layout'),
            $this->version,
            'all'
        );

        // تحميل ملف CSS الخاص بترتيب عناصر النموذج
        wp_enqueue_style(
            $this->plugin_name . '-elements-order',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/form-elements-order.css',
            array($this->plugin_name, $this->plugin_name . '-layout', $this->plugin_name . '-input-consistency'),
            $this->version,
            'all'
        );

        // تحميل ملف CSS الخاص بزر تبديل ملخص الطلب
        wp_enqueue_style(
            $this->plugin_name . '-summary-toggle',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/summary-toggle.css',
            array($this->plugin_name, $this->plugin_name . '-elements-order'),
            $this->version,
            'all'
        );

        // تحميل ملف CSS الخاص بمتغيرات المنتج
        wp_enqueue_style(
            $this->plugin_name . '-variation-styles',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/variation-styles.css',
            array($this->plugin_name, $this->plugin_name . '-summary-toggle'),
            $this->version,
            'all'
        );

        // تحميل ملف CSS الخاص بأنماط المتغيرات الجديدة
        wp_enqueue_style(
            $this->plugin_name . '-variation-styles',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/variation-styles.css',
            array($this->plugin_name, $this->plugin_name . '-product-variations'),
            $this->version,
            'all'
        );

        // تحميل ملف CSS الخاص بالتوافق مع القوالب المختلفة
        wp_enqueue_style(
            $this->plugin_name . '-theme-compatibility',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/theme-compatibility.css',
            array($this->plugin_name, $this->plugin_name . '-product-variations'),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );

        // تحميل ملف CSS لحل مشكلة تداخل عناصر ووكومرس
        wp_enqueue_style(
            $this->plugin_name . '-woocommerce-conflict-fix',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/woocommerce-conflict-fix.css',
            array($this->plugin_name, $this->plugin_name . '-theme-compatibility'),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );

        // تحميل ملف CSS الخاص بأنماط الأسعار
        wp_enqueue_style(
            $this->plugin_name . '-price-styles',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/price-styles.css',
            array($this->plugin_name, $this->plugin_name . '-variation-styles', $this->plugin_name . '-theme-compatibility'),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );

        // تحميل ملف CSS الخاص بتقييد الطلبات
        wp_enqueue_style(
            $this->plugin_name . '-order-limit',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/order-limit.css',
            array($this->plugin_name, $this->plugin_name . '-theme-compatibility'),
            $this->version,
            'all'
        );

        // تحميل ملف CSS لإصلاح مشكلة ظهور واختفاء الحدود
        wp_enqueue_style(
            $this->plugin_name . '-border-fix',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/form-border-fix.css',
            array($this->plugin_name, $this->plugin_name . '-theme-compatibility'),
            $this->version,
            'all'
        );



        // تحميل ملف CSS الخاص باتجاه النموذج حسب اللغة
        wp_enqueue_style(
            $this->plugin_name . '-language-direction',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/form-language-direction.css',
            array($this->plugin_name, $this->plugin_name . '-offers-styles'),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );

        // تحميل ملف CSS الخاص بخط Rubik Arabic
        wp_enqueue_style(
            $this->plugin_name . '-rubik-arabic-font',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/rubik-arabic-font.css',
            array($this->plugin_name, $this->plugin_name . '-language-direction'),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );

        // تحميل ملف CSS الخاص بأنماط الشورت كود
        wp_enqueue_style(
            $this->plugin_name . '-shortcode-styles',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/shortcode-styles.css',
            array($this->plugin_name, $this->plugin_name . '-rubik-arabic-font'),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );

        // تحميل ملف CSS الخاص بإصلاحات نظام الألوان الموحد
        wp_enqueue_style(
            $this->plugin_name . '-unified-theme-fixes',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/unified-theme-fixes.css',
            array($this->plugin_name, $this->plugin_name . '-shortcode-styles'),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );



        // تحميل ملف CSS الخاص بتحسينات التصميم
        wp_enqueue_style(
            $this->plugin_name . '-design-improvements',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/form-design-improvements.css',
            array($this->plugin_name, $this->plugin_name . '-unified-theme-fixes'),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );

        // تحميل ملف CSS الخاص بالإصلاح النهائي للوضع المظلم
        wp_enqueue_style(
            $this->plugin_name . '-dark-mode-ultimate-fix',
            plugin_dir_url(dirname(__FILE__)) . 'public/css/dark-mode-ultimate-fix.css',
            array($this->plugin_name, $this->plugin_name . '-design-improvements'),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );



        // تحميل Font Awesome من CDN للأيقونات
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css',
            array(),
            '5.15.4',
            'all'
        );
    }

    /**
     * Enqueue public-facing scripts.
     *
     * @since    1.0.0
     */
    public function enqueue_public_scripts() {
        // تحميل ملف JavaScript الرئيسي للإضافة
        wp_enqueue_script(
            $this->plugin_name,
            plugin_dir_url(dirname(__FILE__)) . 'public/js/pexlat-form-public.js',
            array('jquery'),
            $this->version,
            true
        );

        // تحميل ملف JavaScript الخاص بمتغيرات المنتج
        wp_enqueue_script(
            $this->plugin_name . '-product-variations',
            plugin_dir_url(dirname(__FILE__)) . 'public/js/product-variations.js',
            array('jquery', $this->plugin_name),
            $this->version,
            true
        );

        // تحميل ملف JavaScript الخاص بإصلاح موضع النموذج
        wp_enqueue_script(
            $this->plugin_name . '-form-position-fix',
            plugin_dir_url(dirname(__FILE__)) . 'public/js/form-position-fix.js',
            array('jquery', $this->plugin_name),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            true
        );

        // تحميل ملف JavaScript الخاص بتحسينات الشورت كود
        wp_enqueue_script(
            $this->plugin_name . '-shortcode-enhancements',
            plugin_dir_url(dirname(__FILE__)) . 'public/js/shortcode-enhancements.js',
            array('jquery', $this->plugin_name),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            true
        );

        // تحميل ملف JavaScript الخاص بإصلاح الوضع المظلم
        wp_enqueue_script(
            $this->plugin_name . '-dark-mode-fix',
            plugin_dir_url(dirname(__FILE__)) . 'public/js/dark-mode-fix.js',
            array('jquery', $this->plugin_name),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            true
        );

        // تم إزالة ملف JavaScript الخاص بالعروض

        // الحصول على إعدادات الشريط المثبت من نموذج افتراضي
        $default_form_id = get_option('pexlat_form_default_form_id');

        // تحقق من الحصول على معرف النموذج
        if (empty($default_form_id)) {
            // البحث عن النموذج الافتراضي من قاعدة البيانات مباشرة إذا لم يتم تعيين الخيار
            global $wpdb;
            $default_form_id = $wpdb->get_var("SELECT id FROM {$wpdb->prefix}pexlat_form_forms ORDER BY id ASC LIMIT 1");
        }

        // الحصول على إعدادات النموذج من قاعدة البيانات
        $form_settings = array();
        if (!empty($default_form_id)) {
            global $wpdb;
            $form_table = $wpdb->prefix . 'pexlat_form_forms';
            $db_form = $wpdb->get_row($wpdb->prepare("SELECT settings FROM {$form_table} WHERE id = %d", $default_form_id));

            if ($db_form && !empty($db_form->settings)) {
                // استخدام الدالة المساعدة لفك تشفير البيانات
                if (class_exists('Pexlat_Form_Helper')) {
                    $form_settings = Pexlat_Form_Helper::unserialize_data($db_form->settings);
                } else {
                    // استخدام unserialize مباشرة إذا لم تكن الدالة المساعدة متاحة
                    $form_settings = maybe_unserialize($db_form->settings);
                }
            }
        }

        // استيراد فئة الترجمة
        require_once plugin_dir_path(__FILE__) . 'class-pexlat-form-i18n.php';

        // الحصول على اللغة الحالية
        $current_language = Pexlat_Form_i18n::get_current_language();

        // إعداد ترجمات JavaScript
        $translations = array();

        // إضافة الترجمات حسب اللغة الحالية
        if ($current_language === 'fr') {
            $translations = array(
                'field_required' => 'Ce champ est obligatoire',
                'invalid_email' => 'L\'adresse e-mail n\'est pas valide',
                'invalid_phone' => 'Le numéro de téléphone n\'est pas valide',
                'free_shipping' => 'Livraison gratuite',
                'choose_state' => 'Choisir la wilaya',
                'choose_municipality' => 'Choisir la commune',
                'please_choose_state' => 'Veuillez choisir la wilaya pour afficher les méthodes de livraison disponibles',
                'quantity' => 'Quantité',
                'per_piece' => 'par pièce',
                'product_price' => 'Prix du produit',
                'shipping_price' => 'Frais de livraison',
                'total_price' => 'Prix total',
                'order_summary' => 'Résumé de la commande',
                'order_now' => 'Commander maintenant',
                'order_via_whatsapp' => 'Commander via WhatsApp',
                'product_not_available' => 'Le produit n\'est pas disponible actuellement',
                'please_fill_required_fields' => 'Veuillez remplir tous les champs obligatoires',
                'added_to_cart' => 'Le produit a été ajouté au panier avec succès',
                'error_adding_to_cart' => 'Une erreur s\'est produite lors de l\'ajout du produit au panier',
                'loading_shipping_methods' => 'Chargement des méthodes de livraison...',
                'standard_shipping' => 'Livraison standard',
                'free' => 'Gratuit',
                'متوفر في المخزون' => 'Disponible en stock',
                'غير متوفر في المخزون' => 'Non disponible en stock',
                'تم إرسال طلبك بنجاح.' => 'Votre commande a été envoyée avec succès.',
                'عذراً، يمكنك إرسال طلب جديد بعد' => 'Désolé, vous pouvez soumettre une nouvelle commande après',
                'عذراً، لقد وصلت للحد الأقصى من الطلبات المسموح بها' => 'Désolé, vous avez atteint le nombre maximum de commandes autorisées',
                'اختر الولاية' => 'Choisir la wilaya'
            );
        } elseif ($current_language === 'en') {
            $translations = array(
                'field_required' => 'This field is required',
                'invalid_email' => 'Invalid email address',
                'invalid_phone' => 'Invalid phone number',
                'free_shipping' => 'Free shipping',
                'choose_state' => 'Choose state',
                'choose_municipality' => 'Choose municipality',
                'please_choose_state' => 'Please choose a state to display available shipping methods',
                'quantity' => 'Quantity',
                'per_piece' => 'per piece',
                'product_price' => 'Product price',
                'shipping_price' => 'Shipping price',
                'total_price' => 'Total price',
                'order_summary' => 'Order summary',
                'order_now' => 'Order now',
                'order_via_whatsapp' => 'Order via WhatsApp',
                'product_not_available' => 'Product is currently unavailable',
                'please_fill_required_fields' => 'Please fill in all required fields',
                'added_to_cart' => 'Product has been successfully added to cart',
                'error_adding_to_cart' => 'An error occurred while adding the product to cart',
                'loading_shipping_methods' => 'Loading shipping methods...',
                'standard_shipping' => 'Standard shipping',
                'free' => 'Free',
                'متوفر في المخزون' => 'In stock',
                'غير متوفر في المخزون' => 'Out of stock',
                'تم إرسال طلبك بنجاح.' => 'Your order has been sent successfully.',
                'عذراً، يمكنك إرسال طلب جديد بعد' => 'Sorry, you can submit a new order after',
                'عذراً، لقد وصلت للحد الأقصى من الطلبات المسموح بها' => 'Sorry, you have reached the maximum number of allowed orders',
                'اختر الولاية' => 'Choose State',
                'اختر الولاية الخاصة بك' => 'Choose your state',
                'اختر البلدية' => 'Choose Municipality',
                'اختر البلدية الخاصة بك' => 'Choose your municipality',
                'العنوان التفصيلي لتوصيل الطلب' => 'Detailed address for order delivery',
                'أدخل العنوان التفصيلي' => 'Enter detailed address',
                'ملخص الطلب' => 'Order Summary',
                'إتمام الطلب' => 'Complete Order',
                'اطلب الآن' => 'Order Now'
            );
        } elseif ($current_language === 'es') {
            $translations = array(
                'field_required' => 'Este campo es obligatorio',
                'invalid_email' => 'Dirección de correo electrónico inválida',
                'invalid_phone' => 'Número de teléfono inválido',
                'free_shipping' => 'Envío gratis',
                'choose_state' => 'Elegir estado',
                'choose_municipality' => 'Elegir municipio',
                'please_choose_state' => 'Por favor, elija un estado para mostrar los métodos de envío disponibles',
                'quantity' => 'Cantidad',
                'per_piece' => 'por pieza',
                'product_price' => 'Precio del producto',
                'shipping_price' => 'Precio de envío',
                'total_price' => 'Precio total',
                'order_summary' => 'Resumen del pedido',
                'order_now' => 'Ordenar ahora',
                'order_via_whatsapp' => 'Ordenar vía WhatsApp',
                'product_not_available' => 'El producto no está disponible actualmente',
                'please_fill_required_fields' => 'Por favor, complete todos los campos requeridos',
                'added_to_cart' => 'El producto se ha añadido al carrito exitosamente',
                'error_adding_to_cart' => 'Ocurrió un error al añadir el producto al carrito',
                'loading_shipping_methods' => 'Cargando métodos de envío...',
                'standard_shipping' => 'Envío estándar',
                'free' => 'Gratis',
                'متوفر في المخزون' => 'En stock',
                'غير متوفر في المخزون' => 'Agotado',
                'تم إرسال طلبك بنجاح.' => 'Su pedido ha sido enviado exitosamente.',
                'عذراً، يمكنك إرسال طلب جديد بعد' => 'Lo siento, puedes enviar un nuevo pedido después de',
                'عذراً، لقد وصلت للحد الأقصى من الطلبات المسموح بها' => 'Lo siento, has alcanzado el número máximo de pedidos permitidos',
                'اختر الولاية' => 'Elegir Estado',
                'اختر الولاية الخاصة بك' => 'Elige tu estado',
                'اختر البلدية' => 'Elegir Municipio',
                'اختر البلدية الخاصة بك' => 'Elige tu municipio',
                'العنوان التفصيلي لتوصيل الطلب' => 'Dirección detallada para entrega del pedido',
                'أدخل العنوان التفصيلي' => 'Ingresa dirección detallada',
                'ملخص الطلب' => 'Resumen del Pedido',
                'إتمام الطلب' => 'Completar Pedido',
                'اطلب الآن' => 'Ordenar Ahora'
            );
        }

        // إعداد البيانات المشتركة لجميع ملفات JavaScript
        $script_data = array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('pexlat_form_nonce'),

            // إعدادات الشريط العلوي
            'sticky_bar_show_product' => $form_settings['sticky_bar_show_product'] ?? 'yes',
            'sticky_bar_button_text' => $form_settings['sticky_bar_button_text'] ?? ($current_language === 'fr' ? 'Commander maintenant' : 'اطلب الآن'),
            'sticky_bar_button_icon' => $form_settings['sticky_bar_button_icon'] ?? 'check',
            'sticky_bar_button_gradient' => $form_settings['sticky_bar_button_gradient'] ?? 'no',
            'sticky_bar_button_gradient_direction' => $form_settings['sticky_bar_button_gradient_direction'] ?? 'to bottom',
            'sticky_bar_button_color' => $form_settings['sticky_bar_button_color'] ?? '#FF5722',
            'sticky_bar_button_text_color' => $form_settings['sticky_bar_button_text_color'] ?? '#FFFFFF',
            'sticky_bar_button_gradient_color' => $form_settings['sticky_bar_button_gradient_color'] ?? '#F44336',
            'sticky_bar_button_border_radius' => $form_settings['sticky_bar_button_border_radius'] ?? '5',
            'sticky_bar_button_animation' => $form_settings['sticky_bar_button_animation'] ?? 'none',

            // إعدادات طرق الشحن الافتراضية - استخدام استدعاء قاعدة البيانات مباشرة للتأكد من آخر الإعدادات
            'default_shipping_1_title' => $current_language === 'fr' ? 'Livraison à domicile' : ($this->get_fresh_form_setting('default_shipping_1_title', $default_form_id) ?? 'توصيل للمنزل'),
            'default_shipping_1_cost' => intval($this->get_fresh_form_setting('default_shipping_1_cost', $default_form_id) ?? 800),
            'default_shipping_1_description' => $this->get_fresh_form_setting('default_shipping_1_description', $default_form_id) ?? '',
            'default_shipping_1_enabled' => intval($this->get_fresh_form_setting('default_shipping_1_enabled', $default_form_id) ?? 1),

            // إعداد طريقة عرض طرق التوصيل
            'shipping_display_mode' => $this->get_fresh_form_setting('shipping_display_mode', $default_form_id) ?? 'detailed',
            'default_shipping_2_title' => $current_language === 'fr' ? 'Livraison au bureau' : ($this->get_fresh_form_setting('default_shipping_2_title', $default_form_id) ?? 'توصيل للمكتب'),
            'default_shipping_2_cost' => intval($this->get_fresh_form_setting('default_shipping_2_cost', $default_form_id) ?? 600),
            'default_shipping_2_description' => $this->get_fresh_form_setting('default_shipping_2_description', $default_form_id) ?? '',
            'default_shipping_2_enabled' => intval($this->get_fresh_form_setting('default_shipping_2_enabled', $default_form_id) ?? 1),

            // تسجيل معلومات التصحيح لإعدادات طرق الشحن
            'debug_info' => 'تم تحديث إعدادات طرق الشحن في ' . date('Y-m-d H:i:s'),

            // إعدادات الحماية الجديدة
            'phone_validation_enabled' => get_option('pexlat_form_phone_validation_enabled', '1'),
            'phone_prefixes' => get_option('pexlat_form_phone_prefixes', '05,06,07'),
            'phone_length' => get_option('pexlat_form_phone_length', '10'),
            'custom_phone_validation' => get_option('pexlat_form_custom_phone_validation', '0'),
            'limit_orders_enabled' => get_option('pexlat_form_limit_orders_enabled', '1'),
            'max_orders' => get_option('pexlat_form_max_orders', '3'),
            'time_period' => get_option('pexlat_form_time_period', '24'),
            'disable_autocomplete' => get_option('pexlat_form_disable_autocomplete', '0'),
            'disable_copy_paste' => get_option('pexlat_form_disable_copy_paste', '0'),

            // إضافة الترجمات
            'translations' => $translations,
            'current_language' => $current_language,

            // إضافة معلومات العملة من WooCommerce
            'currency_symbol' => function_exists('get_woocommerce_currency_symbol') ? get_woocommerce_currency_symbol() : 'د.ج',
            'currency_code' => function_exists('get_woocommerce_currency') ? get_woocommerce_currency() : 'DZD',
            'currency_position' => function_exists('get_option') ? get_option('woocommerce_currency_pos', 'left') : 'left',
            'price_decimals' => function_exists('wc_get_price_decimals') ? wc_get_price_decimals() : 2,
            'price_decimal_separator' => function_exists('wc_get_price_decimal_separator') ? wc_get_price_decimal_separator() : '.',
            'price_thousand_separator' => function_exists('wc_get_price_thousand_separator') ? wc_get_price_thousand_separator() : ','
        );

        // تمرير البيانات لملفات JavaScript
        $scripts = [$this->plugin_name, $this->plugin_name . '-product-variations'];
        foreach ($scripts as $script) {
            wp_localize_script($script, 'formElrakami', $script_data);
        }


    }

    /**
     * Run the loader to execute all of the hooks with WordPress.
     *
     * @since    1.0.0
     */
    public function run() {
        $this->loader->run();
    }

    /**
     * جدولة مهمة تنظيف الطلبات المتروكة
     */
    public function schedule_abandoned_orders_cleanup() {
        if (!wp_next_scheduled('pexlat_form_cleanup_abandoned_orders')) {
            wp_schedule_event(time(), 'hourly', 'pexlat_form_cleanup_abandoned_orders');
        }
    }

    /**
     * تنظيف الطلبات المتروكة القديمة
     */
    public function cleanup_abandoned_orders() {
        // التحقق من تفعيل النظام
        if (get_option('pexlat_form_save_abandoned_orders', 1) != 1) {
            return;
        }

        global $wpdb;

        // الحصول على مدة الاحتفاظ بالطلبات المتروكة
        $cleanup_hours = intval(get_option('pexlat_form_abandoned_orders_cleanup_time', 24));
        $cleanup_hours = max(1, min(168, $cleanup_hours)); // بين ساعة واحدة وأسبوع

        // البحث عن الطلبات المتروكة القديمة
        $old_orders = $wpdb->get_col($wpdb->prepare("
            SELECT ID
            FROM {$wpdb->posts}
            WHERE post_type = 'shop_order'
            AND post_status = 'wc-draft'
            AND post_date < DATE_SUB(NOW(), INTERVAL %d HOUR)
        ", $cleanup_hours));

        $cleaned_count = 0;

        foreach ($old_orders as $order_id) {
            $order = wc_get_order($order_id);
            if ($order && $order->get_status() === 'draft') {
                // التحقق من أن هذا طلب متروك من Pexlat Form
                $is_pexlat_order = $order->get_meta('_pexlat_form_draft_data') ||
                                  $order->get_meta('_pexlat_form_form_id');

                if ($is_pexlat_order) {
                    // حذف الطلب نهائياً
                    wp_delete_post($order_id, true);
                    $cleaned_count++;

                    // حذف خيارات المسودة المرتبطة
                    $phone = $order->get_meta('_customer_phone');
                    if ($phone) {
                        $draft_key = 'pexlat_form_draft_' . md5($phone);
                        delete_option($draft_key);
                    }
                }
            }
        }

        // تسجيل عملية التنظيف
        if ($cleaned_count > 0) {
            error_log(sprintf('Pexlat Form: تم تنظيف %d طلب متروك قديم', $cleaned_count));
        }
    }

    /**
     * The name of the plugin used to uniquely identify it within the context of
     * WordPress and to define internationalization functionality.
     *
     * @since     1.0.0
     * @return    string    The name of the plugin.
     */
    public function get_plugin_name() {
        return $this->plugin_name;
    }

    /**
     * The reference to the class that orchestrates the hooks with the plugin.
     *
     * @since     1.0.0
     * @return    Pexlat_Form_Loader    Orchestrates the hooks of the plugin.
     */
    public function get_loader() {
        return $this->loader;
    }

    /**
     * Retrieve the version number of the plugin.
     *
     * @since     1.0.0
     * @return    string    The version number of the plugin.
     */
    public function get_version() {
        return $this->version;
    }

    /**
     * الحصول على إعدادات محدثة مباشرة من قاعدة البيانات
     * هذه الدالة تستخدم لضمان الحصول على أحدث الإعدادات من قاعدة البيانات مباشرة
     *
     * @since    1.0.0
     * @param    string    $setting_name    اسم الإعداد المطلوب استرجاعه
     * @param    int       $form_id         معرف النموذج
     * @return   mixed                      قيمة الإعداد أو null إذا لم يتم العثور عليه
     */
    private function get_fresh_form_setting($setting_name, $form_id) {
        if (empty($form_id) || $form_id <= 0) {
            return null;
        }

        global $wpdb;
        $form_table = $wpdb->prefix . 'pexlat_form_forms';

        // استخدام استعلام مباشر للحصول على أحدث البيانات وتجنب التخزين المؤقت
        $db_form = $wpdb->get_row($wpdb->prepare("SELECT settings FROM {$form_table} WHERE id = %d", $form_id));

        if (!$db_form || empty($db_form->settings)) {
            return null;
        }

        // استخدام الدالة المساعدة لفك تشفير البيانات
        if (class_exists('Pexlat_Form_Helper')) {
            $settings = Pexlat_Form_Helper::unserialize_data($db_form->settings);
        } else {
            // استخدام unserialize مباشرة إذا لم تكن الدالة المساعدة متاحة
            $settings = maybe_unserialize($db_form->settings);
        }

        // التحقق من وجود الإعداد المطلوب
        if (is_array($settings) && isset($settings[$setting_name])) {
            return $settings[$setting_name];
        }

        // البحث في form_settings إذا لم يتم العثور على الإعداد في settings
        if (is_array($settings) && isset($settings['form_settings']) && is_array($settings['form_settings']) && isset($settings['form_settings'][$setting_name])) {
            return $settings['form_settings'][$setting_name];
        }

        return null;
    }

    /**
     * Render WooCommerce order details shortcode
     *
     * @since    1.0.0
     * @param    array    $atts      Shortcode attributes.
     * @return   string              HTML output.
     */
    public function render_order_details_shortcode($atts) {
        // Extract and set default attributes
        $atts = shortcode_atts(array(
            'order_id' => 0,
        ), $atts, 'woocommerce_order_details');

        // Start output buffer
        ob_start();

        // Get order ID from query parameters if not provided in shortcode
        $order_id = (int) $atts['order_id'];

        if ($order_id <= 0 && isset($_GET['order_id'])) {
            $order_id = (int) $_GET['order_id'];
        }

        // Check if we have a valid order ID
        if ($order_id <= 0) {
            echo '<p>لم يتم تحديد رقم الطلب.</p>';
            return ob_get_clean();
        }

        // Check if order key is provided
        $order_key = isset($_GET['key']) ? sanitize_text_field($_GET['key']) : '';

        // Get order
        $order = wc_get_order($order_id);

        // Verify order exists and order key matches
        if (!$order || $order->get_order_key() !== $order_key) {
            echo '<p>عذراً، لا يمكن العثور على تفاصيل هذا الطلب.</p>';
            return ob_get_clean();
        }

        // Display standard WooCommerce order received content
        echo '<div class="woocommerce">';

        // Show thank you message
        wc_get_template('checkout/thankyou.php', array('order' => $order));

        echo '</div>';

        // Return the buffered content
        return ob_get_clean();
    }
}