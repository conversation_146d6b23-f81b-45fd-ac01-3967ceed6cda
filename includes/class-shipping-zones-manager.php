<?php
/**
 * مدير مناطق الشحن
 *
 * @package Pexlat_Form
 * @subpackage Shipping_Zones_Manager
 */

defined('ABSPATH') || exit;

class Pexlat_Form_Shipping_Zones_Manager {

    private $shipping_data = [];
    private $companies = [];

    public function __construct() {
        // تسجيل AJAX actions
        add_action('wp_ajax_pexlat_form_import_shipping_zones', array($this, 'import_shipping_zones'));
        add_action('wp_ajax_pexlat_form_upload_pricing_file', array($this, 'upload_pricing_file'));
        add_action('wp_ajax_pexlat_form_add_shipping_company', array($this, 'add_shipping_company'));
        add_action('wp_ajax_pexlat_form_toggle_shipping_company', array($this, 'toggle_shipping_company'));
        add_action('wp_ajax_pexlat_form_delete_shipping_company', array($this, 'delete_shipping_company'));
        add_action('wp_ajax_pexlat_form_get_pricing_file_content', array($this, 'get_pricing_file_content'));
        add_action('wp_ajax_pexlat_form_save_pricing_file_content', array($this, 'save_pricing_file_content'));
        add_action('wp_ajax_pexlat_form_save_shipping_class_costs', array($this, 'save_shipping_class_costs'));

        // تسجيل الولايات الجزائرية
        add_filter('woocommerce_states', array($this, 'add_dz_states'));

        // تحميل بيانات الشركات المخزنة
        $this->load_saved_companies();
    }

    /**
     * تحميل بيانات الشركات المحفوظة
     */
    private function load_saved_companies() {
        // الشركات الافتراضية
        $default_companies = [
            'zr_express' => [
                'name' => 'ZR Express',
                'file' => 'shopping-pricing.json',
                'enabled' => true
            ],
            'yalidine' => [
                'name' => 'Yalidine',
                'file' => 'yalidine-shopping-pricing.json',
                'enabled' => true
            ]
        ];

        // الحصول على الشركات المحفوظة
        $saved_companies = get_option('pexlat_form_shipping_companies_data', []);

        // الحصول على قائمة الشركات المحذوفة
        $deleted_companies = get_option('pexlat_form_deleted_companies', []);

        // إضافة الشركات الافتراضية فقط إذا لم يتم حذفها
        foreach ($default_companies as $company_id => $company) {
            if (!in_array($company_id, $deleted_companies)) {
                $this->companies[$company_id] = $company;
            }
        }

        // إضافة الشركات المحفوظة
        if (!empty($saved_companies)) {
            foreach ($saved_companies as $company_id => $company) {
                if (!isset($company['enabled'])) {
                    $saved_companies[$company_id]['enabled'] = true;
                }
                // إضافة الشركة فقط إذا لم يتم حذفها
                if (!in_array($company_id, $deleted_companies)) {
                    $this->companies[$company_id] = $company;
                }
            }
        }
    }

    /**
     * تحميل ملف تسعير شركة معينة
     */
    private function load_pricing_file($company_id) {
        $company = isset($this->companies[$company_id]) ? $this->companies[$company_id] : null;
        if (!$company) {
            return null;
        }

        $file_path = PEXLAT_FORM_PLUGIN_DIR . 'shipping-data/' . $company['file'];

        if (file_exists($file_path)) {
            $content = file_get_contents($file_path);
            if ($content !== false) {
                $data = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                    // إضافة معرف الشركة واسمها للبيانات
                    foreach ($data as &$zone) {
                        $zone['company_id'] = $company_id;
                        $zone['company_name'] = $company['name'];
                    }
                    return $data;
                }
            }
        }
        return null;
    }

    /**
     * تحميل جميع ملفات التسعير للشركات المفعلة
     */
    private function load_all_pricing_files() {
        $all_data = [];
        $enabled_companies = 0;
        $loaded_companies = 0;

        foreach ($this->companies as $company_id => $company) {
            if ($company['enabled']) {
                $enabled_companies++;
                $company_data = $this->load_pricing_file($company_id);
                if ($company_data) {
                    $loaded_companies++;
                    foreach ($company_data as $item) {
                        // التأكد من وجود جميع الحقول المطلوبة
                        if (!isset($item['IDWilaya']) || !isset($item['Wilaya']) ||
                            !isset($item['Domicile']) || !isset($item['Stopdesk'])) {
                            continue;
                        }
                        $all_data[] = array_merge($item, [
                            'company_id' => $company_id,
                            'company_name' => $company['name']
                        ]);
                    }
                }
            }
        }

        if ($enabled_companies === 0) {
            throw new Exception('لا توجد شركات شحن مفعلة');
        }

        if ($loaded_companies === 0) {
            throw new Exception('لم يتم العثور على ملفات تسعير للشركات المفعلة');
        }

        if (empty($all_data)) {
            throw new Exception('لم يتم العثور على بيانات صالحة في ملفات التسعير');
        }

        return $all_data;
    }

    /**
     * إزالة طرق الشحن الخاصة بشركة معينة
     */
    private function remove_company_shipping_methods($company_id) {
        if (!class_exists('WC_Shipping_Zones')) {
            return;
        }

        $company_name = $this->companies[$company_id]['name'];
        $shipping_zones = WC_Shipping_Zones::get_zones();

        foreach ($shipping_zones as $zone_data) {
            $zone = new WC_Shipping_Zone($zone_data['id']);
            $shipping_methods = $zone->get_shipping_methods();

            foreach ($shipping_methods as $method) {
                $title = $method->get_title();
                // حذف طرق الشحن التي تبدأ باسم الشركة
                if (strpos($title, $company_name) === 0) {
                    $zone->delete_shipping_method($method->get_instance_id());
                }
            }
        }
    }

    /**
     * تفعيل/تعطيل شركة شحن
     */
    public function toggle_shipping_company() {
        check_ajax_referer('pexlat_form_nonce', '_ajax_nonce');

        try {
            if (!isset($_POST['company_id'])) {
                throw new Exception('معرف الشركة غير موجود');
            }

            $company_id = sanitize_key($_POST['company_id']);
            if (!isset($this->companies[$company_id])) {
                throw new Exception('الشركة غير موجودة');
            }

            $saved_companies = get_option('pexlat_form_shipping_companies_data', []);
            if (!isset($saved_companies[$company_id])) {
                $saved_companies[$company_id] = $this->companies[$company_id];
            }

            $new_status = !$saved_companies[$company_id]['enabled'];
            $saved_companies[$company_id]['enabled'] = $new_status;

            // إذا تم تعطيل الشركة، نقوم بحذف طرق الشحن الخاصة بها
            if (!$new_status) {
                $this->remove_company_shipping_methods($company_id);
            }

            update_option('pexlat_form_shipping_companies_data', $saved_companies);

            wp_send_json_success([
                'message' => $new_status ? 'تم تفعيل الشركة' : 'تم تعطيل الشركة وحذف طرق الشحن الخاصة بها',
                'enabled' => $new_status
            ]);

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * حذف شركة شحن
     */
    public function delete_shipping_company() {
        check_ajax_referer('pexlat_form_nonce', '_ajax_nonce');

        try {
            if (!isset($_POST['company_id'])) {
                throw new Exception('معرف الشركة غير موجود');
            }

            $company_id = sanitize_key($_POST['company_id']);
            if (!isset($this->companies[$company_id])) {
                throw new Exception('الشركة غير موجودة');
            }

            $company_name = $this->companies[$company_id]['name'];

            // حذف طرق الشحن الخاصة بالشركة أولاً
            $this->remove_company_shipping_methods($company_id);

            // حذف ملف التسعير إذا كان موجوداً
            $file_path = PEXLAT_FORM_PLUGIN_DIR . 'shipping-data/' . $this->companies[$company_id]['file'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }

            // إضافة الشركة إلى قائمة الشركات المحذوفة
            $deleted_companies = get_option('pexlat_form_deleted_companies', []);
            if (!in_array($company_id, $deleted_companies)) {
                $deleted_companies[] = $company_id;
                update_option('pexlat_form_deleted_companies', $deleted_companies);
            }

            // حذف الشركة من قائمة الشركات المحفوظة
            $saved_companies = get_option('pexlat_form_shipping_companies_data', []);
            unset($saved_companies[$company_id]);
            update_option('pexlat_form_shipping_companies_data', $saved_companies);

            // حذف الشركة من المصفوفة المحلية
            unset($this->companies[$company_id]);

            wp_send_json_success(sprintf(
                'تم حذف شركة %s وإزالة جميع طرق الشحن الخاصة بها',
                $company_name
            ));

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * إضافة شركة شحن جديدة
     */
    public function add_shipping_company() {
        check_ajax_referer('pexlat_form_nonce', '_ajax_nonce');

        try {
            if (!isset($_POST['company_id']) || !isset($_POST['company_name'])) {
                throw new Exception('بيانات الشركة غير مكتملة');
            }

            $company_id = sanitize_key($_POST['company_id']);
            $company_name = sanitize_text_field($_POST['company_name']);

            if (isset($this->companies[$company_id])) {
                throw new Exception('معرف الشركة مستخدم بالفعل');
            }

            $this->companies[$company_id] = [
                'name' => $company_name,
                'file' => $company_id . '_pricing.json',
                'enabled' => true
            ];

            $saved_companies = get_option('pexlat_form_shipping_companies_data', []);
            $saved_companies[$company_id] = $this->companies[$company_id];
            update_option('pexlat_form_shipping_companies_data', $saved_companies);

            wp_send_json_success('تمت إضافة الشركة بنجاح');

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * رفع ملف تسعير
     */
    public function upload_pricing_file() {
        check_ajax_referer('pexlat_form_nonce', '_ajax_nonce');

        try {
            if (!isset($_FILES['pricing_file']) || !isset($_POST['company_id'])) {
                throw new Exception('البيانات غير مكتملة');
            }

            $company_id = sanitize_key($_POST['company_id']);
            if (!isset($this->companies[$company_id])) {
                throw new Exception('الشركة غير موجودة');
            }

            $file = $_FILES['pricing_file'];
            if ($file['error']) {
                throw new Exception('حدث خطأ أثناء رفع الملف: ' . $file['error']);
            }

            // التحقق من صحة الملف
            $content = file_get_contents($file['tmp_name']);
            if ($content === false) {
                throw new Exception('فشل في قراءة محتوى الملف');
            }

            $data = json_decode($content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('الملف غير صالح: ' . json_last_error_msg());
            }

            // إنشاء مجلد shipping-data إذا لم يكن موجوداً
            $shipping_dir = PEXLAT_FORM_PLUGIN_DIR . 'shipping-data/';
            if (!file_exists($shipping_dir)) {
                wp_mkdir_p($shipping_dir);
            }

            // حفظ الملف في مجلد الإضافة
            $target_file = $shipping_dir . $this->companies[$company_id]['file'];
            if (!move_uploaded_file($file['tmp_name'], $target_file)) {
                throw new Exception('فشل في حفظ الملف');
            }

            wp_send_json_success('تم رفع ملف أسعار ' . $this->companies[$company_id]['name'] . ' بنجاح');

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * استيراد مناطق الشحن
     */
    public function import_shipping_zones() {
        check_ajax_referer('pexlat_form_nonce', '_ajax_nonce');

        try {
            if (!class_exists('WC_Shipping_Zone')) {
                throw new Exception('WooCommerce غير مثبت');
            }

            // تفعيل الجزائر كدولة للشحن
            update_option('woocommerce_allowed_countries', 'specific');
            update_option('woocommerce_specific_allowed_countries', array('DZ'));

            // مسح جميع مناطق الشحن القديمة
            $this->delete_existing_zones();

            // تجميع بيانات الشحن من جميع الشركات
            $this->shipping_data = $this->load_all_pricing_files();

            if (empty($this->shipping_data)) {
                throw new Exception('لم يتم العثور على بيانات الشحن');
            }



            // تنظيم البيانات حسب الولاية
            $companies_by_wilaya = [];
            foreach ($this->shipping_data as $data) {
                $wilaya_id = $data['IDWilaya'];
                if (!isset($companies_by_wilaya[$wilaya_id])) {
                    $companies_by_wilaya[$wilaya_id] = [];
                }
                $companies_by_wilaya[$wilaya_id][] = $data;
            }

            // إنشاء منطقة شحن لكل ولاية (58 ولاية)
            $states = $this->add_dz_states([])['DZ'];
            foreach ($states as $state_code => $state_name) {
                $wilaya_id = (int)substr($state_code, 3); // استخراج رقم الولاية من الكود DZ-XX

                // إنشاء منطقة الشحن
                $zone = new WC_Shipping_Zone();
                $zone->set_zone_name(preg_replace('/^\d+\s+-\s+/', '', $state_name)); // إزالة الرقم من اسم الولاية

                $location = array(
                    'type' => 'state',
                    'code' => 'DZ:' . $state_code
                );

                $zone->add_location($location['code'], $location['type']);
                $zone->save();

                // إضافة طرق الشحن المتوفرة من الشركات لهذه الولاية
                if (isset($companies_by_wilaya[$wilaya_id])) {
                    foreach ($companies_by_wilaya[$wilaya_id] as $company_data) {
                        if ($company_data['Domicile'] != "0") {
                            $this->add_shipping_method(
                                $zone,
                                $company_data['company_name'] . ' (توصيل إلى المنزل)',
                                $company_data['Domicile']
                            );
                        }

                        if ($company_data['Stopdesk'] != "0") {
                            $this->add_shipping_method(
                                $zone,
                                $company_data['company_name'] . ' (توصيل إلى المكتب)',
                                $company_data['Stopdesk']
                            );
                        }
                    }
                }
            }

            // تجميع إحصائيات عن العملية
            $stats = [
                'total_zones' => count($states),
                'companies' => [],
                'total_methods' => 0
            ];

            // حساب عدد طرق التوصيل لكل شركة
            foreach ($companies_by_wilaya as $wilaya_methods) {
                foreach ($wilaya_methods as $method) {
                    if (!isset($stats['companies'][$method['company_name']])) {
                        $stats['companies'][$method['company_name']] = [
                            'home_delivery' => 0,
                            'office_delivery' => 0
                        ];
                    }

                    if ($method['Domicile'] != "0") {
                        $stats['companies'][$method['company_name']]['home_delivery']++;
                        $stats['total_methods']++;
                    }
                    if ($method['Stopdesk'] != "0") {
                        $stats['companies'][$method['company_name']]['office_delivery']++;
                        $stats['total_methods']++;
                    }
                }
            }

            // إنشاء تقرير مفصل
            $report = "تم إنشاء {$stats['total_zones']} منطقة شحن\n\n";
            $report .= "إجمالي طرق التوصيل: {$stats['total_methods']}\n\n";
            $report .= "تفاصيل الشركات:\n";
            $report .= "══════════════\n\n";

            foreach ($stats['companies'] as $company => $methods) {
                $report .= "◉ {$company}:\n";
                $report .= "  • توصيل للمنزل: {$methods['home_delivery']} ولاية\n";
                $report .= "  • توصيل للمكتب: {$methods['office_delivery']} ولاية\n\n";
            }

            wp_send_json_success($report);

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * حذف جميع مناطق الشحن الموجودة مع تنظيف شامل
     */
    private function delete_existing_zones() {
        global $wpdb;

        try {
            // الحصول على جميع معرفات طرق الشحن قبل الحذف
            $shipping_method_ids = $wpdb->get_col("
                SELECT instance_id
                FROM {$wpdb->prefix}woocommerce_shipping_zone_methods
            ");

            // حذف جداول مناطق الشحن
            $wpdb->query("TRUNCATE TABLE {$wpdb->prefix}woocommerce_shipping_zones");
            $wpdb->query("TRUNCATE TABLE {$wpdb->prefix}woocommerce_shipping_zone_locations");
            $wpdb->query("TRUNCATE TABLE {$wpdb->prefix}woocommerce_shipping_zone_methods");

            // تنظيف خيارات طرق الشحن المرتبطة
            foreach ($shipping_method_ids as $instance_id) {
                delete_option('woocommerce_flat_rate_' . $instance_id . '_settings');
            }

            // تنظيف أي خيارات متبقية لطرق الشحن
            $wpdb->query("
                DELETE FROM {$wpdb->prefix}options
                WHERE option_name LIKE 'woocommerce_flat_rate_%_settings'
            ");

            // تنظيف cache WooCommerce
            if (function_exists('wc_delete_shipping_zone_cache')) {
                wc_delete_shipping_zone_cache();
            }

            // مسح transients متعلقة بالشحن
            delete_transient('wc_shipping_method_count');
            delete_transient('shipping_method_count');

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * إضافة طريقة شحن لمنطقة معينة مع دعم فئات الشحن
     */
    private function add_shipping_method($zone, $title, $cost) {
        $instance_id = $zone->add_shipping_method('flat_rate');
        $shipping_method = WC_Shipping_Zones::get_shipping_method($instance_id);

        if ($shipping_method) {
            $settings = array(
                'title' => $title,
                'cost' => $cost,
                'tax_status' => 'none',
                'enabled' => 'yes'
            );

            // إضافة دعم فئات الشحن
            $shipping_classes = WC()->shipping()->get_shipping_classes();
            if (!empty($shipping_classes)) {
                foreach ($shipping_classes as $shipping_class) {
                    if (!isset($shipping_class->term_id)) {
                        continue;
                    }

                    // الحصول على التكلفة الإضافية لهذه الفئة من الإعدادات
                    $class_cost = $this->get_shipping_class_cost($shipping_class->term_id);
                    if ($class_cost > 0) {
                        $settings['class_cost_' . $shipping_class->term_id] = $class_cost;
                    }
                }

                // إعداد نوع الحساب (per class أو per order)
                $settings['type'] = 'class'; // حساب التكلفة لكل فئة منفصلة
            }

            update_option('woocommerce_flat_rate_' . $instance_id . '_settings', $settings, 'yes');
        }
    }

    /**
     * الحصول على التكلفة الإضافية لفئة شحن معينة
     */
    private function get_shipping_class_cost($class_id) {
        // الحصول على إعدادات فئات الشحن من خيارات الإضافة
        $shipping_class_costs = get_option('pexlat_form_shipping_class_costs', array());

        if (isset($shipping_class_costs[$class_id])) {
            return floatval($shipping_class_costs[$class_id]);
        }

        return 0;
    }

    /**
     * إضافة الولايات الجزائرية لـ WooCommerce
     */
    public function add_dz_states($states) {
        $states['DZ'] = array(
            'DZ-01' => '01 Adrar - أدرار',
            'DZ-02' => '02 Chlef - الشلف',
            'DZ-03' => '03 Laghouat - الأغواط',
            'DZ-04' => '04 Oum El Bouaghi - أم البواقي',
            'DZ-05' => '05 Batna - باتنة',
            'DZ-06' => '06 Béjaïa - بجاية',
            'DZ-07' => '07 Biskra - بسكرة',
            'DZ-08' => '08 Bechar - بشار',
            'DZ-09' => '09 Blida - البليدة',
            'DZ-10' => '10 Bouira - البويرة',
            'DZ-11' => '11 Tamanrasset - تمنراست',
            'DZ-12' => '12 Tébessa - تبسة',
            'DZ-13' => '13 Tlemcene - تلمسان',
            'DZ-14' => '14 Tiaret - تيارت',
            'DZ-15' => '15 Tizi Ouzou - تيزي وزو',
            'DZ-16' => '16 Alger - الجزائر',
            'DZ-17' => '17 Djelfa - الجلفة',
            'DZ-18' => '18 Jijel - جيجل',
            'DZ-19' => '19 Sétif - سطيف',
            'DZ-20' => '20 Saïda - سعيدة',
            'DZ-21' => '21 Skikda - سكيكدة',
            'DZ-22' => '22 Sidi Bel Abbès - سيدي بلعباس',
            'DZ-23' => '23 Annaba - عنابة',
            'DZ-24' => '24 Guelma - قالمة',
            'DZ-25' => '25 Constantine - قسنطينة',
            'DZ-26' => '26 Médéa - المدية',
            'DZ-27' => '27 Mostaganem - مستغانم',
            'DZ-28' => '28 MSila - مسيلة',
            'DZ-29' => '29 Mascara - معسكر',
            'DZ-30' => '30 Ouargla - ورقلة',
            'DZ-31' => '31 Oran - وهران',
            'DZ-32' => '32 El Bayadh - البيض',
            'DZ-33' => '33 Illizi - إليزي',
            'DZ-34' => '34 Bordj Bou Arreridj - برج بوعريريج',
            'DZ-35' => '35 Boumerdès - بومرداس',
            'DZ-36' => '36 El Tarf - الطارف',
            'DZ-37' => '37 Tindouf - تندوف',
            'DZ-38' => '38 Tissemsilt - تيسمسيلت',
            'DZ-39' => '39 Eloued - الوادي',
            'DZ-40' => '40 Khenchela - خنشلة',
            'DZ-41' => '41 Souk Ahras - سوق أهراس',
            'DZ-42' => '42 Tipaza - تيبازة',
            'DZ-43' => '43 Mila - ميلة',
            'DZ-44' => '44 Aïn Defla - عين الدفلى',
            'DZ-45' => '45 Naâma - النعامة',
            'DZ-46' => '46 Aïn Témouchent - عين تموشنت',
            'DZ-47' => '47 Ghardaïa - غرداية',
            'DZ-48' => '48 Relizane - غليزان',
            'DZ-49' => '49 Timimoun - تيميمون',
            'DZ-50' => '50 Bordj Baji Mokhtar - برج باجي مختار',
            'DZ-51' => '51 Ouled Djellal - أولاد جلال',
            'DZ-52' => '52 Béni Abbès - بني عباس',
            'DZ-53' => '53 Aïn Salah - عين صالح',
            'DZ-54' => '54 In Guezzam - عين قزام',
            'DZ-55' => '55 Touggourt - تقرت',
            'DZ-56' => '56 Djanet - جانت',
            'DZ-57' => '57 El MGhair - المغير',
            'DZ-58' => '58 El Menia - المنيعة'
        );

        return $states;
    }

    /**
     * الحصول على قائمة الشركات
     */
    public function get_companies() {
        return $this->companies;
    }

    /**
     * الحصول على محتوى ملف التسعير للتحرير
     */
    public function get_pricing_file_content() {
        check_ajax_referer('pexlat_form_nonce', '_ajax_nonce');

        try {
            if (!isset($_POST['company_id'])) {
                throw new Exception('معرف الشركة غير موجود');
            }

            $company_id = sanitize_key($_POST['company_id']);
            if (!isset($this->companies[$company_id])) {
                throw new Exception('الشركة غير موجودة');
            }

            $file_path = PEXLAT_FORM_PLUGIN_DIR . 'shipping-data/' . $this->companies[$company_id]['file'];

            if (!file_exists($file_path)) {
                // إنشاء ملف فارغ بالبنية الأساسية
                $default_content = $this->get_default_pricing_structure();
                wp_send_json_success([
                    'content' => json_encode($default_content, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE),
                    'file_exists' => false,
                    'company_name' => $this->companies[$company_id]['name']
                ]);
                return;
            }

            $content = file_get_contents($file_path);
            if ($content === false) {
                throw new Exception('فشل في قراءة محتوى الملف');
            }

            // التحقق من صحة JSON وتنسيقه
            $data = json_decode($content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('الملف يحتوي على JSON غير صالح: ' . json_last_error_msg());
            }

            // إعادة تنسيق JSON بشكل جميل
            $formatted_content = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

            wp_send_json_success([
                'content' => $formatted_content,
                'file_exists' => true,
                'company_name' => $this->companies[$company_id]['name'],
                'record_count' => count($data)
            ]);

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * حفظ محتوى ملف التسعير المحرر
     */
    public function save_pricing_file_content() {
        check_ajax_referer('pexlat_form_nonce', '_ajax_nonce');

        try {
            if (!isset($_POST['company_id']) || !isset($_POST['content'])) {
                throw new Exception('البيانات غير مكتملة');
            }

            $company_id = sanitize_key($_POST['company_id']);
            $content = wp_unslash($_POST['content']); // إزالة الـ slashes المضافة بواسطة WordPress

            if (!isset($this->companies[$company_id])) {
                throw new Exception('الشركة غير موجودة');
            }

            // التحقق من صحة JSON
            $data = json_decode($content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('JSON غير صالح: ' . json_last_error_msg());
            }

            // التحقق من بنية البيانات
            if (!is_array($data)) {
                throw new Exception('يجب أن يكون الملف مصفوفة من البيانات');
            }

            // التحقق من صحة بنية كل عنصر
            foreach ($data as $index => $item) {
                if (!is_array($item)) {
                    throw new Exception("العنصر رقم " . ($index + 1) . " يجب أن يكون كائن");
                }

                $required_fields = ['IDWilaya', 'Wilaya', 'Domicile', 'Stopdesk'];
                foreach ($required_fields as $field) {
                    if (!isset($item[$field])) {
                        throw new Exception("العنصر رقم " . ($index + 1) . " يفتقد للحقل المطلوب: $field");
                    }
                }

                // التحقق من أن IDWilaya رقم صحيح
                if (!is_numeric($item['IDWilaya']) || $item['IDWilaya'] < 1 || $item['IDWilaya'] > 58) {
                    throw new Exception("العنصر رقم " . ($index + 1) . " يحتوي على IDWilaya غير صالح: " . $item['IDWilaya']);
                }
            }

            // إنشاء مجلد shipping-data إذا لم يكن موجوداً
            $shipping_dir = PEXLAT_FORM_PLUGIN_DIR . 'shipping-data/';
            if (!file_exists($shipping_dir)) {
                wp_mkdir_p($shipping_dir);
            }

            // حفظ الملف
            $file_path = $shipping_dir . $this->companies[$company_id]['file'];
            $formatted_content = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

            if (file_put_contents($file_path, $formatted_content) === false) {
                throw new Exception('فشل في حفظ الملف');
            }

            wp_send_json_success([
                'message' => 'تم حفظ ملف أسعار ' . $this->companies[$company_id]['name'] . ' بنجاح',
                'record_count' => count($data),
                'file_size' => size_format(filesize($file_path))
            ]);

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * الحصول على البنية الافتراضية لملف التسعير
     */
    private function get_default_pricing_structure() {
        return [
            [
                'IDWilaya' => 1,
                'Wilaya' => 'Adrar',
                'Domicile' => '1000',
                'Stopdesk' => '800',
                'Annuler' => '200'
            ],
            [
                'IDWilaya' => 2,
                'Wilaya' => 'Chlef',
                'Domicile' => '800',
                'Stopdesk' => '600',
                'Annuler' => '200'
            ]
            // يمكن إضافة المزيد من الولايات هنا
        ];
    }



    /**
     * عرض صفحة إدارة طرق التوصيل
     */
    public function render_shipping_zones_page() {
        ?>
        <div class="wrap">
            <h1>إدارة مناطق الشحن</h1>


        </div>

        <div class="pexlat-form-shipping-zones">

            <div class="pexlat-form-card">
                <h2>شركات الشحن</h2>
                <div class="shipping-companies">
                    <?php foreach ($this->companies as $company_id => $company): ?>
                        <div class="company-item <?php echo !$company['enabled'] ? 'disabled' : ''; ?>" data-company="<?php echo esc_attr($company_id); ?>">
                            <div class="company-header">
                                <h3><?php echo esc_html($company['name']); ?></h3>
                                <div class="company-actions">
                                    <button type="button"
                                            class="button edit-pricing-file"
                                            data-company="<?php echo esc_attr($company_id); ?>"
                                            title="تحرير ملف الأسعار">
                                        <i class="fas fa-edit"></i> تحرير
                                    </button>
                                    <button type="button"
                                            class="button toggle-company"
                                            data-company="<?php echo esc_attr($company_id); ?>">
                                        <?php echo $company['enabled'] ? 'تعطيل' : 'تفعيل'; ?>
                                    </button>
                                    <button type="button"
                                            class="button button-danger delete-company"
                                            data-company="<?php echo esc_attr($company_id); ?>">
                                        حذف
                                    </button>
                                </div>
                            </div>

                            <div class="company-status">
                                <span class="status-indicator"></span>
                                <span class="status-text">
                                    <?php echo $company['enabled'] ? 'مفعلة' : 'معطلة'; ?>
                                </span>
                            </div>

                            <?php
                            $company_data = $this->load_pricing_file($company_id);
                            if ($company_data): ?>
                                <div class="notice notice-success inline">
                                    <p>تم العثور على ملف الأسعار (<?php echo esc_html($company['file']); ?>)</p>
                                </div>
                            <?php else: ?>
                                <div class="notice notice-warning inline">
                                    <p>لم يتم العثور على ملف الأسعار</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>

                <h3>إضافة شركة جديدة</h3>
                <form id="add-company-form" method="post">
                    <input type="text" name="company_id" placeholder="معرف الشركة (بالإنجليزية)" required>
                    <input type="text" name="company_name" placeholder="اسم الشركة" required>
                    <button type="submit" class="button">إضافة شركة</button>
                </form>

                <h3>رفع ملف الأسعار</h3>
                <form id="pricing-upload-form" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <select name="company_id" required>
                            <?php foreach ($this->companies as $id => $company): ?>
                                <option value="<?php echo esc_attr($id); ?>"><?php echo esc_html($company['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="file" name="pricing_file" id="pricing_file" accept=".json" required>
                        <button type="submit" class="button button-primary">رفع ملف الأسعار</button>
                    </div>
                    <p class="description">اختر الشركة وارفع ملف JSON الخاص بها</p>
                    <div id="file-preview"></div>
                </form>
            </div>

            <div class="pexlat-form-card" style="margin-top: 20px;">
                <button id="import-shipping-zones" class="button button-primary">بدء الاستيراد</button>
                <div id="import-progress"></div>
            </div>
        </div>

        <!-- نافذة محرر ملف الأسعار -->
        <div id="pricing-file-editor-modal" class="pricing-editor-modal" style="display: none;">
            <div class="pricing-editor-overlay"></div>
            <div class="pricing-editor-content">
                <div class="pricing-editor-header">
                    <h2 id="editor-title">تحرير ملف الأسعار</h2>
                    <button type="button" class="pricing-editor-close">&times;</button>
                </div>

                <div class="pricing-editor-body">
                    <div class="editor-info">
                        <div class="info-item">
                            <strong>الشركة:</strong> <span id="editor-company-name">-</span>
                        </div>
                        <div class="info-item">
                            <strong>عدد السجلات:</strong> <span id="editor-record-count">-</span>
                        </div>
                        <div class="info-item">
                            <strong>حالة الملف:</strong> <span id="editor-file-status">-</span>
                        </div>
                    </div>

                    <div class="editor-help">
                        <h4>تنسيق البيانات المطلوب:</h4>
                        <pre><code>[
  {
    "IDWilaya": 1,
    "Wilaya": "Adrar",
    "Domicile": "1000",
    "Stopdesk": "800",
    "Annuler": "200"
  }
]</code></pre>
                        <p><strong>ملاحظة:</strong> IDWilaya يجب أن يكون رقم من 1 إلى 58</p>
                    </div>

                    <div class="editor-container">
                        <textarea id="pricing-file-content" rows="20" placeholder="محتوى ملف JSON..."></textarea>
                    </div>

                    <div class="editor-validation">
                        <div id="validation-result"></div>
                    </div>
                </div>

                <div class="pricing-editor-footer">
                    <button type="button" id="validate-json" class="button">التحقق من صحة JSON</button>
                    <button type="button" id="format-json" class="button">تنسيق JSON</button>
                    <button type="button" id="save-pricing-file" class="button button-primary">حفظ الملف</button>
                    <button type="button" class="button pricing-editor-close">إلغاء</button>
                </div>
            </div>
        </div>

        <!-- قسم إعدادات فئات الشحن -->
        <div class="pexlat-form-card" style="margin-top: 20px;">
            <h2>إعدادات فئات الشحن</h2>

            <div class="notice notice-info inline" style="margin-bottom: 20px;">
                <h4 style="margin-top: 0;">كيف تعمل فئات الشحن؟</h4>
                <ul style="margin: 10px 0; padding-right: 20px;">
                    <li><strong>التكلفة الأساسية:</strong> هي تكلفة الشحن العادية للولاية (مثل 500 دج)</li>
                    <li><strong>تكلفة الفئة:</strong> هي التكلفة الإضافية للمنتجات من فئة معينة (مثل 200 دج للمنتجات الثقيلة)</li>
                    <li><strong>التكلفة النهائية:</strong> = التكلفة الأساسية + تكلفة الفئة (500 + 200 = 700 دج)</li>
                </ul>
                <p><strong>مثال:</strong> إذا كان سعر التوصيل لولاية الجزائر 500 دج، وتم تحديد تكلفة إضافية 200 دج لفئة "المنتجات الثقيلة"، فإن المنتجات من هذه الفئة ستكلف 700 دج للتوصيل.</p>
            </div>

            <p>يمكنك تحديد التكلفة الإضافية لكل فئة شحن. هذه التكلفة ستُضاف إلى تكلفة الشحن الأساسية للمنتجات التي تنتمي لهذه الفئة.</p>

            <?php
            $shipping_classes = WC()->shipping()->get_shipping_classes();
            $shipping_class_costs = get_option('pexlat_form_shipping_class_costs', array());

            if (!empty($shipping_classes)): ?>
                <form id="shipping-class-costs-form">
                    <table class="widefat fixed">
                        <thead>
                            <tr>
                                <th style="width: 40%;">اسم فئة الشحن</th>
                                <th style="width: 30%;">الوصف</th>
                                <th style="width: 30%;">التكلفة الإضافية (دج)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($shipping_classes as $shipping_class): ?>
                                <tr>
                                    <td data-label="اسم فئة الشحن">
                                        <strong><?php echo esc_html($shipping_class->name); ?></strong>
                                        <br><small>الرمز: <?php echo esc_html($shipping_class->slug); ?></small>
                                    </td>
                                    <td data-label="الوصف">
                                        <?php echo esc_html($shipping_class->description ?: 'لا يوجد وصف'); ?>
                                    </td>
                                    <td data-label="التكلفة الإضافية (دج)">
                                        <input type="number"
                                               name="class_cost_<?php echo esc_attr($shipping_class->term_id); ?>"
                                               value="<?php echo esc_attr($shipping_class_costs[$shipping_class->term_id] ?? '0'); ?>"
                                               min="0"
                                               step="1"
                                               placeholder="0"
                                               style="width: 100%;">
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <p style="margin-top: 15px;">
                        <button type="submit" class="button button-primary">حفظ إعدادات فئات الشحن</button>
                    </p>

                    <div id="shipping-class-costs-result" style="margin-top: 10px;"></div>
                </form>
            <?php else: ?>
                <div class="notice notice-warning inline">
                    <h4 style="margin-top: 0;">لا توجد فئات شحن محددة</h4>
                    <p>لاستخدام هذه الميزة، تحتاج إلى إنشاء فئات شحن أولاً:</p>
                    <ol style="margin: 10px 0; padding-right: 20px;">
                        <li>اذهب إلى <a href="<?php echo admin_url('admin.php?page=wc-settings&tab=shipping&section=classes'); ?>" target="_blank"><strong>إعدادات WooCommerce > الشحن > الفئات</strong></a></li>
                        <li>أضف فئة جديدة (مثل "المنتجات الثقيلة" أو "المنتجات الهشة")</li>
                        <li>احفظ التغييرات وارجع إلى هذه الصفحة</li>
                        <li>حدد التكلفة الإضافية لكل فئة</li>
                        <li>في صفحة تحرير المنتج، اختر الفئة المناسبة من قسم "الشحن"</li>
                    </ol>
                    <p><strong>ملاحظة:</strong> بعد إنشاء الفئات وتحديد التكاليف، ستحتاج إلى إعادة استيراد مناطق الشحن لتطبيق الإعدادات الجديدة.</p>
                </div>
            <?php endif; ?>


        </div>
        <?php
    }

    /**
     * حفظ إعدادات فئات الشحن
     */
    public function save_shipping_class_costs() {
        check_ajax_referer('pexlat_form_nonce', '_ajax_nonce');

        try {
            $shipping_class_costs = array();

            // الحصول على جميع فئات الشحن
            $shipping_classes = WC()->shipping()->get_shipping_classes();

            foreach ($shipping_classes as $shipping_class) {
                $cost_key = 'class_cost_' . $shipping_class->term_id;
                if (isset($_POST[$cost_key])) {
                    $cost = floatval($_POST[$cost_key]);
                    if ($cost >= 0) {
                        $shipping_class_costs[$shipping_class->term_id] = $cost;
                    }
                }
            }

            // حفظ الإعدادات
            update_option('pexlat_form_shipping_class_costs', $shipping_class_costs);

            // تحديث طرق الشحن الموجودة لتشمل فئات الشحن الجديدة
            $this->update_existing_shipping_methods_with_classes();

            wp_send_json_success('تم حفظ إعدادات فئات الشحن بنجاح وتحديث طرق الشحن الموجودة');

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * تحديث طرق الشحن الموجودة لتشمل إعدادات فئات الشحن الجديدة
     */
    private function update_existing_shipping_methods_with_classes() {
        // الحصول على جميع مناطق الشحن
        $zones = WC_Shipping_Zones::get_zones();

        // إضافة المنطقة الافتراضية
        $zones[0] = array(
            'zone_id' => 0,
            'zone_name' => 'المنطقة الافتراضية',
            'zone_locations' => array(),
            'shipping_methods' => WC_Shipping_Zones::get_zone(0)->get_shipping_methods()
        );

        foreach ($zones as $zone_data) {
            $zone_id = $zone_data['zone_id'];
            $zone = WC_Shipping_Zones::get_zone($zone_id);

            // الحصول على طرق الشحن في هذه المنطقة
            $shipping_methods = $zone->get_shipping_methods();

            foreach ($shipping_methods as $instance_id => $shipping_method) {
                // التحقق من أن طريقة الشحن هي flat_rate
                if ($shipping_method->id === 'flat_rate') {
                    $this->update_flat_rate_method_with_classes($instance_id);
                }
            }
        }
    }

    /**
     * تحديث طريقة شحن flat_rate محددة لتشمل إعدادات فئات الشحن
     */
    private function update_flat_rate_method_with_classes($instance_id) {
        // الحصول على إعدادات طريقة الشحن الحالية
        $settings = get_option('woocommerce_flat_rate_' . $instance_id . '_settings', array());

        if (empty($settings)) {
            return;
        }

        // الحصول على فئات الشحن وتكاليفها
        $shipping_classes = WC()->shipping()->get_shipping_classes();
        $shipping_class_costs = get_option('pexlat_form_shipping_class_costs', array());

        if (!empty($shipping_classes)) {
            foreach ($shipping_classes as $shipping_class) {
                if (!isset($shipping_class->term_id)) {
                    continue;
                }

                // إضافة أو تحديث تكلفة الفئة
                $class_cost = isset($shipping_class_costs[$shipping_class->term_id])
                    ? floatval($shipping_class_costs[$shipping_class->term_id])
                    : 0;

                $settings['class_cost_' . $shipping_class->term_id] = $class_cost;
            }

            // إعداد نوع الحساب
            $settings['type'] = 'class';

            // حفظ الإعدادات المحدثة
            update_option('woocommerce_flat_rate_' . $instance_id . '_settings', $settings);
        }
    }
}