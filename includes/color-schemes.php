<?php
/**
 * مجموعات الألوان المتناسقة للنموذج
 * 
 * @package Pexlat_Form
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * الحصول على جميع مجموعات الألوان المتاحة
 * 
 * @return array مصفوفة تحتوي على جميع مجموعات الألوان
 */
function fe_get_color_schemes() {
    return array(
        'blue_professional' => array(
            'name' => 'أزرق مهني',
            'description' => 'مخطط أزرق أنيق ومهني مناسب للأعمال',
            'colors' => array(
                'primary' => '#2563eb',
                'primary_hover' => '#1d4ed8',
                'secondary' => '#64748b',
                'background' => '#ffffff',
                'card_background' => '#f8fafc',
                'text_primary' => '#1e293b',
                'text_secondary' => '#64748b',
                'border' => '#e2e8f0',
                'input_background' => '#ffffff',
                'input_border' => '#d1d5db',
                'input_focus' => '#3b82f6',
                'success' => '#10b981',
                'error' => '#ef4444',
                'warning' => '#f59e0b'
            )
        ),
        
        'green_nature' => array(
            'name' => 'أخضر طبيعي',
            'description' => 'مخطط أخضر منعش مستوحى من الطبيعة',
            'colors' => array(
                'primary' => '#059669',
                'primary_hover' => '#047857',
                'secondary' => '#6b7280',
                'background' => '#ffffff',
                'card_background' => '#f0fdf4',
                'text_primary' => '#1f2937',
                'text_secondary' => '#6b7280',
                'border' => '#d1fae5',
                'input_background' => '#ffffff',
                'input_border' => '#d1d5db',
                'input_focus' => '#10b981',
                'success' => '#10b981',
                'error' => '#ef4444',
                'warning' => '#f59e0b'
            )
        ),
        
        'purple_elegant' => array(
            'name' => 'بنفسجي أنيق',
            'description' => 'مخطط بنفسجي راقي وأنيق',
            'colors' => array(
                'primary' => '#7c3aed',
                'primary_hover' => '#6d28d9',
                'secondary' => '#6b7280',
                'background' => '#ffffff',
                'card_background' => '#faf5ff',
                'text_primary' => '#1f2937',
                'text_secondary' => '#6b7280',
                'border' => '#e9d5ff',
                'input_background' => '#ffffff',
                'input_border' => '#d1d5db',
                'input_focus' => '#8b5cf6',
                'success' => '#10b981',
                'error' => '#ef4444',
                'warning' => '#f59e0b'
            )
        ),
        
        'orange_warm' => array(
            'name' => 'برتقالي دافئ',
            'description' => 'مخطط برتقالي دافئ ومرحب',
            'colors' => array(
                'primary' => '#ea580c',
                'primary_hover' => '#dc2626',
                'secondary' => '#6b7280',
                'background' => '#ffffff',
                'card_background' => '#fff7ed',
                'text_primary' => '#1f2937',
                'text_secondary' => '#6b7280',
                'border' => '#fed7aa',
                'input_background' => '#ffffff',
                'input_border' => '#d1d5db',
                'input_focus' => '#f97316',
                'success' => '#10b981',
                'error' => '#ef4444',
                'warning' => '#f59e0b'
            )
        ),
        
        'teal_modern' => array(
            'name' => 'تركوازي عصري',
            'description' => 'مخطط تركوازي عصري ومنعش',
            'colors' => array(
                'primary' => '#0d9488',
                'primary_hover' => '#0f766e',
                'secondary' => '#6b7280',
                'background' => '#ffffff',
                'card_background' => '#f0fdfa',
                'text_primary' => '#1f2937',
                'text_secondary' => '#6b7280',
                'border' => '#99f6e4',
                'input_background' => '#ffffff',
                'input_border' => '#d1d5db',
                'input_focus' => '#14b8a6',
                'success' => '#10b981',
                'error' => '#ef4444',
                'warning' => '#f59e0b'
            )
        ),
        
        'dark_mode' => array(
            'name' => 'الوضع المظلم',
            'description' => 'مخطط مظلم أنيق للعيون',
            'colors' => array(
                'primary' => '#3b82f6',
                'primary_hover' => '#2563eb',
                'secondary' => '#9ca3af',
                'background' => '#1f2937',
                'card_background' => '#374151',
                'text_primary' => '#f9fafb',
                'text_secondary' => '#d1d5db',
                'border' => '#4b5563',
                'input_background' => '#4b5563',
                'input_border' => '#6b7280',
                'input_focus' => '#60a5fa',
                'success' => '#10b981',
                'error' => '#ef4444',
                'warning' => '#f59e0b'
            )
        ),
        
        'red_bold' => array(
            'name' => 'أحمر جريء',
            'description' => 'مخطط أحمر جريء وقوي',
            'colors' => array(
                'primary' => '#dc2626',
                'primary_hover' => '#b91c1c',
                'secondary' => '#6b7280',
                'background' => '#ffffff',
                'card_background' => '#fef2f2',
                'text_primary' => '#1f2937',
                'text_secondary' => '#6b7280',
                'border' => '#fecaca',
                'input_background' => '#ffffff',
                'input_border' => '#d1d5db',
                'input_focus' => '#ef4444',
                'success' => '#10b981',
                'error' => '#ef4444',
                'warning' => '#f59e0b'
            )
        ),
        
        'gray_minimal' => array(
            'name' => 'رمادي بسيط',
            'description' => 'مخطط رمادي بسيط وأنيق',
            'colors' => array(
                'primary' => '#374151',
                'primary_hover' => '#1f2937',
                'secondary' => '#9ca3af',
                'background' => '#ffffff',
                'card_background' => '#f9fafb',
                'text_primary' => '#111827',
                'text_secondary' => '#6b7280',
                'border' => '#e5e7eb',
                'input_background' => '#ffffff',
                'input_border' => '#d1d5db',
                'input_focus' => '#6b7280',
                'success' => '#10b981',
                'error' => '#ef4444',
                'warning' => '#f59e0b'
            )
        ),

        'custom' => array(
            'name' => 'مجموعة مخصصة',
            'description' => 'قم بتخصيص الألوان حسب احتياجاتك',
            'colors' => array(
                'primary' => '#2563eb',
                'primary_hover' => '#1d4ed8',
                'secondary' => '#64748b',
                'background' => '#ffffff',
                'card_background' => '#f8fafc',
                'text_primary' => '#1e293b',
                'text_secondary' => '#64748b',
                'border' => '#e2e8f0',
                'input_background' => '#ffffff',
                'input_border' => '#d1d5db',
                'input_focus' => '#3b82f6',
                'success' => '#10b981',
                'error' => '#ef4444',
                'warning' => '#f59e0b'
            )
        )
    );
}

/**
 * الحصول على مخطط ألوان محدد
 * 
 * @param string $scheme_id معرف مخطط الألوان
 * @return array|null مخطط الألوان أو null إذا لم يوجد
 */
function fe_get_color_scheme($scheme_id) {
    $schemes = fe_get_color_schemes();
    return isset($schemes[$scheme_id]) ? $schemes[$scheme_id] : null;
}

/**
 * تطبيق مخطط الألوان على إعدادات النموذج
 * 
 * @param array $settings الإعدادات الحالية
 * @param string $scheme_id معرف مخطط الألوان
 * @return array الإعدادات المحدثة
 */
function fe_apply_color_scheme($settings, $scheme_id) {
    // إذا كانت المجموعة مخصصة، استخدم الألوان المخصصة من الإعدادات
    if ($scheme_id === 'custom') {
        return fe_apply_custom_colors($settings);
    }

    $scheme = fe_get_color_scheme($scheme_id);

    if (!$scheme) {
        return $settings;
    }

    $colors = $scheme['colors'];
    
    // تطبيق الألوان على الإعدادات الأساسية
    $settings['icons_color'] = $colors['primary'];
    $settings['card_bg_color'] = $colors['background'];
    $settings['text_color'] = $colors['text_primary'];
    $settings['fields_bg_color'] = $colors['input_background'];
    $settings['fields_border_color'] = $colors['input_border'];
    $settings['button_color'] = $colors['primary'];
    $settings['button_gradient_color'] = $colors['primary_hover'];
    $settings['button_text_color'] = '#ffffff'; // نص أبيض للأزرار
    $settings['form_border_color'] = $colors['border'];

    // إعدادات طرق الشحن
    $settings['shipping_bg_color'] = $colors['card_background'];
    $settings['shipping_border_color'] = $colors['border'];
    $settings['shipping_title_color'] = $colors['text_primary']; // نفس لون النصوص العادية
    $settings['shipping_price_color'] = $colors['primary'];
    $settings['shipping_selected_bg_color'] = $colors['primary'] . '0d'; // شفافية 5%
    $settings['shipping_selected_border_color'] = $colors['primary'];

    // إعدادات الشريط المثبت
    $settings['sticky_bar_button_color'] = $colors['primary'];
    $settings['sticky_bar_button_gradient_color'] = $colors['primary_hover'];
    $settings['sticky_bar_button_text_color'] = '#ffffff';

    // إعدادات الشريط العلوي لملخص الطلب
    $settings['summary_header_bg'] = $colors['input_background'];
    $settings['summary_header_border'] = $colors['border'];
    $settings['summary_title_color'] = $colors['text_primary'];
    $settings['summary_toggle_color'] = $colors['text_secondary'];

    // إعدادات طرق الدفع
    $settings['payment_methods_bg'] = $colors['card_background'];
    $settings['payment_methods_border'] = $colors['border'];
    $settings['payment_methods_title_color'] = $colors['text_primary'];
    $settings['payment_methods_text_color'] = $colors['text_secondary'];

    // إعدادات إضافية للتناسق
    $settings['labels_color'] = $colors['text_secondary'];
    $settings['placeholder_color'] = $colors['text_secondary'];
    $settings['focus_color'] = $colors['input_focus'];

    // إعدادات زر الواتساب (إذا كان مفعلاً)
    if (isset($settings['enable_whatsapp_button']) && $settings['enable_whatsapp_button']) {
        $settings['whatsapp_button_color'] = '#25d366'; // لون الواتساب الأخضر
    }
    
    return $settings;
}

/**
 * تطبيق الألوان المخصصة على إعدادات النموذج
 *
 * @param array $settings الإعدادات الحالية
 * @return array الإعدادات المحدثة
 */
function fe_apply_custom_colors($settings) {
    // استخدام الألوان المخصصة إذا كانت موجودة، وإلا استخدام القيم الافتراضية
    $custom_colors = array(
        'icons_color' => $settings['custom_primary'] ?? '#2563eb',
        'card_bg_color' => $settings['custom_background'] ?? '#ffffff',
        'text_color' => $settings['custom_text_primary'] ?? '#1e293b',
        'fields_bg_color' => $settings['custom_input_background'] ?? '#ffffff',
        'fields_border_color' => $settings['custom_input_border'] ?? '#d1d5db',
        'button_color' => $settings['custom_primary'] ?? '#2563eb',
        'button_gradient_color' => $settings['custom_primary_hover'] ?? '#1d4ed8',
        'button_text_color' => '#ffffff',
        'form_border_color' => $settings['custom_border'] ?? '#e2e8f0',
        'labels_color' => $settings['custom_text_secondary'] ?? '#64748b',
        'placeholder_color' => $settings['custom_text_secondary'] ?? '#64748b',
        'focus_color' => $settings['custom_input_focus'] ?? '#3b82f6',

        // إعدادات طرق الشحن
        'shipping_bg_color' => $settings['custom_background'] ?? '#ffffff',
        'shipping_border_color' => $settings['custom_border'] ?? '#e2e8f0',
        'shipping_title_color' => $settings['custom_text_primary'] ?? '#1e293b',
        'shipping_price_color' => $settings['custom_primary'] ?? '#2563eb',
        'shipping_selected_bg_color' => ($settings['custom_primary'] ?? '#2563eb') . '0d',
        'shipping_selected_border_color' => $settings['custom_primary'] ?? '#2563eb',

        // إعدادات الشريط المثبت
        'sticky_bar_button_color' => $settings['custom_primary'] ?? '#2563eb',
        'sticky_bar_button_gradient_color' => $settings['custom_primary_hover'] ?? '#1d4ed8',
        'sticky_bar_button_text_color' => '#ffffff',

        // إعدادات الشريط العلوي لملخص الطلب
        'summary_header_bg' => $settings['custom_input_background'] ?? '#f8fafc',
        'summary_header_border' => $settings['custom_border'] ?? '#e2e8f0',
        'summary_title_color' => $settings['custom_text_primary'] ?? '#1e293b',
        'summary_toggle_color' => $settings['custom_text_secondary'] ?? '#64748b',

        // إعدادات طرق الدفع
        'payment_methods_bg' => $settings['custom_card_background'] ?? $settings['custom_background'] ?? '#f8fafc',
        'payment_methods_border' => $settings['custom_border'] ?? '#e2e8f0',
        'payment_methods_title_color' => $settings['custom_text_primary'] ?? '#1e293b',
        'payment_methods_text_color' => $settings['custom_text_secondary'] ?? '#64748b'
    );

    // إعدادات زر الواتساب (إذا كان مفعلاً)
    if (isset($settings['enable_whatsapp_button']) && $settings['enable_whatsapp_button']) {
        $custom_colors['whatsapp_button_color'] = '#25d366'; // لون الواتساب الأخضر
    }

    // دمج الألوان المخصصة مع الإعدادات الحالية
    return array_merge($settings, $custom_colors);
}

/**
 * الحصول على قائمة بجميع الألوان القابلة للتخصيص
 *
 * @return array قائمة الألوان مع أوصافها
 */
function fe_get_customizable_colors() {
    return array(
        'custom_primary' => array(
            'name' => 'اللون الرئيسي',
            'description' => 'لون الأزرار والعناصر التفاعلية',
            'default' => '#2563eb',
            'group' => 'primary'
        ),
        'custom_primary_hover' => array(
            'name' => 'لون التحويم الرئيسي',
            'description' => 'لون الأزرار عند التحويم',
            'default' => '#1d4ed8',
            'group' => 'primary'
        ),
        'custom_background' => array(
            'name' => 'خلفية النموذج',
            'description' => 'لون خلفية النموذج الرئيسية',
            'default' => '#ffffff',
            'group' => 'background'
        ),
        'custom_text_primary' => array(
            'name' => 'النص الرئيسي',
            'description' => 'لون النصوص الرئيسية',
            'default' => '#1e293b',
            'group' => 'text'
        ),
        'custom_text_secondary' => array(
            'name' => 'النص الثانوي',
            'description' => 'لون التسميات والنصوص الثانوية',
            'default' => '#64748b',
            'group' => 'text'
        ),
        'custom_input_background' => array(
            'name' => 'خلفية الحقول',
            'description' => 'لون خلفية حقول الإدخال',
            'default' => '#ffffff',
            'group' => 'input'
        ),
        'custom_input_border' => array(
            'name' => 'حدود الحقول',
            'description' => 'لون حدود حقول الإدخال',
            'default' => '#d1d5db',
            'group' => 'input'
        ),
        'custom_input_focus' => array(
            'name' => 'تركيز الحقول',
            'description' => 'لون الحقول عند التركيز',
            'default' => '#3b82f6',
            'group' => 'input'
        ),
        'custom_border' => array(
            'name' => 'الحدود العامة',
            'description' => 'لون الحدود العامة للعناصر',
            'default' => '#e2e8f0',
            'group' => 'border'
        )
    );
}
