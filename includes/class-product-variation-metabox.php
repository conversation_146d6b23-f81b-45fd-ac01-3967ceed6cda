<?php
/**
 * إدارة ميتا بوكس المتغيرات والإعدادات في صفحة المنتج
 *
 * @since      1.0.0
 * @package    Pexlat_Form
 */

// منع الوصول المباشر
if (!defined('WPINC')) {
    die;
}

/**
 * صنف إدارة ميتا بوكس المتغيرات
 */
class Product_Variation_Metabox {

    /**
     * تهيئة الصنف
     */
    public function __construct() {
        // إضافة تبويب جديد في صفحة المنتج
        add_filter('woocommerce_product_data_tabs', array($this, 'add_pexlat_form_product_tab'));

        // إضافة حقول التبويب
        add_action('woocommerce_product_data_panels', array($this, 'add_pexlat_form_product_tab_content'));

        // حفظ بيانات المنتج
        add_action('woocommerce_process_product_meta', array($this, 'save_pexlat_form_product_fields'));

        // إضافة حقول مخصصة للمتغيرات
        add_action('woocommerce_product_after_variable_attributes', array($this, 'add_variation_custom_fields'), 10, 3);

        // حفظ بيانات المتغيرات
        add_action('woocommerce_save_product_variation', array($this, 'save_variation_custom_fields'), 10, 2);
    }

    /**
     * إضافة تبويب جديد في صفحة المنتج
     */
    public function add_pexlat_form_product_tab($tabs) {
        $tabs['pexlat_form'] = array(
            'label'    => 'Pexlat Form',
            'target'   => 'pexlat_form_product_data',
            'class'    => array('show_if_variable'),
            'priority' => 60
        );
        return $tabs;
    }

    /**
     * إضافة محتوى تبويب Pexlat Form
     */
    public function add_pexlat_form_product_tab_content() {
        global $post;
        $product = wc_get_product($post->ID);

        // التحقق من أن المنتج متغير
        if (!$product || !$product->is_type('variable')) {
            ?>
            <div id="pexlat_form_product_data" class="panel woocommerce_options_panel">
                <div class="options_group">
                    <p>هذه الإعدادات متاحة فقط للمنتجات المتغيرة.</p>
                </div>
            </div>
            <?php
            return;
        }

        // الحصول على سمات المنتج المتغير
        $attributes = $product->get_variation_attributes();

        ?>
        <div id="pexlat_form_product_data" class="panel woocommerce_options_panel">
            <div class="options_group">
                <h3>إعدادات عرض المتغيرات</h3>

                <?php if (!empty($attributes)) : ?>
                    <div class="attribute-display-settings">
                        <h4>تخصيص طريقة عرض السمات</h4>
                        <p class="description">يمكنك تخصيص طريقة عرض كل سمة من سمات المنتج بشكل منفصل.</p>

                        <table class="widefat attribute-display-table">
                            <thead>
                                <tr>
                                    <th>السمة</th>
                                    <th>طريقة العرض</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($attributes as $attribute_name => $options) :
                                    // الحصول على اسم السمة بدون البادئة
                                    $attribute_label = wc_attribute_label(str_replace('pa_', '', $attribute_name));
                                    $attribute_id = sanitize_title($attribute_name);

                                    // الحصول على طريقة العرض المحفوظة لهذه السمة
                                    $display_style = get_post_meta($post->ID, '_attribute_display_' . $attribute_id, true) ?: 'default';

                                    // تحديد ما إذا كانت السمة تمثل لونًا
                                    $is_color = (
                                        strpos(strtolower($attribute_name), 'color') !== false ||
                                        strpos(strtolower($attribute_name), 'colour') !== false ||
                                        strpos($attribute_name, 'لون') !== false
                                    );

                                    // تحديد خيارات العرض المتاحة
                                    $display_options = array(
                                        'default' => 'الافتراضي',
                                        'square' => 'مربعات (جنبًا إلى جنب)',
                                        'dropdown' => 'قائمة منسدلة',
                                        'buttons' => 'أزرار نصية',
                                        'extended' => 'بطاقات موسعة (مع الاسم كاملاً)',
                                        'offer_cards_rectangle' => 'بطاقات عروض مستطيلة',
                                        'offer_cards_square' => 'بطاقات عروض مربعة'
                                    );

                                    // إضافة خيار الدوائر للألوان
                                    if ($is_color) {
                                        $display_options['circle'] = 'دوائر ألوان';
                                    }
                                ?>
                                <tr>
                                    <td><?php echo esc_html($attribute_label); ?></td>
                                    <td>
                                        <select name="attribute_display_<?php echo esc_attr($attribute_id); ?>">
                                            <?php foreach ($display_options as $value => $label) : ?>
                                                <option value="<?php echo esc_attr($value); ?>" <?php selected($display_style, $value); ?>><?php echo esc_html($label); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else : ?>
                    <p>لم يتم العثور على سمات لهذا المنتج. يرجى إضافة سمات للمنتج أولاً.</p>
                <?php endif; ?>



                <div class="form-field">
                    <p class="description">
                        <strong>ملاحظة:</strong> يتم التعرف تلقائياً على نوع السمة:
                        <br>- <strong>اللون:</strong> إذا كان اسم السمة يحتوي على "color"، "colour"، "لون"
                        <br>- <strong>المقاس:</strong> إذا كان يحتوي على "size"، "مقاس"، "حجم"
                        <br>- <strong>الصورة:</strong> إذا كانت هناك صور مخصصة للمتغير في ووكومرس
                        <br>- <strong>النص:</strong> الخيار الافتراضي لباقي السمات
                    </p>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * حفظ بيانات المنتج
     */
    public function save_pexlat_form_product_fields($post_id) {
        $product = wc_get_product($post_id);

        // حفظ إعدادات عرض السمات إذا كان المنتج متغيراً
        if ($product && $product->is_type('variable')) {
            $attributes = $product->get_variation_attributes();

            if (!empty($attributes)) {
                foreach ($attributes as $attribute_name => $options) {
                    $attribute_id = sanitize_title($attribute_name);
                    $field_name = 'attribute_display_' . $attribute_id;

                    if (isset($_POST[$field_name])) {
                        $display_style = sanitize_text_field($_POST[$field_name]);
                        update_post_meta($post_id, '_attribute_display_' . $attribute_id, $display_style);
                    }
                }
            }
        }
    }

    /**
     * إضافة حقول مخصصة للمتغيرات
     */
    public function add_variation_custom_fields($loop, $variation_data, $variation) {
        // الحصول على قيمة نسبة الخصم المحفوظة
        $discount_percentage = get_post_meta($variation->ID, '_variation_discount_percentage', true);

        // الحصول على قيمة الكمية المطلوبة المحفوظة
        $required_quantity = get_post_meta($variation->ID, '_variation_required_quantity', true);
        ?>
        <div class="pexlat-form-variation-fields">
            <p class="form-field form-row form-row-full">
                <label for="variation_description_<?php echo esc_attr($loop); ?>">
                    <?php echo esc_html__('وصف المتغير', 'pexlat-form'); ?>
                </label>
                <textarea name="variation_description[<?php echo esc_attr($loop); ?>]" id="variation_description_<?php echo esc_attr($loop); ?>" rows="3"><?php echo esc_textarea(get_post_meta($variation->ID, '_variation_description', true)); ?></textarea>
            </p>

            <!-- تم إزالة حقل نسبة الخصم وسيتم حسابها تلقائيًا من السعر الأصلي وسعر البيع -->

            <p class="form-field form-row form-row-full">
                <label for="variation_required_quantity_<?php echo esc_attr($loop); ?>">
                    <?php echo esc_html__('الكمية المطلوبة', 'pexlat-form'); ?>
                    <span class="woocommerce-help-tip" data-tip="<?php echo esc_attr__('أدخل الكمية المطلوبة لهذا العرض. عند اختيار هذا المتغير، سيتم تحديث الكمية تلقائياً إلى هذه القيمة.', 'pexlat-form'); ?>"></span>
                </label>
                <input type="number"
                       name="variation_required_quantity[<?php echo esc_attr($loop); ?>]"
                       id="variation_required_quantity_<?php echo esc_attr($loop); ?>"
                       value="<?php echo esc_attr($required_quantity); ?>"
                       placeholder="مثال: 3"
                       min="1"
                       step="1"
                       style="width: 100%;">
            </p>

            <p class="form-field form-row form-row-full">
                <label for="variation_show_quantity_badge_<?php echo esc_attr($loop); ?>">
                    <?php echo esc_html__('إظهار شارة الكمية', 'pexlat-form'); ?>
                    <span class="woocommerce-help-tip" data-tip="<?php echo esc_attr__('اختر ما إذا كنت تريد إظهار شارة الكمية في الزاوية العليا اليمنى من بطاقة المتغير.', 'pexlat-form'); ?>"></span>
                </label>
                <select name="variation_show_quantity_badge[<?php echo esc_attr($loop); ?>]" id="variation_show_quantity_badge_<?php echo esc_attr($loop); ?>" style="width: 100%;">
                    <option value="no" <?php selected(get_post_meta($variation->ID, '_variation_show_quantity_badge', true), 'no'); ?>>لا</option>
                    <option value="yes" <?php selected(get_post_meta($variation->ID, '_variation_show_quantity_badge', true), 'yes'); ?>>نعم</option>
                </select>
            </p>
        </div>
        <?php
    }

    /**
     * حفظ بيانات المتغيرات
     */
    public function save_variation_custom_fields($variation_id, $loop) {
        // حفظ وصف المتغير
        if (isset($_POST['variation_description'][$loop])) {
            $variation_description = sanitize_textarea_field($_POST['variation_description'][$loop]);
            update_post_meta($variation_id, '_variation_description', $variation_description);
        }

        // حساب نسبة الخصم تلقائيًا من السعر الأصلي وسعر البيع
        $variation = wc_get_product($variation_id);
        if ($variation) {
            $regular_price = $variation->get_regular_price();
            $sale_price = $variation->get_sale_price();

            // حساب نسبة الخصم فقط إذا كان هناك سعر بيع وسعر أصلي
            if (!empty($regular_price) && !empty($sale_price) && $regular_price > $sale_price) {
                $discount_percentage = round(100 - (($sale_price / $regular_price) * 100));
                update_post_meta($variation_id, '_variation_discount_percentage', $discount_percentage);
            } else {
                // إذا لم يكن هناك خصم، قم بإزالة البيانات التعريفية
                delete_post_meta($variation_id, '_variation_discount_percentage');
            }
        }

        // حفظ الكمية المطلوبة
        if (isset($_POST['variation_required_quantity'][$loop])) {
            $required_quantity = absint($_POST['variation_required_quantity'][$loop]);
            // التأكد من أن الكمية على الأقل 1
            $required_quantity = max(1, $required_quantity);
            update_post_meta($variation_id, '_variation_required_quantity', $required_quantity);
        }

        // حفظ إعداد إظهار شارة الكمية
        if (isset($_POST['variation_show_quantity_badge'][$loop])) {
            $show_quantity_badge = sanitize_text_field($_POST['variation_show_quantity_badge'][$loop]);
            update_post_meta($variation_id, '_variation_show_quantity_badge', $show_quantity_badge);
        } else {
            // إذا لم يتم تحديد قيمة، نستخدم القيمة الافتراضية "لا"
            update_post_meta($variation_id, '_variation_show_quantity_badge', 'no');
        }
    }
}

// تهيئة الصنف
new Product_Variation_Metabox();
