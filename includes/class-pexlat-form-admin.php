<?php
/**
 * The admin-specific functionality of the plugin.
 *
 * @link       https://pexlat.com
 * @since      1.0.0
 *
 * @package    Pexlat_Form
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for the admin area.
 *
 * @package    Pexlat_Form
 */
class Pexlat_Form_Admin {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        add_action('admin_init', array($this, 'register_settings'));

        // إضافة نقطة عمل للتعامل مع طلبات المعاينة المتجاوبة عبر AJAX
        add_action('wp_ajax_pexlat_form_get_preview', array($this, 'ajax_get_form_preview'));

        // إضافة نقطة عمل للتعامل مع طلبات حظر العملاء عبر AJAX
        add_action('wp_ajax_pexlat_form_block_customer', array($this, 'ajax_block_customer'));

        // إضافة معالجات AJAX للحقول
        add_action('wp_ajax_pexlat_form_delete_field', array($this, 'delete_field_ajax'));
        add_action('wp_ajax_pexlat_form_add_field', array($this, 'add_field_ajax'));
        add_action('wp_ajax_pexlat_form_update_field_visibility', array($this, 'update_field_visibility_ajax'));
        add_action('wp_ajax_pexlat_form_update_field_required', array($this, 'update_field_required_ajax'));
        add_action('wp_ajax_pexlat_form_update_field_settings', array($this, 'update_field_settings_ajax'));

        // إضافة معالجات AJAX لتصدير/استيراد الإعدادات
        add_action('wp_ajax_pexlat_form_export_settings', array($this, 'export_form_settings'));
        add_action('wp_ajax_pexlat_form_import_settings', array($this, 'import_form_settings'));
        add_action('wp_ajax_pexlat_form_get_backups', array($this, 'get_settings_backups'));
        add_action('wp_ajax_pexlat_form_restore_backup', array($this, 'restore_settings_backup'));
        add_action('wp_ajax_pexlat_form_delete_backup', array($this, 'delete_settings_backup'));
    }

    /**
     * معالجة طلب حظر عميل عبر AJAX
     */
    public function ajax_block_customer() {
        // التحقق من الأمان
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'save_blocked_customer')) {
            wp_send_json_error('فشل التحقق الأمني');
            exit;
        }

        // التحقق من وجود البيانات
        if (!isset($_POST['data']) || empty($_POST['data'])) {
            wp_send_json_error('البيانات المرسلة غير كاملة');
            exit;
        }

        $data = $_POST['data'];

        // التحقق من وجود معرف واحد على الأقل (رقم هاتف، عنوان IP، بريد إلكتروني)
        if (empty($data['phone_number']) && empty($data['ip_address']) && empty($data['email'])) {
            wp_send_json_error('يجب توفير رقم هاتف أو عنوان IP أو بريد إلكتروني على الأقل');
            exit;
        }

        // إعداد بيانات العميل المحظور
        global $wpdb;
        $blocked_table = $wpdb->prefix . 'pexlat_form_blocked_customers';

        $customer_data = array(
            'customer_name' => isset($data['customer_name']) ? sanitize_text_field($data['customer_name']) : '',
            'phone_number' => isset($data['phone_number']) ? sanitize_text_field($data['phone_number']) : '',
            'ip_address' => isset($data['ip_address']) ? sanitize_text_field($data['ip_address']) : '',
            'email' => isset($data['email']) ? sanitize_email($data['email']) : '',
            'reason' => isset($data['reason']) ? sanitize_textarea_field($data['reason']) : '',
            'notes' => isset($data['notes']) ? sanitize_textarea_field($data['notes']) : '',
            'status' => 'active',
            'blocked_by' => get_current_user_id(),
            'blocked_at' => current_time('mysql')
        );

        // إدراج البيانات في قاعدة البيانات
        $result = $wpdb->insert(
            $blocked_table,
            $customer_data,
            array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s')
        );

        if ($result === false) {
            wp_send_json_error('فشل في إضافة العميل إلى قائمة الحظر');
            exit;
        }

        wp_send_json_success(array(
            'message' => 'تم إضافة العميل إلى قائمة الحظر بنجاح',
            'customer_id' => $wpdb->insert_id
        ));
        exit;
    }

    public function register_settings() {
        register_setting('pexlat_form_settings', 'pexlat_form_delete_data');
        register_setting('pexlat_form_settings', 'pexlat_form_language');
        register_setting('pexlat_form_settings', 'pexlat_form_save_abandoned_orders');
        register_setting('pexlat_form_settings', 'pexlat_form_abandoned_order_status');
        register_setting('pexlat_form_settings', 'pexlat_form_abandoned_orders_cleanup_time');
        register_setting('pexlat_form_settings', 'pexlat_form_prevent_duplicate_abandoned_orders');

        // إعدادات السلة
        register_setting('pexlat_form_settings', 'pexlat_form_cart_system_enabled');
        register_setting('pexlat_form_settings', 'pexlat_form_cart_button_default_text');
        register_setting('pexlat_form_settings', 'pexlat_form_cart_button_default_color');
        register_setting('pexlat_form_settings', 'pexlat_form_cart_icon_enabled');
        register_setting('pexlat_form_settings', 'pexlat_form_cart_auto_open');
        register_setting('pexlat_form_settings', 'pexlat_form_cart_save_data');
        register_setting('pexlat_form_settings', 'pexlat_form_cart_clear_after_order');
        register_setting('pexlat_form_settings', 'pexlat_form_cart_default_enabled');
        register_setting('pexlat_form_settings', 'pexlat_form_cart_button_style');
        register_setting('pexlat_form_settings', 'pexlat_form_floating_cart_position');
        register_setting('pexlat_form_settings', 'pexlat_form_floating_cart_color');
        register_setting('pexlat_form_settings', 'pexlat_form_floating_cart_size');
        register_setting('pexlat_form_settings', 'pexlat_form_floating_cart_shape');
    }

    /**
     * Register the stylesheets for the admin area.
     *
     * @since    1.0.0
     */
    public function enqueue_styles() {
        wp_enqueue_style($this->plugin_name, plugin_dir_url(dirname(__FILE__)) . 'admin/css/pexlat-form-admin.css', array(), $this->version, 'all');

        // إضافة CSS المخصص لصفحة قائمة العملاء
        $screen = get_current_screen();
        if ($screen && $screen->id === 'pexlat-form_page_pexlat-form-customers') {
            wp_enqueue_style(
                $this->plugin_name . '-customers',
                plugin_dir_url(dirname(__FILE__)) . 'admin/css/customers-list.css',
                array(),
                $this->version,
                'all'
            );
        }

        // محرر تخصيص الزر
        wp_enqueue_style(
            $this->plugin_name . '-button-editor',
            plugin_dir_url(dirname(__FILE__)) . 'admin/css/button-editor.css',
            array(),
            $this->version,
            'all'
        );



        // إضافة CSS المخصص لإعادة تصميم واجهة الإعدادات
        wp_enqueue_style(
            $this->plugin_name . '-settings-redesign',
            plugin_dir_url(dirname(__FILE__)) . 'admin/css/settings-redesign.css',
            array(),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );

        // إضافة CSS المخصص لتخطيط النظام الموحد للألوان والتصميم
        wp_enqueue_style(
            $this->plugin_name . '-unified-theme-layout',
            plugin_dir_url(dirname(__FILE__)) . 'admin/css/unified-theme-layout.css',
            array(),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );

        // إضافة CSS المحسن لخيارات التخطيط
        wp_enqueue_style(
            $this->plugin_name . '-layout-options-improved',
            plugin_dir_url(dirname(__FILE__)) . 'admin/css/layout-options-improved.css',
            array(),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );

        // إضافة CSS لشريط الشعار في أعلى صفحات الإضافة
        wp_enqueue_style(
            $this->plugin_name . '-header-bar',
            plugin_dir_url(dirname(__FILE__)) . 'admin/css/header-bar.css',
            array(),
            $this->version,
            'all'
        );



        // إضافة CSS لإصلاح مشكلة ظهور الشعار في صفحة إعدادات النموذج
        wp_enqueue_style(
            $this->plugin_name . '-logo-fix',
            plugin_dir_url(dirname(__FILE__)) . 'admin/css/logo-fix.css',
            array(),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );

        // تم إزالة تحميل ملف CSS الخاص بإصلاح الشريط الجانبي

        // إضافة CSS لقسم إعدادات اللغة
        wp_enqueue_style(
            $this->plugin_name . '-language-settings',
            plugin_dir_url(dirname(__FILE__)) . 'admin/css/language-settings.css',
            array(),
            $this->version,
            'all'
        );

        // إضافة CSS المخصص لتحسين نافذة تعديل الحقل وإضافة حقل جديد
        wp_enqueue_style(
            $this->plugin_name . '-field-editor-enhanced',
            plugin_dir_url(dirname(__FILE__)) . 'admin/css/field-editor-enhanced.css',
            array(),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            'all'
        );



        // Add WordPress color picker CSS
        wp_enqueue_style('wp-color-picker');

        // تضمين Font Awesome للأيقونات
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css',
            array(),
            '5.15.4',
            'all'
        );

        // إضافة أنماط ميتا بوكس المتغيرات
        $screen = get_current_screen();
        if ($screen && ($screen->id === 'product' || $screen->id === 'edit-product')) {
            wp_enqueue_style(
                $this->plugin_name . '-variation-metabox',
                plugin_dir_url(dirname(__FILE__)) . 'admin/css/variation-metabox.css',
                array(),
                $this->version,
                'all'
            );
        }

        // تحميل أنماط طرق التوصيل في صفحة طرق التوصيل
        if (isset($_GET['page']) && $_GET['page'] === 'pexlat-form-shipping') {
            wp_enqueue_style(
                $this->plugin_name . '-shipping',
                plugin_dir_url(dirname(__FILE__)) . 'admin/css/shipping-zones-admin.css',
                array(),
                $this->version,
                'all'
            );
        }
    }

    /**
     * Register the JavaScript for the admin area.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts() {
        // تحميل مكتبات jQuery UI المطلوبة
        wp_enqueue_script('jquery-ui-core');
        wp_enqueue_script('jquery-ui-draggable');
        wp_enqueue_script('jquery-ui-sortable');
        
        // تحميل مكتبة الوسائط لدعم رفع الصور
        wp_enqueue_media();

        // تحميل سكربت الإدارة الأساسي
        wp_enqueue_script(
            $this->plugin_name,
            plugin_dir_url(dirname(__FILE__)) . 'admin/js/pexlat-form-admin.js',
            array('jquery', 'wp-color-picker', 'jquery-ui-core', 'jquery-ui-draggable', 'jquery-ui-sortable'),
            $this->version,
            false
        );

        // نظام التعديل المدمج الجديد للحقول
        wp_enqueue_script(
            $this->plugin_name . '-inline-field-editor',
            plugin_dir_url(dirname(__FILE__)) . 'admin/js/inline-field-editor.js',
            array('jquery'),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            false
        );

        // سكريبت محرر تخصيص الزر
        wp_enqueue_script(
            $this->plugin_name . '-button-editor',
            plugin_dir_url(dirname(__FILE__)) . 'admin/js/button-editor.js',
            array('jquery', 'wp-color-picker'),
            $this->version,
            false
        );



        // سكريبت إعادة تصميم واجهة الإعدادات
        wp_enqueue_script(
            $this->plugin_name . '-settings-redesign',
            plugin_dir_url(dirname(__FILE__)) . 'admin/js/settings-redesign.js',
            array('jquery', 'jquery-ui-sortable'),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            false
        );

        // سكريبت إصلاح أخطاء JavaScript
        wp_enqueue_script(
            $this->plugin_name . '-admin-fix',
            plugin_dir_url(dirname(__FILE__)) . 'admin/js/pexlat-form-admin-fix.js',
            array('jquery'),
            $this->version . '.' . time(), // إضافة الوقت الحالي لتجنب التخزين المؤقت
            false
        );

        // تم إزالة تحميل ملف JavaScript الخاص بإصلاح الشريط الجانبي

        // سكريبت إعدادات اللغة
        wp_enqueue_script(
            $this->plugin_name . '-language-settings',
            plugin_dir_url(dirname(__FILE__)) . 'admin/js/language-settings.js',
            array('jquery'),
            $this->version,
            false
        );

        // سكريپت معالج الأمان المحسن
        wp_enqueue_script(
            $this->plugin_name . '-security-handler',
            plugin_dir_url(dirname(__FILE__)) . 'admin/js/security-handler.js',
            array('jquery'),
            $this->version,
            false
        );

        // توفير متغيرات PHP لملف JavaScript
        wp_localize_script(
            $this->plugin_name,
            'pexlat_form_admin_vars',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('pexlat_form_nonce')
            )
        );

        // توفير متغيرات لنظام التعديل المدمج
        $form_id = isset($_GET['id']) && is_numeric($_GET['id']) ? intval($_GET['id']) : 0;
        if ($form_id === 0) {
            // محاولة الحصول على النموذج الافتراضي
            $form_id = get_option('pexlat_form_default_form_id', 0);
        }

        wp_localize_script(
            $this->plugin_name . '-inline-field-editor',
            'formElrakamiAdmin',
            array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('pexlat_form_nonce'),
                'formId' => $form_id
            )
        );



        // إضافة سكريبت ميتا بوكس المتغيرات
        $screen = get_current_screen();
        if ($screen && ($screen->id === 'product' || $screen->id === 'edit-product')) {
            // تحميل مكتبة الوسائط
            wp_enqueue_media();
        }

        // تحميل JavaScript لطرق التوصيل في صفحة طرق التوصيل
        if (isset($_GET['page']) && $_GET['page'] === 'pexlat-form-shipping') {
            wp_enqueue_script(
                $this->plugin_name . '-shipping',
                plugin_dir_url(dirname(__FILE__)) . 'admin/js/shipping-zones-admin.js',
                array('jquery'),
                $this->version,
                false
            );

            // توفير متغيرات لـ JavaScript
            wp_localize_script(
                $this->plugin_name . '-shipping',
                'formElrakamAdmin',
                array(
                    'ajax_url' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('pexlat_form_nonce')
                )
            );
        }
    }

    /**
     * Add admin menu pages.
     *
     * @since    1.0.0
     */
    public function add_admin_menu() {
        // Main menu
        add_menu_page(
            'Pexlat Form', // Page title
            'Pexlat Form', // Menu title
            'manage_options', // Capability
            'pexlat-form', // Menu slug
            array($this, 'display_forms_list_page'), // Callback
            plugin_dir_url(dirname(__FILE__)) . 'includes/pexlat-form-logo.png', // استخدام الشعار كأيقونة
            30 // Position
        );

        // Forms list submenu (same as main menu)
        add_submenu_page(
            'pexlat-form', // Parent slug
            'كل النماذج', // Page title
            'كل النماذج', // Menu title
            'manage_options', // Capability
            'pexlat-form', // Menu slug
            array($this, 'display_forms_list_page') // Callback
        );

        // Form editor submenu
        add_submenu_page(
            'pexlat-form', // Parent slug
            'إعدادات النموذج', // Page title
            'إعدادات النموذج', // Menu title
            'manage_options', // Capability
            'pexlat-form-edit', // Menu slug
            array($this, 'display_default_form_editor') // Callback
        );

        // Customers list submenu
        add_submenu_page(
            'pexlat-form', // Parent slug
            'قائمة العملاء', // Page title
            'قائمة العملاء', // Menu title
            'manage_options', // Capability
            'pexlat-form-customers', // Menu slug
            array($this, 'display_customers_page') // Callback
        );

        // Shipping zones submenu
        add_submenu_page(
            'pexlat-form', // Parent slug
            'طرق التوصيل', // Page title
            'طرق التوصيل', // Menu title
            'manage_options', // Capability
            'pexlat-form-shipping', // Menu slug
            array($this, 'display_shipping_zones_page') // Callback
        );

        // Settings submenu
        add_submenu_page(
            'pexlat-form', // Parent slug
            'إعدادات النماذج', // Page title
            'الإعدادات', // Menu title
            'manage_options', // Capability
            'pexlat-form-settings', // Menu slug
            array($this, 'display_settings_page') // Callback
        );

        // Abandoned orders monitor submenu
        add_submenu_page(
            'pexlat-form', // Parent slug
            'مراقب الطلبات المتروكة', // Page title
            'الطلبات المتروكة', // Menu title
            'manage_options', // Capability
            'pexlat-form-abandoned-orders', // Menu slug
            array($this, 'display_abandoned_orders_monitor') // Callback
        );
    }

    /**
     * Display the forms list page.
     *
     * @since    1.0.0
     */
    public function display_forms_list_page() {
        // Handle actions
        if (isset($_GET['action']) && !empty($_GET['id'])) {
            $form_id = intval($_GET['id']);
            $action = sanitize_text_field($_GET['action']);

            if ($action === 'delete' && isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'delete_form_' . $form_id)) {
                $this->delete_form($form_id);
                wp_redirect(admin_url('admin.php?page=pexlat-form&message=1'));
                exit;
            } elseif ($action === 'toggle_status' && isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'toggle_form_status_' . $form_id)) {
                $this->toggle_form_status($form_id);
                wp_redirect(admin_url('admin.php?page=pexlat-form&message=2'));
                exit;
            }
        }

        // Handle default form update
        if (isset($_POST['pexlat_form_update_default_form_nonce']) && wp_verify_nonce($_POST['pexlat_form_update_default_form_nonce'], 'update_default_form')) {
            $default_form_id = intval($_POST['default_form_id']);
            update_option('pexlat_form_default_form_id', $default_form_id);

            add_settings_error(
                'pexlat_form_settings',
                'default_form_updated',
                'تم تحديث النموذج الافتراضي بنجاح.',
                'updated'
            );
        }

        // Load template
        require_once plugin_dir_path(dirname(__FILE__)) . 'admin/partials/forms-list.php';
    }

    /**
     * Display the form editor page.
     *
     * @since    1.0.0
     */
    public function display_default_form_editor() {
        // التحقق مما إذا كان هناك طلب لإنشاء نموذج جديد
        if (isset($_GET['id']) && $_GET['id'] === 'new') {
            // لا تغير المعلمة، اتركها كما هي للسماح بإنشاء نموذج جديد
        } else {
            // إذا لم يتم تحديد النموذج من خلال الرابط، استخدم النموذج الافتراضي
            $default_form_id = get_option('pexlat_form_default_form_id', 0);
            if (!isset($_GET['id']) && $default_form_id > 0) {
                $_GET['id'] = $default_form_id;
            }
        }
        require_once plugin_dir_path(dirname(__FILE__)) . 'admin/partials/form-editor.php';
    }

    /**
     * Display the customers page.
     *
     * @since    1.0.0
     */
    public function display_customers_page() {
        // Load template
        require_once plugin_dir_path(dirname(__FILE__)) . 'admin/partials/customers-list.php';
    }

    /**
     * Display the shipping zones page.
     *
     * @since    1.0.0
     */
    public function display_shipping_zones_page() {
        // التحقق من وجود WooCommerce
        if (!class_exists('WooCommerce')) {
            echo '<div class="notice notice-error"><p><strong>تنبيه:</strong> يتطلب هذا القسم تثبيت وتفعيل إضافة WooCommerce.</p></div>';
            return;
        }

        // إنشاء مثيل من مدير طرق التوصيل
        $shipping_manager = new Pexlat_Form_Shipping_Zones_Manager();

        echo '<div class="wrap">';
        echo '<h1>طرق التوصيل</h1>';
        echo '<p>إدارة شركات الشحن ومناطق التوصيل لـ WooCommerce</p>';

        // عرض صفحة إدارة طرق التوصيل
        $shipping_manager->render_shipping_zones_page();

        echo '</div>';
    }

    /**
     * Display the settings page.
     *
     * @since    1.0.0
     */
    public function display_settings_page() {
        // Get current settings
        $notification_email = get_option('pexlat_form_notification_email', get_option('admin_email'));
        $delete_data = get_option('pexlat_form_delete_data', 0); // Added to get delete data setting

        // Handle settings form submission
        if (isset($_POST['pexlat_form_settings_nonce']) && wp_verify_nonce($_POST['pexlat_form_settings_nonce'], 'pexlat_form_settings')) {
            // Hard-code the value for woo_integration_location since it's now fixed
            update_option('pexlat_form_woo_location', 'short_description');

            if (isset($_POST['notification_email'])) {
                $new_email = sanitize_email($_POST['notification_email']);

                if (!empty($new_email) && is_email($new_email)) {
                    update_option('pexlat_form_notification_email', $new_email);
                    $notification_email = $new_email;

                    // Process other display settings if they exist
                    if (isset($_POST['form_container_style'])) {
                        update_option('pexlat_form_container_style', sanitize_text_field($_POST['form_container_style']));
                    }

                    if (isset($_POST['form_title_text'])) {
                        update_option('pexlat_form_title_text', sanitize_text_field($_POST['form_title_text']));
                    }

                    if (isset($_POST['form_description_text'])) {
                        update_option('pexlat_form_description_text', sanitize_textarea_field($_POST['form_description_text']));
                    }

                    // Update delete data setting
                    if(isset($_POST['pexlat_form_delete_data'])){
                        update_option('pexlat_form_delete_data', intval($_POST['pexlat_form_delete_data']));
                        $delete_data = intval($_POST['pexlat_form_delete_data']);
                    }

                    // تحديث إعدادات التحقق من رقم الهاتف
                    if (isset($_POST['phone_validation_enabled'])) {
                        update_option('pexlat_form_phone_validation_enabled', 1);

                        // تحديث البادئات المسموح بها
                        if (isset($_POST['phone_prefixes'])) {
                            update_option('pexlat_form_phone_prefixes', sanitize_text_field($_POST['phone_prefixes']));
                        }

                        // تحديث طول رقم الهاتف
                        if (isset($_POST['phone_length'])) {
                            update_option('pexlat_form_phone_length', intval($_POST['phone_length']));
                        }

                        // تحديث تخصيص التحقق من رقم الهاتف
                        if (isset($_POST['custom_phone_validation'])) {
                            update_option('pexlat_form_custom_phone_validation', 1);
                        } else {
                            update_option('pexlat_form_custom_phone_validation', 0);
                        }
                    } else {
                        update_option('pexlat_form_phone_validation_enabled', 0);
                    }

                    // تحديث إعدادات منع الطلبات المتكررة
                    if (isset($_POST['limit_orders_enabled'])) {
                        update_option('pexlat_form_limit_orders_enabled', 1);

                        // تحديث الحد الأقصى للطلبات
                        if (isset($_POST['max_orders'])) {
                            update_option('pexlat_form_max_orders', intval($_POST['max_orders']));
                        }

                        // تحديث الفترة الزمنية
                        if (isset($_POST['time_period'])) {
                            update_option('pexlat_form_time_period', intval($_POST['time_period']));
                        }
                    } else {
                        update_option('pexlat_form_limit_orders_enabled', 0);
                    }

                    // تحديث إعدادات منع الإكمال التلقائي
                    if (isset($_POST['disable_autocomplete'])) {
                        update_option('pexlat_form_disable_autocomplete', 1);
                    } else {
                        update_option('pexlat_form_disable_autocomplete', 0);
                    }

                    // تحديث إعدادات منع نسخ ولصق النصوص
                    if (isset($_POST['disable_copy_paste'])) {
                        update_option('pexlat_form_disable_copy_paste', 1);
                    } else {
                        update_option('pexlat_form_disable_copy_paste', 0);
                    }

                    // تحديث إعدادات اللغة
                    if (isset($_POST['pexlat_form_language'])) {
                        update_option('pexlat_form_language', sanitize_text_field($_POST['pexlat_form_language']));
                    }

                    // تحديث إعدادات الطلبات المتروكة
                    if (isset($_POST['save_abandoned_orders'])) {
                        update_option('pexlat_form_save_abandoned_orders', 1);
                    } else {
                        update_option('pexlat_form_save_abandoned_orders', 0);
                    }

                    // تحديث حالة الطلب المتروك
                    if (isset($_POST['abandoned_order_status'])) {
                        update_option('pexlat_form_abandoned_order_status', sanitize_text_field($_POST['abandoned_order_status']));
                    }

                    // تحديث إعدادات السلة
                    if (isset($_POST['cart_system_enabled'])) {
                        update_option('pexlat_form_cart_system_enabled', 1);
                    } else {
                        update_option('pexlat_form_cart_system_enabled', 0);
                    }

                    if (isset($_POST['cart_button_default_text'])) {
                        update_option('pexlat_form_cart_button_default_text', sanitize_text_field($_POST['cart_button_default_text']));
                    }

                    if (isset($_POST['cart_button_default_color'])) {
                        update_option('pexlat_form_cart_button_default_color', sanitize_hex_color($_POST['cart_button_default_color']));
                    }

                    if (isset($_POST['cart_icon_enabled'])) {
                        update_option('pexlat_form_cart_icon_enabled', 1);
                    } else {
                        update_option('pexlat_form_cart_icon_enabled', 0);
                    }

                    if (isset($_POST['cart_auto_open'])) {
                        update_option('pexlat_form_cart_auto_open', 1);
                    } else {
                        update_option('pexlat_form_cart_auto_open', 0);
                    }

                    if (isset($_POST['cart_save_data'])) {
                        update_option('pexlat_form_cart_save_data', 1);
                    } else {
                        update_option('pexlat_form_cart_save_data', 0);
                    }

                    // إعدادات تنسيق زر السلة
                    if (isset($_POST['cart_button_style'])) {
                        update_option('pexlat_form_cart_button_style', sanitize_text_field($_POST['cart_button_style']));
                    }

                    // إعدادات الأيقونة العائمة
                    if (isset($_POST['floating_cart_position'])) {
                        update_option('pexlat_form_floating_cart_position', sanitize_text_field($_POST['floating_cart_position']));
                    }

                    if (isset($_POST['floating_cart_color'])) {
                        update_option('pexlat_form_floating_cart_color', sanitize_hex_color($_POST['floating_cart_color']));
                    }

                    if (isset($_POST['floating_cart_size'])) {
                        update_option('pexlat_form_floating_cart_size', sanitize_text_field($_POST['floating_cart_size']));
                    }

                    if (isset($_POST['floating_cart_shape'])) {
                        update_option('pexlat_form_floating_cart_shape', sanitize_text_field($_POST['floating_cart_shape']));
                    }

                    if (isset($_POST['cart_clear_after_order'])) {
                        update_option('pexlat_form_cart_clear_after_order', 1);
                    } else {
                        update_option('pexlat_form_cart_clear_after_order', 0);
                    }

                    add_settings_error(
                        'pexlat_form_settings',
                        'settings_updated',
                        'تم تحديث الإعدادات بنجاح.',
                        'updated'
                    );
                } else {
                    add_settings_error(
                        'pexlat_form_settings',
                        'invalid_email',
                        'بريد إلكتروني غير صالح. لم يتم تحديث الإعدادات.',
                        'error'
                    );
                }
            }
        }

        // Load template, passing delete_data setting
        require_once plugin_dir_path(dirname(__FILE__)) . 'admin/partials/settings-page.php';
    }

    /**
     * Handle form actions in admin.
     *
     * @since    1.0.0
     */
    public function handle_form_actions() {
        // Handle form save
        if (isset($_POST['pexlat_form_form_nonce']) && wp_verify_nonce($_POST['pexlat_form_form_nonce'], 'pexlat_form_save_form')) {


            // التأكد من أن كل الحقول موجودة قبل الحفظ
            $this->ensure_form_settings($_POST);

            $this->save_form($_POST);
        }
    }

    /**
     * التأكد من وجود كل إعدادات النموذج قبل الحفظ
     * هذه الدالة تتحقق من وجود إعدادات الزر، إعدادات الشريط المثبت، إلخ.
     *
     * @since    1.0.0
     * @param    array    &$data    بيانات النموذج المرجعية لتعديلها
     */
    private function ensure_form_settings(&$data) {
        // إذا لم يتم إرسال إعدادات فارغة، إنشاء مصفوفة فارغة
        if (!isset($data['settings']) || !is_array($data['settings'])) {
            $data['settings'] = array();
        }

        // إعدادات زر النموذج الرئيسي
        $button_settings = array(
            'button_text', 'button_color', 'button_text_color', 'button_border_radius',
            'button_size', 'button_hover_effect', 'button_icon', 'button_icon_position',
            'button_gradient', 'button_gradient_color', 'button_gradient_direction', 'button_animation',
            'show_total_price_in_button'
        );

        // إعدادات زر الواتساب
        $whatsapp_settings = array(
            'enable_whatsapp_button', 'whatsapp_number', 'whatsapp_button_text', 'whatsapp_button_color'
        );

        // إعدادات الشريط المثبت
        $sticky_bar_settings = array(
            'show_sticky_bar', 'sticky_bar_show_product', 'sticky_bar_button_text',
            'sticky_bar_button_icon', 'sticky_bar_button_color', 'sticky_bar_button_text_color',
            'sticky_bar_button_border_radius', 'sticky_bar_button_animation',
            'sticky_bar_button_gradient', 'sticky_bar_button_gradient_color',
            'sticky_bar_button_gradient_direction'
        );

        // إعدادات طرق الشحن
        $shipping_settings = array(
            'shipping_columns', 'shipping_mobile_columns', 'shipping_gap', 'shipping_padding',
            'shipping_border_radius', 'shipping_border_width', 'shipping_bg_color',
            'shipping_border_color', 'shipping_title_color', 'shipping_price_color',
            'shipping_selected_bg_color', 'shipping_selected_border_color', 'shipping_section_title'
        );

        // إعدادات تخطيط النموذج
        $layout_settings = array(
            'fields_layout', 'fields_column_gap', 'fields_height', 'show_quantity_controls',
            'quantity_position', // إضافة إعداد موضع الكمية
            'elements_order', // إضافة إعدادات ترتيب العناصر
            'hide_order_summary', 'summary_collapsed_default' // إعدادات ملخص الطلب
        );

        // إعدادات النظام الموحد الجديد
        $unified_settings = array(
            'unified_theme_enabled', 'color_scheme', 'unified_border_radius'
        );

        // إعدادات الألوان المخصصة
        $custom_color_settings = array(
            'custom_primary', 'custom_primary_hover', 'custom_background',
            'custom_text_primary', 'custom_text_secondary', 'custom_input_background',
            'custom_input_border', 'custom_input_focus', 'custom_border'
        );

        // التأكد من وجود كل الإعدادات
        $all_settings = array_merge($button_settings, $whatsapp_settings, $sticky_bar_settings, $shipping_settings, $layout_settings, $unified_settings, $custom_color_settings);

        // التحقق من إعدادات settings
        foreach ($all_settings as $setting) {
            if (!isset($data['settings'][$setting])) {
                // تسجيل الإعداد المفقود
                error_log('الإعداد المفقود في settings: ' . $setting);
            }
        }

        // التحقق من إعدادات form_settings لإعدادات طرق الشحن
        foreach ($shipping_settings as $setting) {
            if (!isset($data['form_settings'][$setting])) {
                // تسجيل الإعداد المفقود
                error_log('الإعداد المفقود في form_settings: ' . $setting);
            }
        }

        // التأكد من وجود إعدادات ترتيب العناصر
        if (isset($data['settings']['elements_order']) && is_array($data['settings']['elements_order'])) {
            // ترتيب العناصر موجود
        } else {
            // إذا لم يتم إرسال ترتيب العناصر، استخدم الترتيب الافتراضي
            $data['settings']['elements_order'] = array('fields', 'variations', 'shipping', 'quantity', 'button', 'summary');
        }

        // التأكد من وجود إعداد موضع الكمية
        if (!isset($data['settings']['quantity_position'])) {
            $data['settings']['quantity_position'] = 'center';
            error_log('تم استخدام موضع الكمية الافتراضي: center');
        } else {
            error_log('موضع الكمية المستلم: ' . $data['settings']['quantity_position']);
        }
    }

    /**
     * تحديث رؤية الحقل عبر AJAX.
     *
     * @since    1.0.0
     */
    public function update_field_visibility_ajax() {
        // التحقق من الأمان
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pexlat_form_nonce')) {
            wp_send_json_error('فشل التحقق الأمني');
        }

        // التحقق من وجود بيانات مطلوبة
        if (!isset($_POST['form_id']) || !isset($_POST['field_id']) || !isset($_POST['visible'])) {
            wp_send_json_error('البيانات المرسلة غير كاملة');
        }

        // إستخراج البيانات
        $form_id = intval($_POST['form_id']);
        $field_id = sanitize_text_field($_POST['field_id']);
        $visible = ($_POST['visible'] == 1);

        // تحديث حالة الحقل في قاعدة البيانات
        global $wpdb;
        $table_name = $wpdb->prefix . 'pexlat_form_forms';

        // الحصول على بيانات النموذج الحالية
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d", $form_id));

        if (!$form) {
            wp_send_json_error('النموذج غير موجود');
        }

        // تحديث حالة الرؤية للحقل
        $fields = Pexlat_Form_Helper::unserialize_data($form->fields);

        $field_updated = false;
        foreach ($fields as $key => $field) {
            if ($field['id'] === $field_id) {
                $fields[$key]['visible'] = $visible;
                $field_updated = true;
                break;
            }
        }

        if (!$field_updated) {
            wp_send_json_error('الحقل غير موجود في هذا النموذج');
        }

        // حفظ التغييرات
        $wpdb->update(
            $table_name,
            array('fields' => Pexlat_Form_Helper::serialize_data($fields), 'updated_at' => current_time('mysql')),
            array('id' => $form_id),
            array('%s', '%s'),
            array('%d')
        );

        wp_send_json_success(array(
            'message' => 'تم تحديث حالة الحقل بنجاح',
            'field_id' => $field_id,
            'visible' => $visible
        ));
    }

    /**
     * تحديث حالة الإلزامية للحقل عبر AJAX.
     *
     * @since    1.0.0
     */
    public function update_field_required_ajax() {
        // التحقق من الأمان
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pexlat_form_nonce')) {
            wp_send_json_error('فشل التحقق الأمني');
        }

        // التحقق من وجود بيانات مطلوبة
        if (!isset($_POST['form_id']) || !isset($_POST['field_id']) || !isset($_POST['required'])) {
            wp_send_json_error('البيانات المرسلة غير كاملة');
        }

        // إستخراج البيانات
        $form_id = intval($_POST['form_id']);
        $field_id = sanitize_text_field($_POST['field_id']);
        $required = ($_POST['required'] == 1);

        // تحديث حالة الحقل في قاعدة البيانات
        global $wpdb;
        $table_name = $wpdb->prefix . 'pexlat_form_forms';

        // الحصول على بيانات النموذج الحالية
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d", $form_id));

        if (!$form) {
            wp_send_json_error('النموذج غير موجود');
        }

        // تحديث حالة الإلزامية للحقل
        $fields = Pexlat_Form_Helper::unserialize_data($form->fields);

        $field_updated = false;
        foreach ($fields as $key => $field) {
            if ($field['id'] === $field_id) {
                $fields[$key]['required'] = $required;
                $field_updated = true;
                break;
            }
        }

        if (!$field_updated) {
            wp_send_json_error('الحقل غير موجود في هذا النموذج');
        }

        // حفظ التغييرات
        $wpdb->update(
            $table_name,
            array('fields' => Pexlat_Form_Helper::serialize_data($fields), 'updated_at' => current_time('mysql')),
            array('id' => $form_id),
            array('%s', '%s'),
            array('%d')
        );

        wp_send_json_success(array(
            'message' => 'تم تحديث حالة الإلزامية بنجاح',
            'field_id' => $field_id,
            'required' => $required
        ));
    }

    /**
     * تحديث جميع إعدادات الحقل عبر AJAX.
     *
     * @since    1.0.0
     */
    public function update_field_settings_ajax() {
        // التحقق من الأمان
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pexlat_form_nonce')) {
            wp_send_json_error('فشل التحقق الأمني');
        }

        // التحقق من وجود بيانات مطلوبة
        if (!isset($_POST['form_id']) || !isset($_POST['field_id']) || !isset($_POST['field_data'])) {
            wp_send_json_error('البيانات المرسلة غير كاملة');
        }

        // إستخراج البيانات
        $form_id = intval($_POST['form_id']);
        $field_id = sanitize_text_field($_POST['field_id']);
        $field_data = json_decode(stripslashes($_POST['field_data']), true);

        if (!$field_data) {
            wp_send_json_error('بيانات الحقل غير صحيحة');
        }

        // تنظيف بيانات الحقل
        $clean_field_data = array(
            'id' => sanitize_text_field($field_data['id']),
            'label' => sanitize_text_field($field_data['label']),
            'type' => sanitize_text_field($field_data['type']),
            'required' => (bool) $field_data['required'],
            'visible' => (bool) $field_data['visible'],
            'placeholder' => sanitize_text_field($field_data['placeholder']),
            'default_value' => sanitize_text_field($field_data['default_value']),
            'description' => sanitize_textarea_field($field_data['description']),
            'options' => isset($field_data['options']) ? array_map('sanitize_text_field', $field_data['options']) : array()
        );

        // تحديث إعدادات الحقل في قاعدة البيانات
        global $wpdb;
        $table_name = $wpdb->prefix . 'pexlat_form_forms';

        // الحصول على بيانات النموذج الحالية
        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d", $form_id));

        if (!$form) {
            wp_send_json_error('النموذج غير موجود');
        }

        // تحديث إعدادات الحقل
        $fields = Pexlat_Form_Helper::unserialize_data($form->fields);

        $field_updated = false;
        foreach ($fields as $key => $field) {
            if ($field['id'] === $field_id) {
                $fields[$key] = $clean_field_data;
                $field_updated = true;
                break;
            }
        }

        if (!$field_updated) {
            wp_send_json_error('الحقل غير موجود في هذا النموذج');
        }

        // حفظ التغييرات
        $result = $wpdb->update(
            $table_name,
            array('fields' => Pexlat_Form_Helper::serialize_data($fields), 'updated_at' => current_time('mysql')),
            array('id' => $form_id),
            array('%s', '%s'),
            array('%d')
        );

        if ($result === false) {
            wp_send_json_error('فشل في حفظ التغييرات في قاعدة البيانات');
        }

        wp_send_json_success(array(
            'message' => 'تم تحديث جميع إعدادات الحقل بنجاح',
            'field_id' => $field_id,
            'field_data' => $clean_field_data
        ));
    }

    /**
     * Save a form to the database.
     *
     * @since    1.0.0
     * @param    array    $data    The form data.
     */
    private function save_form($data) {
        global $wpdb;

        // تحقق مما إذا كان هذا نموذجًا جديدًا أم لا
        $is_new = (isset($data['form_id']) && empty($data['form_id'])) || (isset($data['form_id']) && $data['form_id'] === 'new');
        // إذا كان نموذجًا جديدًا، اجعل معرف النموذج صفر
        $form_id = $is_new ? 0 : intval($data['form_id']);

        $title = isset($data['form_title']) ? sanitize_text_field($data['form_title']) : '';
        $description = isset($data['form_description']) ? sanitize_textarea_field($data['form_description']) : '';
        $status = isset($data['form_status']) ? sanitize_text_field($data['form_status']) : 'active';

        // Validate data
        if (empty($title)) {
            wp_die('عنوان النموذج مطلوب.', 'خطأ في الحفظ', array('back_link' => true));
        }

        // Process fields - الحقول تُحفظ بنفس الترتيب الموجود في النموذج
        // يتم ترتيب الحقول في واجهة المستخدم باستخدام سحب وإفلات ويتم تحديث المؤشرات تلقائياً
        $fields = array();
        if (isset($data['fields']) && is_array($data['fields'])) {
            // الحفاظ على ترتيب العناصر كما هو في المصفوفة المرسلة من النموذج
            foreach ($data['fields'] as $field) {
                $field_id = sanitize_text_field($field['id']);
                $fields[] = array(
                    'id' => $field_id,
                    'label' => sanitize_text_field($field['label']),
                    'type' => sanitize_text_field($field['type']),
                    'required' => ($field['required'] === '1'),
                    'visible' => ($field['visible'] === '1'),
                    'placeholder' => isset($field['placeholder']) ? sanitize_text_field($field['placeholder']) : '',
                    'default_value' => isset($field['default_value']) ? sanitize_text_field($field['default_value']) : '',
                    'description' => isset($field['description']) ? sanitize_text_field($field['description']) : '',
                    'options' => isset($field['options']) ? array_map('sanitize_text_field', $field['options']) : array(),
                );
            }
        }

        // التأكد من وجود الحقول الإلزامية (الولاية، البلدية، العنوان)
        // ملاحظة: هذه الدالة تضيف الحقول المفقودة فقط ولا تعيد تعيين إعدادات المستخدم
        $this->ensure_required_fields($fields);


        // التأكد من حفظ صفحة التوجيه
        if (isset($data['settings']['redirect_page_id_backup'])) {
            $data['settings']['redirect_page_id'] = intval($data['settings']['redirect_page_id_backup']);
        }

        // إذا تم تحديد صفحة، قم بتخزين الرابط أيضًا للتوافق مع الإصدارات السابقة
        if (isset($data['settings']['redirect_page_id']) && !empty($data['settings']['redirect_page_id'])) {
            $page_id = intval($data['settings']['redirect_page_id']);
            $page_url = get_permalink($page_id);
            if ($page_url) {
                $data['settings']['redirect_url'] = $page_url;
                error_log('تم تحويل معرف الصفحة إلى رابط: ' . $page_url);
            }
        } else {
            // إذا لم يتم تحديد صفحة، تأكد من إزالة أي رابط سابق
            $data['settings']['redirect_url'] = '';
        }

        // Process settings
        $settings = array(
            'button_text' => isset($data['settings']['button_text']) ? sanitize_text_field($data['settings']['button_text']) : 'إتمام الطلب',
            'button_color' => isset($data['settings']['button_color']) ? $data['settings']['button_color'] : '#4CAF50',
            'button_text_color' => isset($data['settings']['button_text_color']) ? $data['settings']['button_text_color'] : '#ffffff',
            'button_border_radius' => isset($data['settings']['button_border_radius']) ? intval($data['settings']['button_border_radius']) : 4,
            'button_size' => isset($data['settings']['button_size']) ? sanitize_text_field($data['settings']['button_size']) : 'medium',
            'button_hover_effect' => isset($data['settings']['button_hover_effect']) ? sanitize_text_field($data['settings']['button_hover_effect']) : 'shadow',
            'button_icon' => isset($data['settings']['button_icon']) ? sanitize_text_field($data['settings']['button_icon']) : 'check',
            'button_icon_position' => isset($data['settings']['button_icon_position']) ? sanitize_text_field($data['settings']['button_icon_position']) : 'right',
            'button_gradient' => isset($data['settings']['button_gradient']) ? sanitize_text_field($data['settings']['button_gradient']) : 'no',
            'button_gradient_color' => isset($data['settings']['button_gradient_color']) ? $data['settings']['button_gradient_color'] : '#38a169',
            'button_gradient_direction' => isset($data['settings']['button_gradient_direction']) ? sanitize_text_field($data['settings']['button_gradient_direction']) : 'to bottom',
            'button_animation' => isset($data['settings']['button_animation']) ? sanitize_text_field($data['settings']['button_animation']) : 'none',
            'show_total_price_in_button' => isset($data['settings']['show_total_price_in_button']) ? 1 : 0,
            'fields_border_radius' => isset($data['settings']['fields_border_radius']) ? intval($data['settings']['fields_border_radius']) : 4,
            'fields_spacing' => isset($data['settings']['fields_spacing']) ? sanitize_text_field($data['settings']['fields_spacing']) : 'medium',
            'fields_bg_color' => isset($data['settings']['fields_bg_color']) ? sanitize_hex_color($data['settings']['fields_bg_color']) : '#f8fafc',
            'fields_border_color' => isset($data['settings']['fields_border_color']) ? sanitize_hex_color($data['settings']['fields_border_color']) : '#e2e8f0',
            'fields_layout' => isset($data['settings']['fields_layout']) ? sanitize_text_field($data['settings']['fields_layout']) : 'vertical',
            'fields_column_gap' => isset($data['settings']['fields_column_gap']) ? sanitize_text_field($data['settings']['fields_column_gap']) : 'medium',
            'fields_height' => isset($data['settings']['fields_height']) ? sanitize_text_field($data['settings']['fields_height']) : 'medium',
            'form_border_style' => isset($data['settings']['form_border_style']) ? sanitize_text_field($data['settings']['form_border_style']) : 'solid',
            'form_border_color' => isset($data['settings']['form_border_color']) ? sanitize_hex_color($data['settings']['form_border_color']) : '#e5e7eb',
            'form_border_width' => isset($data['settings']['form_border_width']) ? intval($data['settings']['form_border_width']) : 1,
            'form_padding' => isset($data['settings']['form_padding']) ? sanitize_text_field($data['settings']['form_padding']) : 'medium',
            'form_margin' => isset($data['settings']['form_margin']) ? sanitize_text_field($data['settings']['form_margin']) : 'medium',
            'card_shadow' => isset($data['settings']['card_shadow']) ? sanitize_text_field($data['settings']['card_shadow']) : 'medium',
            'card_border_radius' => isset($data['settings']['card_border_radius']) ? intval($data['settings']['card_border_radius']) : 8,
            'card_bg_color' => isset($data['settings']['card_bg_color']) ? sanitize_hex_color($data['settings']['card_bg_color']) : '#ffffff',
            'icons_color' => isset($data['settings']['icons_color']) ? sanitize_hex_color($data['settings']['icons_color']) : '#2563eb',
            'icons_position' => isset($data['settings']['icons_position']) ? sanitize_text_field($data['settings']['icons_position']) : 'right',
            'labels_font_weight' => isset($data['settings']['labels_font_weight']) ? sanitize_text_field($data['settings']['labels_font_weight']) : 'bold',
            'text_color' => isset($data['settings']['text_color']) ? sanitize_hex_color($data['settings']['text_color']) : '#1e293b',
            'summary_position' => isset($data['settings']['summary_position']) ? sanitize_text_field($data['settings']['summary_position']) : 'bottom',

            // إعدادات النظام الموحد الجديد
            'unified_theme_enabled' => isset($data['settings']['unified_theme_enabled']) ? sanitize_text_field($data['settings']['unified_theme_enabled']) : 'no',
            'color_scheme' => isset($data['settings']['color_scheme']) ? sanitize_text_field($data['settings']['color_scheme']) : 'blue_professional',
            'unified_border_radius' => isset($data['settings']['unified_border_radius']) ? intval($data['settings']['unified_border_radius']) : 8,
            // إعدادات طرق الشحن
            'shipping_columns' => isset($data['form_settings']['shipping_columns']) ? sanitize_text_field($data['form_settings']['shipping_columns']) : '2',
            'shipping_mobile_columns' => isset($data['form_settings']['shipping_mobile_columns']) ? sanitize_text_field($data['form_settings']['shipping_mobile_columns']) : '1',
            'shipping_gap' => isset($data['form_settings']['shipping_gap']) ? intval($data['form_settings']['shipping_gap']) : 8,
            'shipping_padding' => isset($data['form_settings']['shipping_padding']) ? intval($data['form_settings']['shipping_padding']) : 8,
            'shipping_border_radius' => isset($data['form_settings']['shipping_border_radius']) ? intval($data['form_settings']['shipping_border_radius']) : 8,
            'shipping_border_width' => isset($data['form_settings']['shipping_border_width']) ? intval($data['form_settings']['shipping_border_width']) : 1,
            'shipping_bg_color' => isset($data['form_settings']['shipping_bg_color']) ? sanitize_hex_color($data['form_settings']['shipping_bg_color']) : '#ffffff',
            'shipping_border_color' => isset($data['form_settings']['shipping_border_color']) ? sanitize_hex_color($data['form_settings']['shipping_border_color']) : '#e2e8f0',
            'shipping_title_color' => isset($data['form_settings']['shipping_title_color']) ? sanitize_hex_color($data['form_settings']['shipping_title_color']) : '#1e293b',
            'shipping_price_color' => isset($data['form_settings']['shipping_price_color']) ? sanitize_hex_color($data['form_settings']['shipping_price_color']) : '#2563eb',
            'shipping_selected_bg_color' => isset($data['form_settings']['shipping_selected_bg_color']) ? sanitize_hex_color($data['form_settings']['shipping_selected_bg_color']) : 'rgba(37, 99, 235, 0.05)',
            'shipping_selected_border_color' => isset($data['form_settings']['shipping_selected_border_color']) ? sanitize_hex_color($data['form_settings']['shipping_selected_border_color']) : '#2563eb',
            // إعدادات طرق الشحن الافتراضية
            'default_shipping_1_title' => isset($data['form_settings']['default_shipping_1_title']) ? sanitize_text_field($data['form_settings']['default_shipping_1_title']) : 'توصيل للمنزل',
            'default_shipping_1_cost' => isset($data['form_settings']['default_shipping_1_cost']) ? intval($data['form_settings']['default_shipping_1_cost']) : 800,
            'default_shipping_1_description' => isset($data['form_settings']['default_shipping_1_description']) ? sanitize_text_field($data['form_settings']['default_shipping_1_description']) : '',
            'default_shipping_1_enabled' => isset($data['form_settings']['default_shipping_1_enabled']) ? 1 : 0,
            'default_shipping_2_title' => isset($data['form_settings']['default_shipping_2_title']) ? sanitize_text_field($data['form_settings']['default_shipping_2_title']) : 'توصيل للمكتب',
            'default_shipping_2_cost' => isset($data['form_settings']['default_shipping_2_cost']) ? intval($data['form_settings']['default_shipping_2_cost']) : 600,
            'default_shipping_2_description' => isset($data['form_settings']['default_shipping_2_description']) ? sanitize_text_field($data['form_settings']['default_shipping_2_description']) : '',
            'default_shipping_2_enabled' => isset($data['form_settings']['default_shipping_2_enabled']) ? 1 : 0,

            // إعداد طريقة عرض طرق التوصيل
            'shipping_display_mode' => isset($data['form_settings']['shipping_display_mode']) ? sanitize_text_field($data['form_settings']['shipping_display_mode']) : 'detailed',

            // تسجيل معلومات التصحيح لإعدادات طرق الشحن
            'shipping_debug_info' => 'تم تحديث إعدادات طرق الشحن في ' . date('Y-m-d H:i:s'),
            // إعدادات الشريط المثبت
            'show_sticky_bar' => isset($data['settings']['show_sticky_bar']) ? sanitize_text_field($data['settings']['show_sticky_bar']) : 'yes',
            'sticky_bar_show_product' => isset($data['settings']['sticky_bar_show_product']) ? sanitize_text_field($data['settings']['sticky_bar_show_product']) : 'yes',
            'sticky_bar_button_text' => isset($data['settings']['sticky_bar_button_text']) ? sanitize_text_field($data['settings']['sticky_bar_button_text']) : 'اطلب الآن',
            'sticky_bar_button_icon' => isset($data['settings']['sticky_bar_button_icon']) ? sanitize_text_field($data['settings']['sticky_bar_button_icon']) : 'cart',
            'sticky_bar_button_color' => isset($data['settings']['sticky_bar_button_color']) ? $data['settings']['sticky_bar_button_color'] : '#4CAF50',
            'sticky_bar_button_text_color' => isset($data['settings']['sticky_bar_button_text_color']) ? $data['settings']['sticky_bar_button_text_color'] : '#ffffff',
            'sticky_bar_button_border_radius' => isset($data['settings']['sticky_bar_button_border_radius']) ? intval($data['settings']['sticky_bar_button_border_radius']) : 5,
            'sticky_bar_button_animation' => isset($data['settings']['sticky_bar_button_animation']) ? sanitize_text_field($data['settings']['sticky_bar_button_animation']) : 'none',
            'sticky_bar_button_gradient' => isset($data['settings']['sticky_bar_button_gradient']) ? sanitize_text_field($data['settings']['sticky_bar_button_gradient']) : 'no',
            'sticky_bar_button_gradient_color' => isset($data['settings']['sticky_bar_button_gradient_color']) ? $data['settings']['sticky_bar_button_gradient_color'] : '#38a169',
            'sticky_bar_button_gradient_direction' => isset($data['settings']['sticky_bar_button_gradient_direction']) ? sanitize_text_field($data['settings']['sticky_bar_button_gradient_direction']) : 'to bottom',
            // إعدادات الخيارات الجديدة للشريط المثبت
            'sticky_bar_always_visible' => isset($data['settings']['sticky_bar_always_visible']) ? sanitize_text_field($data['settings']['sticky_bar_always_visible']) : 'no',
            'sticky_bar_button_submit' => isset($data['settings']['sticky_bar_button_submit']) ? sanitize_text_field($data['settings']['sticky_bar_button_submit']) : 'no',
            // إعدادات صفحة التوجيه
            'enable_custom_thankyou' => isset($data['settings']['enable_custom_thankyou']) ? 1 : 0,
            'thankyou_page_id' => isset($data['settings']['thankyou_page_id']) ? intval($data['settings']['thankyou_page_id']) : 0,
            'redirect_page_id' => isset($data['settings']['redirect_page_id']) ? intval($data['settings']['redirect_page_id']) : 0,
            'redirect_url' => isset($data['settings']['redirect_url']) ? esc_url_raw($data['settings']['redirect_url']) : '',
            // إعدادات عنوان قسم طرق التوصيل
            'shipping_section_title' => isset($data['form_settings']['shipping_section_title']) ? sanitize_text_field($data['form_settings']['shipping_section_title']) : 'طرق التوصيل المتاحة',
            // إعدادات إظهار/إخفاء الأيقونات
            'fields_icons_display' => isset($data['settings']['fields_icons_display']) ? sanitize_text_field($data['settings']['fields_icons_display']) : 'show',
            // إعدادات استخدام نفس أسلوب الزر الرئيسي للشريط المثبت
            'use_main_button_settings' => isset($data['settings']['use_main_button_settings']) ? sanitize_text_field($data['settings']['use_main_button_settings']) : 'yes',
            // إعدادات إظهار/إخفاء عنصر الكمية
            'show_quantity_controls' => isset($data['settings']['show_quantity_controls']) ? sanitize_text_field($data['settings']['show_quantity_controls']) : 'show',
            // إعداد موضع عنصر الكمية
            'quantity_position' => isset($data['settings']['quantity_position']) ? sanitize_text_field($data['settings']['quantity_position']) : 'center',
            // إعدادات ترتيب عناصر النموذج
            'elements_order' => isset($data['settings']['elements_order']) ? $data['settings']['elements_order'] : array('fields', 'variations', 'offers', 'shipping', 'quantity', 'button', 'summary'),

            // إعدادات ملخص الطلب
            'hide_order_summary' => isset($data['settings']['hide_order_summary']) ? 1 : 0,
            'summary_collapsed_default' => isset($data['settings']['summary_collapsed_default']) ? 1 : 0,

            // إعدادات زر الواتساب
            'enable_whatsapp_button' => isset($data['settings']['enable_whatsapp_button']) ? 1 : 0, // إذا كان موجوداً، فهو مفعل
            'whatsapp_number' => isset($data['settings']['whatsapp_number']) ? sanitize_text_field($data['settings']['whatsapp_number']) : '',
            'whatsapp_button_text' => isset($data['settings']['whatsapp_button_text']) ? sanitize_text_field($data['settings']['whatsapp_button_text']) : 'طلب عبر الواتساب',
            'whatsapp_button_color' => isset($data['settings']['whatsapp_button_color']) ? sanitize_hex_color($data['settings']['whatsapp_button_color']) : '#25D366',

            // إعدادات الألوان المخصصة
            'custom_primary' => isset($data['settings']['custom_primary']) ? sanitize_hex_color($data['settings']['custom_primary']) : '#2563eb',
            'custom_primary_hover' => isset($data['settings']['custom_primary_hover']) ? sanitize_hex_color($data['settings']['custom_primary_hover']) : '#1d4ed8',
            'custom_background' => isset($data['settings']['custom_background']) ? sanitize_hex_color($data['settings']['custom_background']) : '#ffffff',
            'custom_text_primary' => isset($data['settings']['custom_text_primary']) ? sanitize_hex_color($data['settings']['custom_text_primary']) : '#1e293b',
            'custom_text_secondary' => isset($data['settings']['custom_text_secondary']) ? sanitize_hex_color($data['settings']['custom_text_secondary']) : '#64748b',
            'custom_input_background' => isset($data['settings']['custom_input_background']) ? sanitize_hex_color($data['settings']['custom_input_background']) : '#ffffff',
            'custom_input_border' => isset($data['settings']['custom_input_border']) ? sanitize_hex_color($data['settings']['custom_input_border']) : '#d1d5db',
            'custom_input_focus' => isset($data['settings']['custom_input_focus']) ? sanitize_hex_color($data['settings']['custom_input_focus']) : '#3b82f6',
            'custom_border' => isset($data['settings']['custom_border']) ? sanitize_hex_color($data['settings']['custom_border']) : '#e2e8f0',

            // إعدادات طرق الدفع
            'payment_methods_enabled' => isset($data['settings']['payment_methods_enabled']) ? 1 : 0,
        );

        // إضافة إعدادات طرق الدفع من WooCommerce
        if (class_exists('WooCommerce')) {
            $all_gateways = WC()->payment_gateways->payment_gateways();
            foreach ($all_gateways as $gateway_id => $gateway) {
                $settings['payment_gateway_' . $gateway_id] = isset($data['settings']['payment_gateway_' . $gateway_id]) ? 1 : 0;

                // حفظ صورة شعار طريقة الدفع
                if (isset($data['settings']['payment_logo_' . $gateway_id])) {
                    $settings['payment_logo_' . $gateway_id] = sanitize_url($data['settings']['payment_logo_' . $gateway_id]);
                }
            }
        }

        // إضافة إعداد تخطي صفحة الدفع
        $settings['skip_checkout_page'] = isset($data['settings']['skip_checkout_page']) ? 1 : 0;

        // تسجيل القيمة المحفوظة لطريقة عرض طرق التوصيل
        error_log('القيمة المحفوظة لطريقة عرض طرق التوصيل: ' . $settings['shipping_display_mode']);

        // تسجيل القيمة المحفوظة لموضع الكمية
        error_log('القيمة المحفوظة لموضع الكمية: ' . $settings['quantity_position']);

        // تطبيق مجموعة الألوان إذا كان النظام الموحد مفعلاً
        if (isset($settings['unified_theme_enabled']) && $settings['unified_theme_enabled'] === 'yes') {
            // تسجيل للتصحيح
            error_log('تطبيق النظام الموحد - مجموعة الألوان: ' . ($settings['color_scheme'] ?? 'غير محدد'));
            error_log('الزوايا الموحدة: ' . ($settings['unified_border_radius'] ?? 'غير محدد'));

            // تضمين ملف مجموعات الألوان
            require_once plugin_dir_path(__FILE__) . 'color-schemes.php';

            // تطبيق مجموعة الألوان المختارة
            $color_scheme = isset($settings['color_scheme']) ? $settings['color_scheme'] : 'blue_professional';
            $settings = fe_apply_color_scheme($settings, $color_scheme);

            // تطبيق الزوايا الموحدة
            $unified_radius = isset($settings['unified_border_radius']) ? $settings['unified_border_radius'] : 8;
            $settings['card_border_radius'] = $unified_radius;
            $settings['fields_border_radius'] = $unified_radius;
            $settings['button_border_radius'] = $unified_radius;
            $settings['shipping_border_radius'] = $unified_radius;
            $settings['sticky_bar_button_border_radius'] = $unified_radius;

            error_log('تم تطبيق النظام الموحد بنجاح');
        } else {
            error_log('النظام الموحد غير مفعل أو غير موجود في البيانات');
        }

        // Define table name
        $table_name = $wpdb->prefix . 'pexlat_form_forms';

        // Insert or update
        if ($form_id > 0) {
            // Update existing form
            $wpdb->update(
                $table_name,
                array(
                    'title' => $title,
                    'description' => $description,
                    'fields' => Pexlat_Form_Helper::serialize_data($fields),
                    'settings' => Pexlat_Form_Helper::serialize_data($settings),
                    'status' => $status,
                    'updated_at' => current_time('mysql'),
                ),
                array('id' => $form_id),
                array('%s', '%s', '%s', '%s', '%s', '%s'),
                array('%d')
            );

            $message = 'تم تحديث النموذج بنجاح.';
        } else {
            // Insert new form
            $wpdb->insert(
                $table_name,
                array(
                    'title' => $title,
                    'description' => $description,
                    'fields' => Pexlat_Form_Helper::serialize_data($fields),
                    'settings' => Pexlat_Form_Helper::serialize_data($settings),
                    'status' => $status,
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql'),
                ),
                array('%s', '%s', '%s', '%s', '%s', '%s', '%s')
            );

            $form_id = $wpdb->insert_id;
            $message = 'تم إنشاء النموذج بنجاح.';
        }

        // Redirect to form editor with success message
        wp_redirect(admin_url('admin.php?page=pexlat-form-edit&id=' . $form_id . '&message=1'));
        exit;
    }

    /**
     * Delete a form from the database.
     *
     * @since    1.0.0
     * @param    int    $form_id    The form ID.
     */
    private function delete_form($form_id) {
        global $wpdb;

        // Define table name
        $table_name = $wpdb->prefix . 'pexlat_form_forms';

        // Delete the form
        $wpdb->delete(
            $table_name,
            array('id' => $form_id),
            array('%d')
        );

        // Check if this was the default form and reset if needed
        $default_form_id = get_option('pexlat_form_default_form_id', 0);
        if ($default_form_id == $form_id) {
            // Find another form to set as default or set to 0
            $new_default = $wpdb->get_var("SELECT id FROM $table_name WHERE status = 'active' ORDER BY id DESC LIMIT 1");
            update_option('pexlat_form_default_form_id', $new_default ? $new_default : 0);
        }
    }

    /**
     * Toggle a form's status between active and inactive.
     *
     * @since    1.0.0
     * @param    int    $form_id    The form ID.
     */
    private function toggle_form_status($form_id) {
        global $wpdb;

        // Define table name
        $table_name = $wpdb->prefix . 'pexlat_form_forms';

        // Get current status
        $current_status = $wpdb->get_var($wpdb->prepare("SELECT status FROM $table_name WHERE id = %d", $form_id));

        // Toggle status
        $new_status = ($current_status === 'active') ? 'inactive' : 'active';

        // Update form
        $wpdb->update(
            $table_name,
            array('status' => $new_status),
            array('id' => $form_id),
            array('%s'),
            array('%d')
        );

        // If form is now inactive and was the default form, pick a new default
        if ($new_status === 'inactive') {
            $default_form_id = get_option('pexlat_form_default_form_id', 0);
            if ($default_form_id == $form_id) {
                // Find another form to set as default or set to 0
                $new_default = $wpdb->get_var("SELECT id FROM $table_name WHERE status = 'active' ORDER BY id DESC LIMIT 1");
                update_option('pexlat_form_default_form_id', $new_default ? $new_default : 0);
            }
        }
    }

    /**
     * Add action links on plugins page.
     *
     * @since    1.0.0
     * @param    array    $links    Existing action links.
     * @return   array              Modified action links.
     */
    public function add_action_links($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=pexlat-form') . '">إعدادات</a>';
        array_unshift($links, $settings_link);

        return $links;
    }

    /**
     * التأكد من وجود الحقول الإلزامية (الولاية، البلدية، العنوان)
     * هذه الدالة تتحقق من وجود حقول الولاية والبلدية والعنوان في النموذج،
     * وتضيفها إذا لم تكن موجودة.
     *
     * @since    1.0.0
     * @param    array    &$fields    قائمة الحقول المرجعية لتعديلها
     */
    private function ensure_required_fields(&$fields) {
        // تعريف الحقول الإلزامية
        $required_fields = [
            'state' => [
                'id' => 'state',
                'label' => 'الولاية',
                'type' => 'select',
                'required' => true,
                'visible' => true,
                'placeholder' => 'اختر الولاية',
                'default_value' => '',
                'description' => 'اختر الولاية الخاصة بك',
                'options' => []
            ],
            'municipality' => [
                'id' => 'municipality',
                'label' => 'البلدية',
                'type' => 'select',  // يجب أن يكون من نوع select وليس text
                'required' => true,
                'visible' => true,
                'placeholder' => 'اختر البلدية',
                'default_value' => '',
                'description' => 'اختر البلدية الخاصة بك',
                'options' => []
            ],
            'address' => [
                'id' => 'address',
                'label' => 'العنوان التفصيلي',
                'type' => 'text',
                'required' => true,
                'visible' => true,
                'placeholder' => 'أدخل العنوان التفصيلي',
                'default_value' => '',
                'description' => 'العنوان التفصيلي لتوصيل الطلب',
                'options' => []
            ]
        ];

        // تحقق من وجود كل حقل من الحقول الإلزامية
        foreach ($required_fields as $field_id => $field_data) {
            $field_exists = false;
            $field_needs_correction = false;
            $field_index = -1;

            // البحث عن الحقل في القائمة الحالية
            foreach ($fields as $index => $field) {
                if ($field['id'] === $field_id) {
                    $field_exists = true;
                    $field_index = $index;

                    // تحقق مما إذا كان نوع الحقل صحيحًا (خاصة للبلدية، يجب أن تكون select)
                    if ($field_id === 'municipality' && $field['type'] !== 'select') {
                        $field_needs_correction = true;
                    }

                    break;
                }
            }

            // إذا كان الحقل موجودًا ولكن يحتاج إلى تصحيح
            if ($field_exists && $field_needs_correction) {
                // تصحيح نوع الحقل فقط مع الحفاظ على جميع إعدادات المستخدم
                $fields[$field_index]['type'] = 'select';
                // التأكد من وجود مصفوفة الخيارات
                if (!isset($fields[$field_index]['options'])) {
                    $fields[$field_index]['options'] = [];
                }
            }
            // إضافة الحقل إذا لم يكن موجودًا
            elseif (!$field_exists) {
                $fields[] = $field_data;
            }
        }
    }

    /**
     * معالجة طلب AJAX للحصول على معاينة النموذج
     *
     * هذه الدالة تقوم بإنشاء HTML للنموذج بناءً على البيانات المرسلة من المحرر
     * وإرجاعه للعرض في قوالب المعاينة المختلفة (الحاسوب، الجهاز اللوحي، الهاتف)
     *
     * @since    1.0.0
     */
    public function ajax_get_form_preview() {
        // التحقق من الأمان
        check_ajax_referer('pexlat_form_nonce', 'nonce');


        // التحقق من وجود البيانات المطلوبة
        if (!isset($_POST['form_id']) || !isset($_POST['form_data'])) {
            wp_send_json_error(array('message' => 'البيانات المرسلة غير كاملة'));
            return;
        }

        // استخراج البيانات
        $form_id = intval($_POST['form_id']);
        $form_data_json = $_POST['form_data'];

        // فك تشفير JSON
        $form_data = json_decode(stripslashes($form_data_json), true);

        // التحقق من صحة البيانات المستلمة
        if (json_last_error() !== JSON_ERROR_NONE) {
            wp_send_json_error(array('message' => 'خطأ في تحليل بيانات النموذج: ' . json_last_error_msg()));
            return;
        }

        // إعداد بيانات النموذج بالتنسيق المطلوب للعرض
        $form = array(
            'id' => $form_id,
            'title' => isset($form_data['settings']['form_settings']['title']) ? $form_data['settings']['form_settings']['title'] : 'عنوان النموذج',
            'description' => isset($form_data['settings']['form_settings']['description']) ? $form_data['settings']['form_settings']['description'] : 'وصف النموذج',
            'fields' => isset($form_data['fields']) ? $form_data['fields'] : array(),
            'settings' => isset($form_data['settings']) ? $form_data['settings'] : array(),
        );

        // تحميل قالب النموذج
        ob_start();

        // تعيين متغيرات عالمية لاستخدامها في القالب
        global $pexlat_form_form;
        $pexlat_form_form = $form;

        // تضمين قالب النموذج
        include_once plugin_dir_path(dirname(__FILE__)) . 'public/partials/form-template.php';

        $form_html = ob_get_clean();

        // إرجاع HTML النموذج
        wp_send_json_success(array(
            'html' => $form_html,
            'message' => 'تم إنشاء المعاينة بنجاح'
        ));
    }

    /**
     * إضافة حقل جديد عبر AJAX.
     *
     * @since    1.0.0
     */
    public function add_field_ajax() {
        // التحقق من الأمان
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pexlat_form_nonce')) {
            wp_send_json_error('فشل التحقق الأمني');
        }

        // التحقق من وجود البيانات المطلوبة
        if (!isset($_POST['form_id']) || !isset($_POST['field_data'])) {
            wp_send_json_error('بيانات غير مكتملة');
        }

        $form_id = intval($_POST['form_id']);
        $field_data = json_decode(stripslashes($_POST['field_data']), true);

        // التحقق من صحة معرف النموذج
        global $wpdb;
        $table_name = $wpdb->prefix . 'pexlat_form_forms';

        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d", $form_id));
        if (!$form) {
            wp_send_json_error('معرف النموذج غير صحيح');
        }

        // التحقق من صحة بيانات الحقل
        if (!is_array($field_data) || !isset($field_data['id']) || !isset($field_data['label'])) {
            wp_send_json_error('بيانات الحقل غير صحيحة');
        }

        // تنظيف بيانات الحقل
        $clean_field_data = array(
            'id' => sanitize_text_field($field_data['id']),
            'label' => sanitize_text_field($field_data['label']),
            'type' => sanitize_text_field($field_data['type']),
            'placeholder' => sanitize_text_field($field_data['placeholder'] ?? ''),
            'default_value' => sanitize_text_field($field_data['default_value'] ?? ''),
            'description' => sanitize_textarea_field($field_data['description'] ?? ''),
            'required' => !empty($field_data['required']),
            'visible' => !empty($field_data['visible']),
            'options' => array()
        );

        // تنظيف الخيارات إن وجدت
        if (isset($field_data['options']) && is_array($field_data['options'])) {
            $clean_field_data['options'] = array_map('sanitize_text_field', $field_data['options']);
        }

        // الحصول على الحقول الحالية
        $current_fields = Pexlat_Form_Helper::unserialize_data($form->fields);
        if (!is_array($current_fields)) {
            $current_fields = array();
        }

        // إضافة الحقل الجديد
        $current_fields[] = $clean_field_data;

        // حفظ الحقول المحدثة
        $updated = $wpdb->update(
            $table_name,
            array('fields' => Pexlat_Form_Helper::serialize_data($current_fields), 'updated_at' => current_time('mysql')),
            array('id' => $form_id),
            array('%s', '%s'),
            array('%d')
        );

        if ($updated !== false) {
            wp_send_json_success(array(
                'message' => 'تم إضافة الحقل بنجاح',
                'field_data' => $clean_field_data,
                'total_fields' => count($current_fields)
            ));
        } else {
            wp_send_json_error('فشل في حفظ الحقل الجديد');
        }
    }

    /**
     * حذف الحقل عبر AJAX.
     *
     * @since    1.0.0
     */
    public function delete_field_ajax() {
        // التحقق من الأمان
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pexlat_form_nonce')) {
            wp_send_json_error('فشل التحقق الأمني');
        }

        // التحقق من وجود البيانات المطلوبة
        if (!isset($_POST['field_id']) || !isset($_POST['form_id'])) {
            wp_send_json_error('بيانات غير مكتملة');
        }

        $field_id = sanitize_text_field($_POST['field_id']);
        $form_id = intval($_POST['form_id']);

        // التحقق من صحة معرف النموذج
        global $wpdb;
        $table_name = $wpdb->prefix . 'pexlat_form_forms';

        $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d", $form_id));
        if (!$form) {
            wp_send_json_error('معرف النموذج غير صحيح');
        }

        // الحصول على الحقول الحالية
        $current_fields = Pexlat_Form_Helper::unserialize_data($form->fields);
        if (!is_array($current_fields)) {
            $current_fields = array();
        }

        // البحث عن الحقل وحذفه
        $field_found = false;
        foreach ($current_fields as $index => $field) {
            if (isset($field['id']) && $field['id'] === $field_id) {
                unset($current_fields[$index]);
                $field_found = true;
                break;
            }
        }

        if (!$field_found) {
            wp_send_json_error('الحقل غير موجود');
        }

        // إعادة ترقيم المصفوفة
        $current_fields = array_values($current_fields);

        // حفظ الحقول المحدثة
        $updated = $wpdb->update(
            $table_name,
            array('fields' => Pexlat_Form_Helper::serialize_data($current_fields), 'updated_at' => current_time('mysql')),
            array('id' => $form_id),
            array('%s', '%s'),
            array('%d')
        );

        if ($updated !== false) {
            wp_send_json_success(array(
                'message' => 'تم حذف الحقل بنجاح',
                'field_id' => $field_id,
                'remaining_fields' => count($current_fields)
            ));
        } else {
            wp_send_json_error('فشل في حفظ التغييرات');
        }
    }

    /**
     * تصدير إعدادات النموذج
     */
    public function export_form_settings() {
        // التحقق من الأمان
        if (!current_user_can('manage_options')) {
            error_log('Pexlat Form Export: User does not have manage_options capability');
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء - صلاحيات غير كافية');
            return;
        }

        if (!check_ajax_referer('pexlat_form_save_form', 'nonce', false)) {
            error_log('Pexlat Form Export: Nonce verification failed');
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء - فشل التحقق الأمني');
            return;
        }

        try {
            // تسجيل البيانات المرسلة للتصحيح
            error_log('Pexlat Form Export: POST data: ' . print_r($_POST, true));

            $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;
            if ($form_id <= 0) {
                throw new Exception(__('معرف النموذج غير صحيح', 'pexlat-form'));
            }

            // تحديد البيانات المراد تصديرها
            $export_data = isset($_POST['export_data']) ? $_POST['export_data'] : array();
            if (empty($export_data)) {
                throw new Exception(__('يرجى اختيار البيانات المراد تصديرها', 'pexlat-form'));
            }

            // الحصول على معلومات النموذج الأساسية
            global $wpdb;
            $form_table = $wpdb->prefix . 'pexlat_form_forms';
            $db_form = $wpdb->get_row($wpdb->prepare("SELECT title, description, status FROM {$form_table} WHERE id = %d", $form_id));

            $exported_settings = array(
                'export_info' => array(
                    'plugin_name' => 'Pexlat Form',
                    'plugin_version' => $this->version,
                    'export_date' => current_time('mysql'),
                    'site_url' => get_site_url(),
                    'form_id' => $form_id,
                    'form_title' => $db_form ? $db_form->title : '',
                    'form_description' => $db_form ? $db_form->description : '',
                    'form_status' => $db_form ? $db_form->status : 'active'
                ),
                'data' => array()
            );

            // تصدير معلومات النموذج الأساسية
            if (in_array('form_info', $export_data)) {
                $exported_settings['data']['form_info'] = array(
                    'title' => $db_form ? $db_form->title : '',
                    'description' => $db_form ? $db_form->description : '',
                    'status' => $db_form ? $db_form->status : 'active'
                );
            }

            // تصدير إعدادات النموذج العامة من قاعدة البيانات
            if (in_array('form_settings', $export_data)) {
                global $wpdb;
                $form_table = $wpdb->prefix . 'pexlat_form_forms';
                $db_form = $wpdb->get_row($wpdb->prepare("SELECT settings FROM {$form_table} WHERE id = %d", $form_id));

                $form_settings = array();
                if ($db_form && !empty($db_form->settings)) {
                    if (class_exists('Pexlat_Form_Helper')) {
                        $form_settings = Pexlat_Form_Helper::unserialize_data($db_form->settings);
                    } else {
                        $form_settings = maybe_unserialize($db_form->settings);
                    }
                }
                $exported_settings['data']['form_settings'] = $form_settings;
            }

            // تصدير حقول النموذج من قاعدة البيانات
            if (in_array('form_fields', $export_data)) {
                global $wpdb;
                $form_table = $wpdb->prefix . 'pexlat_form_forms';
                $db_form = $wpdb->get_row($wpdb->prepare("SELECT fields FROM {$form_table} WHERE id = %d", $form_id));

                $form_fields = array();
                if ($db_form && !empty($db_form->fields)) {
                    if (class_exists('Pexlat_Form_Helper')) {
                        $form_fields = Pexlat_Form_Helper::unserialize_data($db_form->fields);
                    } else {
                        $form_fields = maybe_unserialize($db_form->fields);
                    }
                }
                $exported_settings['data']['form_fields'] = $form_fields;
            }

            // تصدير إعدادات طرق التوصيل
            if (in_array('shipping_settings', $export_data)) {
                $shipping_settings = array(
                    'shipping_companies_data' => get_option('pexlat_form_shipping_companies_data', array()),
                    'shipping_class_costs' => get_option('pexlat_form_shipping_class_costs', array()),
                    'shipping_zones_data' => get_option('pexlat_form_shipping_zones_data', array())
                );
                $exported_settings['data']['shipping_settings'] = $shipping_settings;
            }

            // تصدير إعدادات التصميم والألوان
            if (in_array('design_settings', $export_data)) {
                global $wpdb;
                $form_table = $wpdb->prefix . 'pexlat_form_forms';
                $db_form = $wpdb->get_row($wpdb->prepare("SELECT settings FROM {$form_table} WHERE id = %d", $form_id));

                $all_settings = array();
                if ($db_form && !empty($db_form->settings)) {
                    if (class_exists('Pexlat_Form_Helper')) {
                        $all_settings = Pexlat_Form_Helper::unserialize_data($db_form->settings);
                    } else {
                        $all_settings = maybe_unserialize($db_form->settings);
                    }
                }

                // استخراج إعدادات التصميم من الإعدادات العامة
                $design_keys = array(
                    'color_scheme', 'unified_theme_enabled', 'unified_border_radius',
                    'card_shadow', 'card_border_radius', 'card_bg_color',
                    'form_border_style', 'form_border_color', 'form_border_width',
                    'form_padding', 'form_margin', 'fields_bg_color', 'fields_border_color',
                    'fields_layout', 'fields_column_gap', 'icons_position', 'icons_color',
                    'labels_font_weight', 'fields_icons_display'
                );

                $design_settings = array();
                foreach ($design_keys as $key) {
                    if (isset($all_settings[$key])) {
                        $design_settings[$key] = $all_settings[$key];
                    }
                }

                // إضافة CSS مخصص إذا وجد
                $design_settings['custom_css'] = get_option('pexlat_form_custom_css_' . $form_id, '');

                $exported_settings['data']['design_settings'] = $design_settings;
            }

            // تصدير الإعدادات المتقدمة
            if (in_array('advanced_settings', $export_data)) {
                global $wpdb;
                $form_table = $wpdb->prefix . 'pexlat_form_forms';
                $db_form = $wpdb->get_row($wpdb->prepare("SELECT settings FROM {$form_table} WHERE id = %d", $form_id));

                $all_settings = array();
                if ($db_form && !empty($db_form->settings)) {
                    if (class_exists('Pexlat_Form_Helper')) {
                        $all_settings = Pexlat_Form_Helper::unserialize_data($db_form->settings);
                    } else {
                        $all_settings = maybe_unserialize($db_form->settings);
                    }
                }

                // استخراج الإعدادات المتقدمة
                $advanced_keys = array(
                    'summary_position', 'hide_order_summary', 'summary_collapsed_default',
                    'shipping_display_mode', 'form_elements_order', 'quantity_position',
                    'quantity_display', 'redirect_page', 'redirect_url', 'redirect_delay',
                    'language_settings', 'phone_validation_enabled', 'phone_prefixes',
                    'phone_length', 'custom_phone_validation', 'limit_orders_enabled',
                    'max_orders', 'time_period', 'disable_autocomplete', 'disable_copy_paste',
                    'save_abandoned_orders', 'abandoned_order_status'
                );

                $advanced_settings = array();
                foreach ($advanced_keys as $key) {
                    if (isset($all_settings[$key])) {
                        $advanced_settings[$key] = $all_settings[$key];
                    }
                }

                // إضافة الإعدادات العامة من خيارات WordPress
                $advanced_settings['notification_email'] = get_option('pexlat_form_notification_email', '');
                $advanced_settings['delete_data'] = get_option('pexlat_form_delete_data', 0);
                $advanced_settings['language'] = get_option('pexlat_form_language', 'ar');
                $advanced_settings['woo_location'] = get_option('pexlat_form_woo_location', 'short_description');

                $exported_settings['data']['advanced_settings'] = $advanced_settings;
            }

            // إنشاء اسم الملف
            $filename = 'pexlat-form-settings-' . $form_id . '-' . date('Y-m-d-H-i-s') . '.json';

            // إرسال الاستجابة
            wp_send_json_success(array(
                'filename' => $filename,
                'data' => $exported_settings,
                'message' => 'تم تصدير الإعدادات بنجاح'
            ));

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * استيراد إعدادات النموذج
     */
    public function import_form_settings() {
        // التحقق من الأمان
        if (!current_user_can('manage_options') || !check_ajax_referer('pexlat_form_save_form', 'nonce', false)) {
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء');
            return;
        }

        try {
            $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;
            if ($form_id <= 0) {
                throw new Exception(__('معرف النموذج غير صحيح', 'pexlat-form'));
            }

            // التحقق من وجود الملف
            if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception(__('فشل في رفع الملف', 'pexlat-form'));
            }

            $file = $_FILES['import_file'];

            // التحقق من نوع الملف
            if ($file['type'] !== 'application/json' && pathinfo($file['name'], PATHINFO_EXTENSION) !== 'json') {
                throw new Exception(__('نوع الملف غير مدعوم. يرجى رفع ملف JSON فقط', 'pexlat-form'));
            }

            // التحقق من حجم الملف (5MB كحد أقصى)
            if ($file['size'] > 5 * 1024 * 1024) {
                throw new Exception(__('حجم الملف كبير جداً. الحد الأقصى 5MB', 'pexlat-form'));
            }

            // قراءة محتوى الملف
            $file_content = file_get_contents($file['tmp_name']);
            if ($file_content === false) {
                throw new Exception(__('فشل في قراءة الملف', 'pexlat-form'));
            }

            // تحليل JSON
            $imported_data = json_decode($file_content, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception(__('الملف لا يحتوي على JSON صحيح: ', 'pexlat-form') . json_last_error_msg());
            }

            // التحقق من بنية البيانات - يمكن أن تكون البيانات مباشرة أو داخل export_info
            $data = null;
            if (isset($imported_data['export_info']) && isset($imported_data['data'])) {
                // تنسيق قديم مع export_info
                $data = $imported_data['data'];
            } elseif (isset($imported_data['data']) && is_array($imported_data['data'])) {
                // تنسيق جديد - البيانات مباشرة
                $data = $imported_data;
            } else {
                throw new Exception(__('بنية الملف غير صحيحة - لا يمكن العثور على البيانات', 'pexlat-form'));
            }

            // التحقق من إنشاء نسخة احتياطية
            $create_backup = isset($_POST['backup_current']) && $_POST['backup_current'] === 'true';
            if ($create_backup) {
                $this->create_settings_backup($form_id);
            }

            $imported_count = 0;
            // المتغير $data تم تعريفه بالفعل في الأعلى

            // استيراد معلومات النموذج الأساسية
            if (isset($data['form_info']) && !empty($data['form_info'])) {
                global $wpdb;
                $form_table = $wpdb->prefix . 'pexlat_form_forms';

                $form_info = $data['form_info'];
                $update_data = array();

                if (isset($form_info['title'])) {
                    $update_data['title'] = sanitize_text_field($form_info['title']);
                }
                if (isset($form_info['description'])) {
                    $update_data['description'] = sanitize_textarea_field($form_info['description']);
                }
                if (isset($form_info['status'])) {
                    $update_data['status'] = sanitize_text_field($form_info['status']);
                }

                if (!empty($update_data)) {
                    $wpdb->update(
                        $form_table,
                        $update_data,
                        array('id' => $form_id),
                        array('%s', '%s', '%s'),
                        array('%d')
                    );
                    $imported_count++;
                }
            }

            // استيراد إعدادات النموذج العامة إلى قاعدة البيانات
            if (isset($data['form_settings']) && !empty($data['form_settings'])) {
                global $wpdb;
                $form_table = $wpdb->prefix . 'pexlat_form_forms';

                // تحويل البيانات إلى تنسيق قابل للحفظ
                $serialized_settings = maybe_serialize($data['form_settings']);

                $wpdb->update(
                    $form_table,
                    array('settings' => $serialized_settings),
                    array('id' => $form_id),
                    array('%s'),
                    array('%d')
                );
                $imported_count++;
            }

            // استيراد حقول النموذج إلى قاعدة البيانات
            if (isset($data['form_fields']) && !empty($data['form_fields'])) {
                global $wpdb;
                $form_table = $wpdb->prefix . 'pexlat_form_forms';

                // تحويل البيانات إلى تنسيق قابل للحفظ
                $serialized_fields = maybe_serialize($data['form_fields']);

                $wpdb->update(
                    $form_table,
                    array('fields' => $serialized_fields),
                    array('id' => $form_id),
                    array('%s'),
                    array('%d')
                );
                $imported_count++;
            }

            // استيراد إعدادات طرق التوصيل
            if (isset($data['shipping_settings'])) {
                $shipping = $data['shipping_settings'];
                if (isset($shipping['shipping_companies_data'])) {
                    update_option('pexlat_form_shipping_companies_data', $shipping['shipping_companies_data']);
                }
                if (isset($shipping['shipping_class_costs'])) {
                    update_option('pexlat_form_shipping_class_costs', $shipping['shipping_class_costs']);
                }
                if (isset($shipping['shipping_zones_data'])) {
                    update_option('pexlat_form_shipping_zones_data', $shipping['shipping_zones_data']);
                }
                $imported_count++;
            }

            // استيراد إعدادات التصميم والألوان
            if (isset($data['design_settings']) && !empty($data['design_settings'])) {
                global $wpdb;
                $form_table = $wpdb->prefix . 'pexlat_form_forms';

                // الحصول على الإعدادات الحالية
                $db_form = $wpdb->get_row($wpdb->prepare("SELECT settings FROM {$form_table} WHERE id = %d", $form_id));
                $current_settings = array();
                if ($db_form && !empty($db_form->settings)) {
                    if (class_exists('Pexlat_Form_Helper')) {
                        $current_settings = Pexlat_Form_Helper::unserialize_data($db_form->settings);
                    } else {
                        $current_settings = maybe_unserialize($db_form->settings);
                    }
                }

                // دمج إعدادات التصميم مع الإعدادات الحالية
                $design_settings = $data['design_settings'];
                foreach ($design_settings as $key => $value) {
                    if ($key !== 'custom_css') { // CSS مخصص يُحفظ منفصلاً
                        $current_settings[$key] = $value;
                    }
                }

                // حفظ الإعدادات المحدثة
                $serialized_settings = maybe_serialize($current_settings);
                $wpdb->update(
                    $form_table,
                    array('settings' => $serialized_settings),
                    array('id' => $form_id),
                    array('%s'),
                    array('%d')
                );

                // حفظ CSS مخصص منفصلاً
                if (isset($design_settings['custom_css'])) {
                    update_option('pexlat_form_custom_css_' . $form_id, $design_settings['custom_css']);
                }

                $imported_count++;
            }

            // استيراد الإعدادات المتقدمة
            if (isset($data['advanced_settings']) && !empty($data['advanced_settings'])) {
                global $wpdb;
                $form_table = $wpdb->prefix . 'pexlat_form_forms';

                // الحصول على الإعدادات الحالية
                $db_form = $wpdb->get_row($wpdb->prepare("SELECT settings FROM {$form_table} WHERE id = %d", $form_id));
                $current_settings = array();
                if ($db_form && !empty($db_form->settings)) {
                    if (class_exists('Pexlat_Form_Helper')) {
                        $current_settings = Pexlat_Form_Helper::unserialize_data($db_form->settings);
                    } else {
                        $current_settings = maybe_unserialize($db_form->settings);
                    }
                }

                // دمج الإعدادات المتقدمة مع الإعدادات الحالية
                $advanced_settings = $data['advanced_settings'];
                $global_options = array('notification_email', 'delete_data', 'language', 'woo_location');

                foreach ($advanced_settings as $key => $value) {
                    if (in_array($key, $global_options)) {
                        // حفظ الإعدادات العامة في خيارات WordPress
                        update_option('pexlat_form_' . $key, $value);
                    } else {
                        // حفظ الإعدادات الخاصة بالنموذج
                        $current_settings[$key] = $value;
                    }
                }

                // حفظ الإعدادات المحدثة
                $serialized_settings = maybe_serialize($current_settings);
                $wpdb->update(
                    $form_table,
                    array('settings' => $serialized_settings),
                    array('id' => $form_id),
                    array('%s'),
                    array('%d')
                );

                $imported_count++;
            }

            wp_send_json_success(array(
                'message' => "تم استيراد {$imported_count} مجموعة من الإعدادات بنجاح",
                'imported_count' => $imported_count,
                'export_info' => isset($imported_data['export_info']) ? $imported_data['export_info'] : array()
            ));

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * إنشاء نسخة احتياطية من إعدادات النموذج الحالية فقط
     */
    private function create_settings_backup($form_id) {
        global $wpdb;
        $form_table = $wpdb->prefix . 'pexlat_form_forms';
        $db_form = $wpdb->get_row($wpdb->prepare("SELECT title, description, status, settings, fields FROM {$form_table} WHERE id = %d", $form_id));

        $form_settings = array();
        $form_fields = array();

        if ($db_form) {
            if (!empty($db_form->settings)) {
                if (class_exists('Pexlat_Form_Helper')) {
                    $form_settings = Pexlat_Form_Helper::unserialize_data($db_form->settings);
                } else {
                    $form_settings = maybe_unserialize($db_form->settings);
                }
            }

            if (!empty($db_form->fields)) {
                if (class_exists('Pexlat_Form_Helper')) {
                    $form_fields = Pexlat_Form_Helper::unserialize_data($db_form->fields);
                } else {
                    $form_fields = maybe_unserialize($db_form->fields);
                }
            }
        }

        // إنشاء نسخة احتياطية شاملة لإعدادات النموذج فقط
        $backup_data = array(
            'backup_info' => array(
                'form_id' => $form_id,
                'backup_date' => current_time('mysql'),
                'backup_type' => 'auto_before_import',
                'form_title' => $db_form ? $db_form->title : '',
                'plugin_version' => $this->version
            ),
            'data' => array(
                'form_info' => array(
                    'title' => $db_form ? $db_form->title : '',
                    'description' => $db_form ? $db_form->description : '',
                    'status' => $db_form ? $db_form->status : 'active'
                ),
                'form_settings' => $form_settings,
                'form_fields' => $form_fields,
                'design_settings' => $this->extract_design_settings($form_settings),
                'advanced_settings' => $this->extract_advanced_settings($form_settings)
            )
        );

        // حفظ النسخة الاحتياطية
        $backups = get_option('pexlat_form_settings_backups', array());
        $backup_id = 'backup_' . time() . '_' . $form_id;
        $backups[$backup_id] = $backup_data;

        // الاحتفاظ بآخر 10 نسخ احتياطية فقط
        if (count($backups) > 10) {
            $backups = array_slice($backups, -10, null, true);
        }

        update_option('pexlat_form_settings_backups', $backups);

        return $backup_id;
    }

    /**
     * استخراج إعدادات التصميم من الإعدادات العامة
     */
    private function extract_design_settings($all_settings) {
        $design_keys = array(
            'color_scheme', 'unified_theme_enabled', 'unified_border_radius',
            'card_shadow', 'card_border_radius', 'card_bg_color',
            'form_border_style', 'form_border_color', 'form_border_width',
            'form_padding', 'form_margin', 'fields_bg_color', 'fields_border_color',
            'fields_layout', 'fields_column_gap', 'icons_position', 'icons_color',
            'labels_font_weight', 'fields_icons_display', 'fields_border_radius',
            'fields_spacing'
        );

        $design_settings = array();
        if (is_array($all_settings)) {
            foreach ($design_keys as $key) {
                if (isset($all_settings[$key])) {
                    $design_settings[$key] = $all_settings[$key];
                }
            }
        }

        return $design_settings;
    }

    /**
     * استخراج الإعدادات المتقدمة من الإعدادات العامة (خاصة بالنموذج فقط)
     */
    private function extract_advanced_settings($all_settings) {
        $advanced_keys = array(
            'summary_position', 'hide_order_summary', 'summary_collapsed_default',
            'shipping_display_mode', 'form_elements_order', 'quantity_position',
            'quantity_display', 'redirect_page', 'redirect_url', 'redirect_delay',
            'phone_validation_enabled', 'phone_prefixes', 'phone_length',
            'custom_phone_validation', 'limit_orders_enabled', 'max_orders',
            'time_period', 'disable_autocomplete', 'disable_copy_paste',
            'save_abandoned_orders', 'abandoned_order_status', 'button_text',
            'button_color', 'button_gradient', 'button_gradient_color',
            'button_border_radius', 'sticky_bar_button_color', 'sticky_bar_button_gradient',
            'sticky_bar_button_gradient_color', 'enable_whatsapp_button',
            'whatsapp_number', 'whatsapp_button_text', 'whatsapp_button_color'
        );

        $advanced_settings = array();
        if (is_array($all_settings)) {
            foreach ($advanced_keys as $key) {
                if (isset($all_settings[$key])) {
                    $advanced_settings[$key] = $all_settings[$key];
                }
            }
        }

        return $advanced_settings;
    }

    /**
     * جلب قائمة النسخ الاحتياطية
     */
    public function get_settings_backups() {
        // التحقق من الأمان
        if (!current_user_can('manage_options') || !check_ajax_referer('pexlat_form_save_form', 'nonce', false)) {
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء');
            return;
        }

        try {
            $backups = get_option('pexlat_form_settings_backups', array());

            // ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
            uasort($backups, function($a, $b) {
                return strtotime($b['backup_info']['backup_date']) - strtotime($a['backup_info']['backup_date']);
            });

            $formatted_backups = array();
            foreach ($backups as $backup_id => $backup_data) {
                $formatted_backups[] = array(
                    'id' => $backup_id,
                    'date' => $backup_data['backup_info']['backup_date'],
                    'type' => $backup_data['backup_info']['backup_type'],
                    'form_id' => $backup_data['backup_info']['form_id'],
                    'form_title' => isset($backup_data['backup_info']['form_title']) ? $backup_data['backup_info']['form_title'] : '',
                    'formatted_date' => date('Y/m/d H:i', strtotime($backup_data['backup_info']['backup_date']))
                );
            }

            wp_send_json_success($formatted_backups);

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * استعادة نسخة احتياطية من الإعدادات
     */
    public function restore_settings_backup() {
        // التحقق من الأمان
        if (!current_user_can('manage_options')) {
            error_log('Pexlat Form Restore: User does not have manage_options capability');
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء - صلاحيات غير كافية');
            return;
        }

        if (!check_ajax_referer('pexlat_form_save_form', 'nonce', false)) {
            error_log('Pexlat Form Restore: Nonce verification failed');
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء - فشل التحقق الأمني');
            return;
        }

        try {
            $backup_id = isset($_POST['backup_id']) ? sanitize_text_field($_POST['backup_id']) : '';
            $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;

            if (empty($backup_id)) {
                throw new Exception(__('معرف النسخة الاحتياطية غير صحيح', 'pexlat-form'));
            }

            if ($form_id <= 0) {
                throw new Exception(__('معرف النموذج غير صحيح', 'pexlat-form'));
            }

            // الحصول على النسخة الاحتياطية
            $backups = get_option('pexlat_form_settings_backups', array());

            if (!isset($backups[$backup_id])) {
                throw new Exception(__('النسخة الاحتياطية غير موجودة', 'pexlat-form'));
            }

            $backup_data = $backups[$backup_id];

            // التحقق من أن النسخة الاحتياطية تخص نفس النموذج
            if ($backup_data['backup_info']['form_id'] != $form_id) {
                throw new Exception(__('النسخة الاحتياطية لا تخص هذا النموذج', 'pexlat-form'));
            }

            // إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
            $current_backup_id = $this->create_settings_backup($form_id);

            // استعادة الإعدادات من النسخة الاحتياطية
            global $wpdb;
            $form_table = $wpdb->prefix . 'pexlat_form_forms';

            $restored_count = 0;
            $data = $backup_data['data'];

            // استعادة معلومات النموذج الأساسية
            if (isset($data['form_info']) && !empty($data['form_info'])) {
                $form_info = $data['form_info'];
                $update_data = array();

                if (isset($form_info['title'])) {
                    $update_data['title'] = sanitize_text_field($form_info['title']);
                }
                if (isset($form_info['description'])) {
                    $update_data['description'] = sanitize_textarea_field($form_info['description']);
                }
                if (isset($form_info['status'])) {
                    $update_data['status'] = sanitize_text_field($form_info['status']);
                }

                if (!empty($update_data)) {
                    $wpdb->update(
                        $form_table,
                        $update_data,
                        array('id' => $form_id),
                        array('%s', '%s', '%s'),
                        array('%d')
                    );
                    $restored_count++;
                }
            }

            // استعادة إعدادات النموذج (دمج جميع الإعدادات)
            $all_settings = array();

            // دمج الإعدادات الأساسية
            if (isset($data['form_settings']) && is_array($data['form_settings'])) {
                $all_settings = array_merge($all_settings, $data['form_settings']);
            }

            // دمج إعدادات التصميم
            if (isset($data['design_settings']) && is_array($data['design_settings'])) {
                $all_settings = array_merge($all_settings, $data['design_settings']);
            }

            // دمج الإعدادات المتقدمة
            if (isset($data['advanced_settings']) && is_array($data['advanced_settings'])) {
                $all_settings = array_merge($all_settings, $data['advanced_settings']);
            }

            // حفظ الإعدادات المدموجة
            if (!empty($all_settings)) {
                $serialized_settings = maybe_serialize($all_settings);
                $wpdb->update(
                    $form_table,
                    array('settings' => $serialized_settings),
                    array('id' => $form_id),
                    array('%s'),
                    array('%d')
                );
                $restored_count++;
            }

            // استعادة حقول النموذج
            if (isset($data['form_fields']) && !empty($data['form_fields'])) {
                $serialized_fields = maybe_serialize($data['form_fields']);
                $wpdb->update(
                    $form_table,
                    array('fields' => $serialized_fields),
                    array('id' => $form_id),
                    array('%s'),
                    array('%d')
                );
                $restored_count++;
            }

            wp_send_json_success(array(
                'message' => "تم استعادة {$restored_count} مجموعة من الإعدادات بنجاح",
                'restored_count' => $restored_count,
                'backup_date' => $backup_data['backup_info']['backup_date'],
                'current_backup_id' => $current_backup_id
            ));

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * حذف نسخة احتياطية من الإعدادات
     */
    public function delete_settings_backup() {
        // التحقق من الأمان
        if (!current_user_can('manage_options')) {
            error_log('Pexlat Form Delete Backup: User does not have manage_options capability');
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء - صلاحيات غير كافية');
            return;
        }

        if (!check_ajax_referer('pexlat_form_save_form', 'nonce', false)) {
            error_log('Pexlat Form Delete Backup: Nonce verification failed');
            wp_send_json_error('غير مصرح لك بتنفيذ هذا الإجراء - فشل التحقق الأمني');
            return;
        }

        try {
            $backup_id = isset($_POST['backup_id']) ? sanitize_text_field($_POST['backup_id']) : '';

            if (empty($backup_id)) {
                throw new Exception(__('معرف النسخة الاحتياطية غير صحيح', 'pexlat-form'));
            }

            // الحصول على النسخ الاحتياطية
            $backups = get_option('pexlat_form_settings_backups', array());

            if (!isset($backups[$backup_id])) {
                throw new Exception(__('النسخة الاحتياطية غير موجودة', 'pexlat-form'));
            }

            // حذف النسخة الاحتياطية
            $backup_info = $backups[$backup_id]['backup_info'];
            unset($backups[$backup_id]);

            // حفظ القائمة المحدثة
            update_option('pexlat_form_settings_backups', $backups);

            wp_send_json_success(array(
                'message' => __('تم حذف النسخة الاحتياطية بنجاح', 'pexlat-form'),
                'backup_id' => $backup_id,
                'backup_date' => $backup_info['backup_date'],
                'remaining_backups' => count($backups)
            ));

        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }

    /**
     * تجديد رمز الأمان عبر AJAX
     *
     * @since 1.5.1
     */
    public function refresh_nonce_ajax() {
        // تنظيف الكاش المتعلق برموز الأمان
        $this->clear_nonce_cache();

        // إنشاء رمز أمان جديد
        $new_nonce = wp_create_nonce('pexlat_form_nonce');

        if ($new_nonce) {
            // تسجيل عملية التجديد للمراقبة
            error_log('Pexlat Form: تم تجديد رمز الأمان بنجاح - New Nonce: ' . substr($new_nonce, 0, 10) . '...');

            wp_send_json_success(array(
                'nonce' => $new_nonce,
                'message' => 'تم تجديد رمز الأمان بنجاح',
                'timestamp' => current_time('timestamp')
            ));
        } else {
            error_log('Pexlat Form: فشل في إنشاء رمز أمان جديد');
            wp_send_json_error('فشل في إنشاء رمز أمان جديد');
        }
    }

    /**
     * تنظيف الكاش المتعلق برموز الأمان
     *
     * @since 1.5.1
     */
    private function clear_nonce_cache() {
        // تنظيف كاش WordPress
        wp_cache_flush();

        // تنظيف كاش الكائن إذا كان متاحاً
        if (function_exists('wp_cache_delete')) {
            wp_cache_delete('nonce_' . wp_nonce_tick(), 'nonces');
        }

        // تنظيف كاش إضافي للاستضافات التي تستخدم كاش قوي
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }
    }

    /**
     * Display abandoned orders monitor page
     */
    public function display_abandoned_orders_monitor() {
        include_once plugin_dir_path(dirname(__FILE__)) . 'admin/partials/abandoned-orders-monitor.php';
    }
}
