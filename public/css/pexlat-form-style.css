/**
 * Form Pexlat - Main Styling to Match Custom Order Form
 * هذا الملف يحتوي على أنماط CSS للنماذج المعروضة في الواجهة الأمامية للموقع
 * التصميم مستوحى من إضافة custom-order-form
 * تم تعديله لإزالة اعتماد Select2
 */

/* استيراد خط Rubik Arabic من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/**
 * إزالة عالمية وقوية جداً لسهم القوائم المنسدلة من جميع المتصفحات
 * هذه الأنماط تطبق على جميع عناصر select في الموقع
 */

/* إزالة السهم بقوة قصوى من جميع عناصر select */
select,
select:focus,
select:hover,
select:active,
select:visited {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    background-position: unset !important;
    background-repeat: no-repeat !important;
    background-size: 0 !important;
}

/* إزالة السهم من متصفحات Microsoft */
select::-ms-expand {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

select::-ms-value {
    background: transparent !important;
    color: inherit !important;
}

/* إزالة السهم من Firefox بطرق متعددة */
@-moz-document url-prefix() {
    select {
        -moz-appearance: none !important;
        background-image: none !important;
        text-indent: 0.01px !important;
        text-overflow: '' !important;
    }
}

/* إزالة السهم من WebKit browsers (Chrome, Safari, Edge الجديد) */
select::-webkit-appearance {
    -webkit-appearance: none !important;
}

select::-webkit-inner-spin-button,
select::-webkit-outer-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
}

/* إزالة السهم من جميع عناصر select بغض النظر عن الكلاس أو المعرف */
* select,
*:before select,
*:after select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
}

/**
 * متغيرات CSS الرئيسية
 * يمكن تخصيصها من إعدادات الإضافة
 */
:root {
    --form-primary-color: #2563eb;
    --form-primary-rgb: 37, 99, 235; /* قيم RGB للون الرئيسي للاستخدام في الشفافية */
    --form-primary-hover: #1d4ed8;
    --form-error-color: #ef4444;
    --form-error-rgb: 239, 68, 68; /* قيم RGB للون الخطأ */
    --form-border-color: #e2e8f0;
    --form-bg-color: #ffffff;
    --form-text-color: #1e293b;
    --form-placeholder-color: #94a3b8;
    --form-input-bg: #f8fafc;
    --form-input-border: #e2e8f0;
    --form-input-focus-border: #93c5fd;
    --form-label-color: #475569;
    --form-padding: 20px;
    --form-radius: 8px;
    --form-input-radius: 6px;
    --form-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

    /* متغيرات النظام الموحد */
    --unified-border-radius: 8px;
}

/**
 * قواعد النظام الموحد للزوايا
 * عندما يكون النظام الموحد مفعلاً، تستخدم جميع العناصر نفس قيمة الزوايا
 */
.pexlat-form-container[data-unified-theme="true"] {
    /* تطبيق الزوايا الموحدة على جميع العناصر */
    --form-radius: var(--unified-border-radius);
    --form-input-radius: var(--unified-border-radius);
}

.pexlat-form-container[data-unified-theme="true"] .pexlat-form-form,
.pexlat-form-container[data-unified-theme="true"] .pexlat-form-card {
    border-radius: var(--unified-border-radius) !important;
}

.pexlat-form-container[data-unified-theme="true"] input,
.pexlat-form-container[data-unified-theme="true"] textarea,
.pexlat-form-container[data-unified-theme="true"] select,
.pexlat-form-container[data-unified-theme="true"] .input-group {
    border-radius: var(--unified-border-radius) !important;
}

.pexlat-form-container[data-unified-theme="true"] .submit-button,
.pexlat-form-container[data-unified-theme="true"] .form-submit-btn,
.pexlat-form-container[data-unified-theme="true"] button {
    border-radius: var(--unified-border-radius) !important;
}

.pexlat-form-container[data-unified-theme="true"] .shipping-method-item,
.pexlat-form-container[data-unified-theme="true"] .shipping-methods .method-item {
    border-radius: var(--unified-border-radius) !important;
}

.pexlat-form-container[data-unified-theme="true"] .variation-button,
.pexlat-form-container[data-unified-theme="true"] .pexlat-form-variations {
    border-radius: var(--unified-border-radius) !important;
}

/* تطبيق الزوايا الموحدة على عناصر إضافية */
.pexlat-form-container[data-unified-theme="true"] .input-group-text {
    border-radius: var(--unified-border-radius) !important;
}

.pexlat-form-container[data-unified-theme="true"] .quantity-controls,
.pexlat-form-container[data-unified-theme="true"] .quantity-input {
    border-radius: var(--unified-border-radius) !important;
}

.pexlat-form-container[data-unified-theme="true"] .form-summary,
.pexlat-form-container[data-unified-theme="true"] .order-summary {
    border-radius: var(--unified-border-radius) !important;
}

/* تطبيق الزوايا الموحدة على أزرار الكمية */
.pexlat-form-container[data-unified-theme="true"] .quantity-btn,
.pexlat-form-container[data-unified-theme="true"] .qty-btn {
    border-radius: var(--unified-border-radius) !important;
}

/* تطبيق الزوايا الموحدة على عناصر الشحن المتقدمة */
.pexlat-form-container[data-unified-theme="true"] .shipping-option,
.pexlat-form-container[data-unified-theme="true"] .delivery-option {
    border-radius: var(--unified-border-radius) !important;
}


/**
 * هذه المتغيرات سيتم تجاوزها عن طريق الإعدادات المخصصة في وسم style مباشرة في النموذج
 * لا تقم بتعديلها هنا، بل استخدم خيارات النموذج في لوحة التحكم
 */
.pexlat-form-container {
    --form-primary-color: #2563eb;
    --form-primary-rgb: 37, 99, 235; /* قيم RGB للون الرئيسي */
    --form-primary-hover: #1d4ed8;
    --form-bg-color: #ffffff;
    --form-padding: 20px;
}

/**
 * حاوية النموذج الرئيسية
 */
.pexlat-form-container {
    width: 100%;
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    color: var(--form-text-color);
    margin-bottom: 30px;
    direction: rtl;
}

/* تقليل الهوامش الخارجية للنموذج على الهاتف */
@media (max-width: 576px) {
    .pexlat-form-container {
        margin: 0 !important;
        padding: 0 !important;
    }
}

/**
 * نموذج الطلب
 */
.pexlat-form-form {
    background-color: var(--form-bg-color);
    border-radius: var(--form-radius);
    padding: 15px;
    box-shadow: var(--form-shadow);
    max-width: 100%;
    width: 100%;
    margin: 0 auto;
}

/* تقليل الهوامش الداخلية للنموذج على الهاتف */
@media (max-width: 576px) {
    .pexlat-form-form {
        padding: 2px !important;
        border-radius: 6px !important;
    }
}

/**
 * عنوان النموذج
 */
.pexlat-form-form h2 {
    color: var(--form-text-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 0.6rem;
    padding-bottom: 0.4rem;
    border-bottom: 1px solid var(--form-border-color);
    text-align: center;
}

.pexlat-form-form h2 i {
    margin-left: 8px;
    color: var(--form-primary-color);
}

/**
 * وصف النموذج
 */
.pexlat-form-form .pexlat-form-description {
    font-size: 0.95rem;
    margin-bottom: 0.8rem;
    text-align: center;
    color: var(--form-label-color);
}

/**
 * مجموعة الحقول
 */
.pexlat-form-form .form-group {
    margin-bottom: 0.8rem;
    position: relative;
}

/**
 * العناوين
 */
.pexlat-form-form label {
    display: block;
    margin-bottom: 0.3rem;
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--form-label-color);
}

.pexlat-form-form label .required {
    color: var(--form-error-color);
    margin-right: 3px;
}

/**
 * مجموعة الإدخال مع الأيقونات
 */
.pexlat-form-form .input-group {
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;
    margin-bottom: 0.3rem;
    border-radius: var(--form-input-radius, 4px);
    overflow: hidden;
}

.pexlat-form-form .input-group-text {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    font-size: 1rem;
    background-color: var(--form-primary-color) !important;
    color: white !important;
    border: 1px solid var(--form-primary-color) !important;
    transition: all 0.3s ease-in-out !important;
}

/* جعل جميع الأيقونات بيضاء دائماً */
.pexlat-form-form .input-group-text i,
.pexlat-form-form .input-group-text .fas,
.pexlat-form-form .input-group-text .far,
.pexlat-form-form .input-group-text .fab,
.pexlat-form-form .input-group-text .dashicons {
    color: white !important;
}

/* تطبيق لون الأيقونات المخصص عن طريق أسلوب إضافي ذو أولوية أعلى */
.pexlat-form-container .pexlat-form-form .input-group-text {
    background-color: var(--form-primary-color) !important;
    border-color: var(--form-primary-color) !important;
}

/* موضع الأيقونات على اليمين (الافتراضي للعربية) */
.pexlat-form-container.icons-right .input-group-text {
    border-radius: 0 var(--form-input-radius) var(--form-input-radius) 0;
    order: -1;
}

/* موضع الأيقونات على اليسار */
.pexlat-form-container.icons-left .input-group-text {
    border-radius: var(--form-input-radius) 0 0 var(--form-input-radius);
    order: 1;
}

/**
 * حقول الإدخال
 */
.pexlat-form-form input[type="text"],
.pexlat-form-form input[type="email"],
.pexlat-form-form input[type="tel"],
.pexlat-form-form input[type="number"],
.pexlat-form-form textarea,
.pexlat-form-form select {
    display: block;
    width: 100%;
    padding: 0.6rem 0.8rem;
    font-size: 1.05rem;
    line-height: 1.4;
    color: var(--form-text-color);
    background-color: var(--form-input-bg);
    border: 1px solid var(--form-input-border);
    border-radius: var(--form-input-radius);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* حقول الإدخال في مجموعات الإدخال - تخصيص حسب موضع الأيقونات */
.pexlat-form-container.icons-right .pexlat-form-form .input-group input,
.pexlat-form-container.icons-right .pexlat-form-form .input-group select {
    border-radius: var(--form-input-radius) 0 0 var(--form-input-radius);
    text-align: right;
}

.pexlat-form-container.icons-left .pexlat-form-form .input-group input,
.pexlat-form-container.icons-left .pexlat-form-form .input-group select {
    border-radius: 0 var(--form-input-radius) var(--form-input-radius) 0;
    text-align: right;
}

.pexlat-form-form input::placeholder,
.pexlat-form-form textarea::placeholder {
    color: var(--form-placeholder-color);
    opacity: 1;
}

.pexlat-form-form input:focus,
.pexlat-form-form textarea:focus,
.pexlat-form-form select:focus {
    border-color: var(--form-input-focus-border);
    outline: 0;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
}

/**
 * تعديلات خاصة لعناصر القائمة المنسدلة للتأكد من اتساقها مع التصميم
 */
.pexlat-form-form select {
    background-color: var(--form-input-bg);
    cursor: pointer;
    text-align: right;
    padding-right: 1rem;
    direction: rtl;
    /* إزالة السهم الافتراضي */
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
}

/* إزالة السهم من جميع القوائم المنسدلة في النماذج */
.pexlat-form-container select,
.pexlat-form-shortcode-container select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    background: var(--form-input-bg) !important;
}

/* إزالة السهم من متصفح Internet Explorer */
.pexlat-form-container select::-ms-expand,
.pexlat-form-shortcode-container select::-ms-expand {
    display: none !important;
}

/* تخصيص حالات التحويم والتركيز */
.pexlat-form-form select:hover {
    border-color: var(--form-primary-color);
}

/* تأكيد على أن قوائم الولاية والبلدية تظهر بشكل صحيح */
.pexlat-form-form select[name="state"],
.pexlat-form-form select[name="municipality"] {
    background-color: var(--form-input-bg);
    width: 100%;
    height: auto;
    min-height: 2.5rem;
}

/**
 * زر الإرسال
 */

/* الأيقونة في الزر */
.pexlat-form-form .button-icon.left {
    margin-left: 10px;
    margin-right: 10px;
}

.pexlat-form-form .button-icon.right {
    margin-right: 10px;
    margin-left: 10px;
}

/**
 * زر الطلب عبر واتساب
 */
.pexlat-form-form .whatsapp-order-button {
    display: inline-block;
    width: auto;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    font-weight: 600;
    color: white !important;
    background-color: #25D366 !important;
    border: none !important;
    border-radius: var(--form-input-radius);
    cursor: pointer;
    transition: all 0.3s ease-in-out !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    text-align: center;
    margin: 0 5px;
    min-width: 120px;
    height: 40px;
    line-height: 1.2;
}

/**
 * عرض السعر في زر الطلب
 */
.pexlat-form-submit .button-total-price {
    display: inline-block;
    margin-right: 8px;
    font-weight: 700;
}

.pexlat-form-submit .button-total-price .total-price-display {
    font-size: 1.1em;
    font-weight: 800;
    color: inherit;
}

/**
 * عرض السعر في الزر المثبت
 */
.pexlat-form-sticky-bar-button .sticky-button-total-price {
    display: inline-block;
    margin-right: 8px;
    font-weight: 700;
}

.pexlat-form-sticky-bar-button .sticky-button-total-price .total-price-display {
    font-size: 1.1em;
    font-weight: 800;
    color: inherit;
}

.pexlat-form-form .whatsapp-order-button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.pexlat-form-form .whatsapp-order-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

/* تنسيق أيقونة زر الواتساب */
.pexlat-form-form .whatsapp-order-button .button-icon {
    margin-left: 5px;
    font-size: 0.9rem;
}

/**
 * حاوية الأزرار والعناصر الرئيسية - تصميم جديد ومبسط
 */
.pexlat-form-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
    margin: 20px 0;
}

/* الصف الرئيسي - يحتوي على زر الطلب وعنصر الكمية (إذا كان مضمن) */
.pexlat-form-actions .main-action-row {
    display: flex;
    align-items: center;
    gap: 15px;
    width: 100%;
    order: 2;
}

/* الوضع الافتراضي - زر الطلب بعرض كامل */
.pexlat-form-actions:not(.inline-quantity) .main-action-row {
    justify-content: center;
}

.pexlat-form-actions:not(.inline-quantity) .pexlat-form-submit {
    width: 100%;
}

/* الوضع المضمن - زر الطلب + عنصر الكمية في صف واحد */
.pexlat-form-actions.inline-quantity .main-action-row {
    flex-direction: row-reverse;
    justify-content: space-between;
    align-items: center;
}

.pexlat-form-actions.inline-quantity .pexlat-form-submit {
    flex: 1;
    margin-left: 15px;
    height: 48px;
    min-height: 48px;
}

.pexlat-form-actions.inline-quantity .quantity-controls {
    flex-shrink: 0;
    width: auto;
    min-width: 120px;
    height: 48px;
    display: flex;
    align-items: center;
}

/**
 * حاوية الأزرار الإضافية (السلة والواتساب)
 */
.pexlat-form-additional-buttons {
    display: flex;
    gap: 10px;
    width: 100%;
    align-items: stretch;
    order: 3;
}

/* عندما يكون هناك زر واحد فقط */
.pexlat-form-additional-buttons button:only-child {
    width: 100%;
}

/* عندما يكون هناك زرين - يقتسمان المساحة بالتساوي */
.pexlat-form-additional-buttons button:not(:only-child) {
    flex: 1;
}

/**
 * تنسيق الأزرار الإضافية (السلة والواتساب)
 */
.pexlat-form-additional-buttons button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    min-height: 48px;
    padding: 0 20px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    box-sizing: border-box;
    white-space: nowrap;
}

.pexlat-form-additional-buttons button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pexlat-form-additional-buttons button .button-icon {
    margin-left: 8px;
    font-size: 16px;
}

/**
 * تنسيق عنصر الكمية في الوضع المضمن
 */
.pexlat-form-actions.inline-quantity .quantity-controls {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
    height: 48px !important;
    min-height: 48px !important;
    max-height: 48px !important;
}

.pexlat-form-actions.inline-quantity .quantity-controls .quantity-btn {
    height: 46px !important;
    min-height: 46px !important;
    width: 40px;
    border: none;
    background-color: var(--form-input-bg, #e2e8f0);
    color: var(--form-text-color, #4a5568);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pexlat-form-actions.inline-quantity .quantity-controls .quantity-btn:hover {
    background-color: var(--form-primary-color, #cbd5e0);
    color: white;
}

.pexlat-form-actions.inline-quantity .quantity-controls .quantity-input {
    height: 46px !important;
    min-height: 46px !important;
    width: 40px;
    border: none;
    background-color: transparent;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    outline: none;
    -moz-appearance: textfield;
}

.pexlat-form-actions.inline-quantity .quantity-controls .quantity-input::-webkit-outer-spin-button,
.pexlat-form-actions.inline-quantity .quantity-controls .quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/**
 * تنسيق عنصر الكمية في الوسط (فوق زر الطلب)
 */
.pexlat-form-actions .quantity-controls-container {
    margin-bottom: 10px;
    display: flex;
    justify-content: center;
    width: 100%;
    order: 1;
}

.pexlat-form-actions .quantity-controls-container .quantity-controls {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
    height: 48px;
    display: flex;
    align-items: center;
    width: 150px;
}

.pexlat-form-actions .quantity-controls-container .quantity-controls .quantity-btn {
    height: 46px;
    width: 50px;
    border: none;
    background-color: var(--form-input-bg, #e2e8f0);
    color: var(--form-text-color, #4a5568);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pexlat-form-actions .quantity-controls-container .quantity-controls .quantity-btn:hover {
    background-color: var(--form-primary-color, #cbd5e0);
    color: white;
}

.pexlat-form-actions .quantity-controls-container .quantity-controls .quantity-input {
    height: 46px;
    width: 50px;
    border: none;
    background-color: transparent;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    outline: none;
    -moz-appearance: textfield;
}

.pexlat-form-actions .quantity-controls-container .quantity-controls .quantity-input::-webkit-outer-spin-button,
.pexlat-form-actions .quantity-controls-container .quantity-controls .quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/**
 * حاوية رسائل النجاح والخطأ
 */
.pexlat-form-messages-container {
    margin-top: 15px;
    text-align: center;
    width: 100%;
    order: 4;
}

/* تنسيق رسائل النجاح والخطأ */
.pexlat-form-message {
    padding: 12px 15px;
    border-radius: 6px;
    margin: 10px auto;
    font-weight: 500;
    text-align: center;
    display: none;
    max-width: 400px;
    width: 100%;
    box-sizing: border-box;
}

.pexlat-form-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.pexlat-form-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* تحسين عرض الرسائل */
.pexlat-form-message:not(:empty) {
    display: block !important;
}

/* تنسيق رسائل النجاح والخطأ */
.pexlat-form-message {
    padding: 12px 15px;
    border-radius: 6px;
    margin: 10px auto;
    font-weight: 500;
    text-align: center;
    display: none;
    max-width: 400px;
    width: 100%;
    box-sizing: border-box;
}

.pexlat-form-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.pexlat-form-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.pexlat-form-message:not(:empty) {
    display: block !important;
}

/**
 * تحسينات للهواتف المحمولة - تصميم مبسط ونظيف
 */
@media (max-width: 576px) {
    /* تحسين المسافات العامة */
    .pexlat-form-actions {
        gap: 12px;
        margin: 15px 0;
    }

    /* الوضع المضمن - الكمية بجانب زر الطلب حتى على الهواتف */
    .pexlat-form-actions.inline-quantity .main-action-row {
        flex-direction: row-reverse;
        justify-content: space-between;
        align-items: center;
        gap: 10px;
    }

    .pexlat-form-actions.inline-quantity .pexlat-form-submit {
        flex: 1;
        margin-left: 10px;
        height: 50px;
        min-height: 50px;
    }

    .pexlat-form-actions.inline-quantity .quantity-controls {
        flex-shrink: 0;
        width: 120px;
        height: 50px !important;
        min-height: 50px !important;
    }

    .pexlat-form-actions.inline-quantity .quantity-controls .quantity-btn {
        height: 48px !important;
        min-height: 48px !important;
        width: 40px;
        font-size: 16px;
    }

    .pexlat-form-actions.inline-quantity .quantity-controls .quantity-input {
        height: 48px !important;
        min-height: 48px !important;
        width: 40px;
        font-size: 14px;
    }

    /* الأزرار الإضافية - عمودياً بعرض كامل */
    .pexlat-form-additional-buttons {
        flex-direction: column;
        gap: 8px;
    }

    .pexlat-form-additional-buttons button {
        width: 100%;
        height: 50px;
        min-height: 50px;
        font-size: 15px;
        padding: 0 16px;
    }

    /* تحسين عنصر الكمية المنفصل */
    .quantity-controls-container {
        margin: 15px 0;
        display: flex;
        justify-content: center;
    }

    .quantity-controls-container .quantity-controls {
        width: 150px;
        height: 50px;
    }

    /* تحسين أزرار الكمية */
    .quantity-controls .quantity-btn {
        height: 50px;
        width: 50px;
        font-size: 18px;
    }

    .quantity-controls .quantity-input {
        height: 50px;
        font-size: 16px;
        text-align: center;
    }

    /* تحسين الرسائل */
    .pexlat-form-message {
        margin: 8px auto;
        padding: 10px 12px;
        font-size: 14px;
    }

    /* تحسين عنصر الكمية في الوسط على الهواتف */
    .pexlat-form-actions .quantity-controls-container {
        margin-bottom: 8px;
    }

    .pexlat-form-actions .quantity-controls-container .quantity-controls {
        width: 150px;
        height: 50px;
    }

    .pexlat-form-actions .quantity-controls-container .quantity-controls .quantity-btn {
        height: 48px;
        width: 50px;
        font-size: 18px;
    }

    .pexlat-form-actions .quantity-controls-container .quantity-controls .quantity-input {
        height: 48px;
        width: 50px;
        font-size: 16px;
    }
}

/**
 * حاوية أزرار الطلب (للتوافق مع الإصدارات السابقة)
 */
.pexlat-form-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-top: 0.8rem;
}



/**
 * عنصر التنبيه (Toast)
 */
.pexlat-form-toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 20px;
    border-radius: 4px;
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    z-index: 9999;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    display: none;
    text-align: center;
    min-width: 250px;
    max-width: 80%;
}

.pexlat-form-toast.info {
    background-color: #3498db;
}

.pexlat-form-toast.success {
    background-color: #2ecc71;
}

.pexlat-form-toast.warning {
    background-color: #f39c12;
}

.pexlat-form-toast.error {
    background-color: #e74c3c;
}
.pexlat-form-form .pexlat-form-submit {
    display: block;
    width: 100%;
    padding: 1.2rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: white !important;
    background-color: var(--button-color, var(--form-primary-color));
    border: none !important;
    border-radius: var(--form-input-radius);
    cursor: pointer;
    transition: all 0.3s ease-in-out !important;
    margin-top: 1.2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    text-align: center;
    min-height: 60px;
}

/* تطبيق التدرج اللوني للزر */
.pexlat-form-form .pexlat-form-submit.gradient {
    background-image: linear-gradient(var(--button-gradient-direction, to bottom), var(--button-color), var(--button-gradient-color)) !important;
    background-size: 100% !important;
    background-position: center !important;
}

/* أحجام مختلفة للزر */
.pexlat-form-form .pexlat-form-submit.button-size-small {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.pexlat-form-form .pexlat-form-submit.button-size-medium {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.pexlat-form-form .pexlat-form-submit.button-size-large {
    padding: 0.7rem 1.2rem;
    font-size: 0.95rem;
    font-weight: 600;
}

.pexlat-form-form .pexlat-form-submit:hover {
    opacity: 0.9;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.pexlat-form-form .pexlat-form-submit:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

/* تأثيرات حركية للزر */
.pexlat-form-form .pexlat-form-submit.animation-pulse {
    animation: form_button_pulse 1.5s infinite;
}

.pexlat-form-form .pexlat-form-submit.animation-bounce {
    animation: form_button_bounce 1.5s infinite;
}

.pexlat-form-form .pexlat-form-submit.animation-tada {
    animation: form_button_tada 1.5s infinite;
}

.pexlat-form-form .pexlat-form-submit.animation-shake {
    animation: form_button_shake 1.5s infinite;
}

@keyframes form_button_pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes form_button_bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

@keyframes form_button_tada {
    0% { transform: scale(1); }
    10%, 20% { transform: scale(0.9) rotate(-3deg); }
    30%, 50%, 70%, 90% { transform: scale(1.1) rotate(3deg); }
    40%, 60%, 80% { transform: scale(1.1) rotate(-3deg); }
    100% { transform: scale(1) rotate(0); }
}

@keyframes form_button_shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/**
 * خيارات Checkbox و Radio
 */
.pexlat-form-form .checkbox-options,
.pexlat-form-form .radio-options {
    margin-top: 0.5rem;
}

.pexlat-form-form .checkbox-label,
.pexlat-form-form .radio-label {
    display: block;
    margin-bottom: 0.5rem;
    padding-right: 1.5rem;
    position: relative;
    cursor: pointer;
    font-weight: 400;
}

.pexlat-form-form .checkbox-label input,
.pexlat-form-form .radio-label input {
    position: absolute;
    right: 0;
    top: 0.25rem;
}

/**
 * رسائل الخطأ
 */
.pexlat-form-form .field-error {
    color: var(--form-error-color);
    font-size: 0.85rem;
    margin-top: 0.25rem;
}

.pexlat-form-form input.error,
.pexlat-form-form textarea.error,
.pexlat-form-form select.error {
    border-color: var(--form-error-color);
}

/**
 * حاوية رسائل النموذج
 */
.pexlat-form-form .pexlat-form-message {
    margin-top: 1rem;
    padding: 0.75rem;
    border-radius: var(--form-input-radius);
    display: none;
}

.pexlat-form-form .pexlat-form-message.success {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
    display: block;
}

.pexlat-form-form .pexlat-form-message.error {
    background-color: #fee2e2;
    color: #b91c1c;
    border: 1px solid #fecaca;
    display: block;
}

/**
 * المؤشر أثناء الإرسال
 */
.pexlat-form-loading {
    display: flex;
    justify-content: center;
    padding: 1rem 0;
}

.pexlat-form-loading .spinner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-top-color: var(--form-primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/**
 * قسم طرق الشحن
 */
.shipping-methods-container {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border-radius: var(--form-input-radius);
    overflow: hidden;
}

/* رأس قسم طرق التوصيل */
.shipping-methods-header {
    margin-bottom: 1rem;
}

.shipping-methods-header h4 {
    font-size: 0.95rem;
    font-weight: 600;
    margin: 0 0 0.4rem 0;
    color: var(--shipping-methods-title-color, var(--form-text-color));
    display: flex;
    align-items: center;
}

.shipping-methods-header h4 i {
    margin-left: 0.5rem;
    color: var(--form-primary-color);
}

/* حالة التحميل */
.loading-shipping-methods {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background-color: #f8fafc;
    border-radius: var(--form-input-radius);
    border: 1px dashed #cbd5e1;
}

.loading-shipping-methods p {
    margin-top: 0.75rem;
    color: var(--form-label-color);
}

/* خيارات طرق التوصيل - تصميم احترافي جديد */
.shipping-methods-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* ثلاثة أعمدة افتراضيًا للكمبيوتر */
    gap: 12px; /* مسافة مناسبة بين بطاقات الشحن */
    margin: 1.2rem 0;
    width: 100%;
}

/* تخطيط عمود واحد - عند الحاجة لبطاقات أكبر */
.pexlat-form-form.single-column-shipping .shipping-methods-list {
    grid-template-columns: 1fr;
}

/* تخطيط عمودين - الأكثر استخدامًا */
.pexlat-form-form.two-column-shipping .shipping-methods-list {
    grid-template-columns: repeat(2, 1fr);
}

/* تخطيط أربعة أعمدة - للبطاقات الصغيرة جدًا */
.pexlat-form-form.four-column-shipping .shipping-methods-list {
    grid-template-columns: repeat(4, 1fr);
}

/* تجاوب تخطيط الأعمدة مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .shipping-methods-list {
        grid-template-columns: repeat(2, 1fr); /* عمودين افتراضيًا للهواتف */
        gap: 10px; /* مسافة مناسبة بين طرق الشحن */
    }

    /* تطبيق عمودين على جميع الأشكال في وضع الهاتف للاتساق */
    .pexlat-form-form.single-column-shipping .shipping-methods-list,
    .pexlat-form-form.three-column-shipping .shipping-methods-list,
    .pexlat-form-form.four-column-shipping .shipping-methods-list {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* شاشات صغيرة جدًا مثل الهواتف الصغيرة */
@media (max-width: 380px) {
    .shipping-methods-list {
        gap: 8px; /* تقليل المسافة في الشاشات الصغيرة جدًا */
    }
}

/* تصميم احترافي وعملي لبطاقات طرق التوصيل */
.shipping-method-option {
    padding: 12px; /* هوامش داخلية أكبر للمظهر الأفضل */
    border-radius: 8px; /* استدارة زوايا مناسبة للتصميم الحديث */
    background-color: #ffffff; /* خلفية بيضاء ناصعة */
    border: 1px solid #e5e7eb; /* حدود خفيفة */
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03); /* ظل خفيف للعمق */
    overflow: hidden; /* لضمان عدم تجاوز المحتوى */
    min-height: 85px; /* ارتفاع مناسب لمحتوى طريقة الشحن */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* تأثير التحويم */
.shipping-method-option:hover {
    background-color: var(--form-input-bg, #f9fafb); /* خلفية خفيفة عند التحويم */
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.06); /* ظل أكبر قليلاً */
    transform: translateY(-1px); /* ارتفاع طفيف */
    border-color: var(--form-input-border, #d1d5db); /* لون حدود أغمق قليلاً عند التحويم */
}

/* البطاقة المحددة */
.shipping-method-option.selected {
    background-color: rgba(var(--form-primary-rgb, 37, 99, 235), 0.04); /* خلفية خفيفة بلون أساسي */
    border-color: var(--form-primary-color, #3b82f6); /* حدود باللون الأساسي */
    box-shadow: 0 2px 5px rgba(var(--form-primary-rgb, 59, 130, 246), 0.15); /* ظل بلون أساسي خفيف */
    position: relative;
}

/* إضافة مؤشر للعنصر المحدد */
.shipping-method-option.selected:before {
    content: '';
    position: absolute;
    top: 6px;
    right: 6px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--form-primary-color, #3b82f6);
}

/* إخفاء زر الراديو مع الاحتفاظ بإمكانية النقر */
.shipping-method-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    margin: 0;
    cursor: pointer;
    z-index: 2;
}

/* تنظيم محتوى البطاقة */
.shipping-method-details {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 2px 0;
}

/* عنوان طريقة التوصيل بتنسيق أنيق */
.shipping-method-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--form-text-color, #111827); /* لون داكن للعنوان */
    font-size: 0.9rem;
    text-align: center;
    line-height: 1.3;
}

/* وصف طريقة التوصيل خفيف وصغير */
.shipping-method-description {
    font-size: 0.75rem;
    color: var(--form-label-color, #6b7280); /* لون رمادي متوسط */
    margin: 3px 0;
    text-align: center;
    line-height: 1.3;
}

/* سعر طريقة التوصيل بارز وبلون مميز */
.shipping-method-price {
    font-weight: 700;
    color: var(--form-primary-color, #3b82f6); /* لون أساسي للسعر */
    font-size: 1rem;
    margin-top: 7px;
    display: inline-block;
    text-align: center;
    background-color: rgba(var(--form-primary-rgb, 59, 130, 246), 0.06); /* خلفية خفيفة جدًا */
    padding: 2px 8px;
    border-radius: var(--form-input-radius, 4px);
    line-height: 1.4;
}

/* رسائل الخطأ في قسم طرق التوصيل */
.shipping-methods-error,
.shipping-methods-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background-color: var(--form-input-bg, #f8fafc);
    border-radius: var(--form-input-radius);
    border: 1px dashed var(--form-border-color, #cbd5e1);
    text-align: center;
}

.shipping-methods-error i,
.shipping-methods-empty i {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
}

.shipping-methods-error i {
    color: var(--form-error-color);
}

.shipping-methods-empty i {
    color: var(--form-placeholder-color, #94a3b8);
}

.shipping-methods-error p,
.shipping-methods-empty p {
    margin: 0;
    color: var(--form-label-color);
}

/**
 * العنوان
 */
.pexlat-form-address-fields {
    margin-top: 1.5rem;
}

/**
 * إجمالي السعر
 */
.total-price-container {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: #f1f5f9;
    border-radius: var(--form-input-radius);
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.total-price-container h4 {
    font-size: 1.1rem;
    margin: 0.5rem 0;
    color: var(--form-text-color);
    font-weight: 600;
}

.total-price-display {
    font-weight: 700;
    color: var(--form-primary-color);
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

/* تأثير تحديث السعر */
.total-price-display.price-updated {
    color: #10b981;
}

/* تفاصيل تقسيم السعر */
.price-breakdown {
    margin-bottom: 0.8rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.6rem;
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.4rem;
    font-size: 0.85rem;
}

.breakdown-label {
    color: var(--form-label-color);
}

.breakdown-value {
    font-weight: 500;
}

/**
 * شريط الطلب المثبت
 */
.pexlat-form-sticky-bar {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background-color: white !important;
    box-shadow: 0 -3px 8px rgba(0, 0, 0, 0.08) !important;
    border-top: 1px solid var(--form-border-color) !important;
    padding: 8px 15px !important;
    z-index: 999999 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    transition: transform 0.3s ease !important;
    transform: translateY(0) !important; /* الشريط ظاهر دائمًا بشكل افتراضي */
    direction: rtl !important; /* تحديد اتجاه من اليمين لليسار */
    margin: 0 !important; /* إزالة أي هوامش */
}

.pexlat-form-sticky-bar.hidden {
    transform: translateY(100%) !important; /* إخفاء الشريط عند الحاجة */
}

/* تنسيق عنصر المنتج في الشريط السفلي */
.pexlat-form-sticky-bar-product {
    display: flex !important;
    align-items: center !important;
    font-size: 0.9rem !important;
    flex: 1 !important; /* يأخذ المساحة المتاحة */
    justify-content: flex-start !important; /* محاذاة العناصر إلى اليمين */
    margin-left: 15px !important; /* هامش بين معلومات المنتج والزر */
}

.pexlat-form-sticky-bar-product img {
    max-width: 50px !important;
    max-height: 50px !important;
    border-radius: 5px !important;
    margin-left: 10px !important; /* هامش بين الصورة والعنوان يمين الصورة */
    object-fit: contain !important;
}

.pexlat-form-sticky-bar-product-info {
    display: flex !important;
    flex-direction: column !important;
    text-align: right !important;
}

.pexlat-form-sticky-bar-mobile {
    display: none;
    width: 100%;
    padding: 15px;
}

.pexlat-form-sticky-bar-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.pexlat-form-sticky-bar-title {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--form-text-color);
}

.pexlat-form-sticky-bar-price {
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--form-primary-color);
    line-height: 1;
}

.pexlat-form-sticky-bar-button {
    background-color: var(--sticky-button-color, var(--form-primary-color)) !important;
    color: var(--sticky-button-text-color, white) !important;
    padding: 0.7rem 1.2rem !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    border: none !important;
    cursor: pointer !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease !important;
    text-align: center !important;
    text-decoration: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    min-height: 45px !important;
}

.pexlat-form-sticky-bar-button.gradient {
    background-image: linear-gradient(var(--sticky-button-gradient-direction, to bottom), var(--sticky-button-color), var(--sticky-button-gradient-color)) !important;
    background-size: 100% !important;
    background-position: center !important;
}

.pexlat-form-sticky-bar-button:hover,
.pexlat-form-sticky-bar-button:focus {
    transform: translateY(-1px) !important;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15) !important;
    background-color: var(--form-primary-hover) !important;
    opacity: 0.95 !important;
}

.pexlat-form-sticky-bar-button:active {
    transform: translateY(0) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
}

/* إضافة هامش سفلي للجسم لتجنب تداخل المحتوى مع الشريط المثبت */
body {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

/* التأكد من عدم وجود فراغ تحت الشريط */
html, body {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

@media (max-width: 576px) {
    .pexlat-form-sticky-bar {
        padding: 8px 12px !important;
        flex-direction: row !important;
        justify-content: center !important; /* تمركز الزر في المنتصف */
    }

    /* إخفاء صورة واسم المنتج في وضع الهاتف */
    .pexlat-form-sticky-bar-product {
        display: none !important; /* إخفاء تام للصورة واسم المنتج */
    }

    .pexlat-form-sticky-bar-button {
        width: 100% !important; /* الزر يأخذ العرض كاملاً */
        max-width: 250px !important; /* تحديد الحد الأقصى للعرض */
        padding: 0.6rem 1rem !important; /* تقليل الحشو أكثر للهاتف */
        min-height: 42px !important; /* ارتفاع أقل للهاتف */
        font-size: 0.85rem !important; /* حجم خط أصغر للهاتف */
    }

    .pexlat-form-sticky-bar-desktop {
        display: none !important;
    }

    .pexlat-form-sticky-bar-mobile {
        display: flex !important;
        flex-direction: column !important;
        width: 100% !important;
        align-items: center !important; /* توسيط الزر */
    }

    .pexlat-form-sticky-bar-info {
        display: none !important; /* إخفاء معلومات المنتج */
    }
}

/**
 * أنماط وظيفة طي ملخص الطلب
 */
.summary-header {
    cursor: pointer !important;
    user-select: none !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 0.75rem 1rem !important;
    background-color: var(--summary-header-bg, var(--form-input-bg, #f8fafc)) !important;
    border: 1px solid var(--summary-header-border, var(--form-border-color, #e2e8f0)) !important;
    border-radius: var(--form-input-radius) !important;
    margin-bottom: 0.5rem !important;
}

.summary-header:hover {
    opacity: 0.9 !important;
}

.summary-header.collapsed {
    border-bottom-left-radius: var(--form-input-radius) !important;
    border-bottom-right-radius: var(--form-input-radius) !important;
}

.summary-header.expanded {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-bottom: none !important;
}

.summary-header .toggle-icon {
    margin-left: 10px !important;
    margin-right: 10px !important;
    transition: transform 0.3s ease !important;
    color: var(--form-primary-color) !important;
    font-size: 0.9rem !important;
}

.summary-header.expanded .toggle-icon {
    transform: rotate(180deg) !important;
}

/* تحسين مظهر محتوى الملخص - تصميم مبسط */
.order-summary-content,
.summary-content,
.order-summary-details {
    border: none !important;
    border-radius: 0 !important;
    padding: 8px 12px !important;
    background-color: transparent !important;
    margin: 0 !important;
}

/* تحسين للغات الأجنبية */
.pexlat-form-container.lang-fr .summary-header,
.pexlat-form-container.lang-en .summary-header,
.pexlat-form-container.lang-es .summary-header {
    text-align: left !important;
    direction: ltr !important;
}

.pexlat-form-container.lang-fr .summary-header .toggle-icon,
.pexlat-form-container.lang-en .summary-header .toggle-icon,
.pexlat-form-container.lang-es .summary-header .toggle-icon {
    float: left !important;
    margin-left: 0 !important;
    margin-right: 10px !important;
}

/**
 * إزالة شاملة وقوية لسهم القوائم المنسدلة من جميع المتصفحات
 */

/* إزالة السهم بقوة من جميع القوائم المنسدلة */
select,
select:focus,
select:hover,
select:active {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    background: transparent !important;
    border-radius: 0 !important;
}

/* إزالة السهم من متصفح Internet Explorer و Edge */
select::-ms-expand {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

select::-ms-value {
    background-color: transparent !important;
    color: inherit !important;
    border: none !important;
}

/* إزالة السهم من Firefox بطرق متعددة */
select {
    -moz-appearance: textfield !important;
    text-overflow: ellipsis !important;
}

@-moz-document url-prefix() {
    select {
        -moz-appearance: none !important;
        background-image: none !important;
    }
}

/* إزالة السهم من Safari و Chrome بقوة */
select {
    -webkit-appearance: none !important;
    -webkit-border-radius: 0 !important;
}

/* تطبيق قوي جداً على نماذج الإضافة */
.pexlat-form-container select,
.pexlat-form-shortcode-container select,
.pexlat-form-form select,
.pexlat-form-container select:focus,
.pexlat-form-shortcode-container select:focus,
.pexlat-form-form select:focus,
.pexlat-form-container select:hover,
.pexlat-form-shortcode-container select:hover,
.pexlat-form-form select:hover {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    background: var(--form-input-bg, #f8fafc) !important;
    border-radius: var(--form-input-radius, 6px) !important;
}

/* إزالة السهم من جميع حالات القوائم المنسدلة */
.pexlat-form-container select::-ms-expand,
.pexlat-form-shortcode-container select::-ms-expand,
.pexlat-form-form select::-ms-expand {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* إزالة السهم من جميع عناصر select بغض النظر عن الكلاس */
*[class*="pexlat-form"] select,
*[class*="pexlat-form"] select:focus,
*[class*="pexlat-form"] select:hover {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
}

/* إزالة السهم من عناصر select داخل أي حاوي يحتوي على pexlat-form */
div[class*="pexlat-form"] select,
form[class*="pexlat-form"] select,
section[class*="pexlat-form"] select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    background: var(--form-input-bg, #f8fafc) !important;
}

/* إزالة السهم من عناصر select بأسماء محددة */
select[name="state"],
select[name="municipality"],
select[name="country"] {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
}

