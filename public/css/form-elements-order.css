/**
 * تنسيقات CSS لدعم ترتيب عناصر النموذج
 * هذا الملف يحتوي على تنسيقات CSS لدعم ترتيب العناصر المختلفة في النموذج
 */

/* تنسيقات حاوية عنصر اختيار الكمية */
.quantity-controls-container {
    margin: 10px 0;
    display: flex;
    justify-content: center;
}

.quantity-controls-container .quantity-controls {
    display: flex;
    align-items: center;
    background-color: var(--form-input-bg, #f8fafc);
    border: 1px solid var(--form-input-border, #e2e8f0);
    border-radius: var(--form-input-radius, 6px);
    overflow: hidden;
    max-width: 150px;
}

/* تنسيقات ملخص الطلب - تصميم مبسط */
.total-price-container {
    margin: 10px 0;
    padding: 0;
    background-color: transparent;
    border-radius: var(--form-radius, 8px);
    border: 1px solid var(--form-border-color, #e2e8f0);
}

/* تحسين المسافات بين العناصر */
.pexlat-form-form > * + * {
    margin-top: 8px;
}

.pexlat-form-form .form-field + .form-field {
    margin-top: 12px;
}

.pexlat-form-form .quantity-controls-container + .total-price-container {
    margin-top: 8px;
}

.pexlat-form-form .total-price-container + .pexlat-form-actions {
    margin-top: 8px;
}

/* تنسيقات منطقة طرق التوصيل */
.pexlat-form-address-fields {
    margin: 20px 0;
}

/* تنسيقات منطقة زر الطلب */
.pexlat-form-actions {
    margin: 20px 0;
    display: flex;
    flex-direction: row-reverse; /* عكس الترتيب لجعل الكمية على اليمين */
    align-items: center;
    gap: 0px;
    justify-content: space-between;
}

/* تنسيقات للتباعد بين العناصر */
.pexlat-form-form > div {
    margin-bottom: 20px;
}

/* تنسيقات للتباعد بين العناصر في حالة الترتيب المخصص */
.pexlat-form-form > div:last-child {
    margin-bottom: 0;
}

/* تنسيقات للتباعد بين العناصر في حالة الترتيب المخصص */
.pexlat-form-form > div:first-child {
    margin-top: 20px;
}

/* تنسيقات للتباعد بين العناصر في حالة الترتيب المخصص */
.pexlat-form-form h2 + div {
    margin-top: 20px;
}

/* تنسيقات للتباعد بين العناصر في حالة الترتيب المخصص */
.pexlat-form-form .pexlat-form-description + div {
    margin-top: 20px;
}
