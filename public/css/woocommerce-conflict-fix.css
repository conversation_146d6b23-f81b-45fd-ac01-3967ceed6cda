/**
 * حل مشكلة تداخل عناصر ووكومرس مع فورم الطلب المخصص
 * يمنع ظهور فورم الطلب مرتين ويحل مشكلة عدم عمل الفورم
 */

/* إخفاء جدول المتغيرات الأصلي بشكل قوي */
.woocommerce div.product table.variations,
.woocommerce div.product .variations,
.woocommerce div.product .single_variation_wrap,
.woocommerce div.product .woocommerce-variation,
.woocommerce div.product .variations_form,
.woocommerce div.product form.variations_form,
.woocommerce-product-gallery + .summary table.variations,
.woocommerce-product-gallery + .summary .variations,
.woocommerce-product-gallery + .summary .single_variation_wrap,
.woocommerce-product-gallery + .summary .woocommerce-variation,
.woocommerce-product-gallery + .summary .variations_form,
.woocommerce-product-gallery + .summary form.variations_form {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

/* إخفاء فورم إضافة إلى السلة الأصلي */
.woocommerce div.product form.cart:not(.pexlat-form-form),
.woocommerce-product-gallery + .summary form.cart:not(.pexlat-form-form) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
}

/* إخفاء زر إضافة إلى السلة الأصلي */
.woocommerce div.product .single_add_to_cart_button:not(.pexlat-form-submit),
.woocommerce-product-gallery + .summary .single_add_to_cart_button:not(.pexlat-form-submit) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* إخفاء كمية المنتج الأصلية */
.woocommerce div.product form.cart .quantity:not(.pexlat-form-quantity),
.woocommerce-product-gallery + .summary form.cart .quantity:not(.pexlat-form-quantity) {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* إخفاء معلومات المتغيرات الأصلية */
.woocommerce div.product .woocommerce-variation-description,
.woocommerce div.product .woocommerce-variation-price,
.woocommerce div.product .woocommerce-variation-availability,

.woocommerce-product-gallery + .summary .woocommerce-variation-description,
.woocommerce-product-gallery + .summary .woocommerce-variation-price,
.woocommerce-product-gallery + .summary .woocommerce-variation-availability,

    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* إخفاء أي عناصر أخرى قد تتداخل */
.woocommerce div.product .reset_variations,
.woocommerce div.product .clear,
.woocommerce-product-gallery + .summary .reset_variations,
.woocommerce-product-gallery + .summary .clear {
    display: none !important;
}

/* التأكد من ظهور فورم الطلب المخصص بشكل صحيح */
.pexlat-form-form {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    left: auto !important;
    height: auto !important;
    overflow: visible !important;
}

/* إخفاء عناصر ووكومرس في صفحات المنتجات المتغيرة */
.single-product .woocommerce div.product table.variations,
.single-product .woocommerce div.product .variations,
.single-product .woocommerce div.product .single_variation_wrap,
.single-product .woocommerce div.product .woocommerce-variation,
.single-product .woocommerce div.product .variations_form,
.single-product .woocommerce div.product form.variations_form {
    display: none !important;
}

/* إخفاء عناصر ووكومرس في قوالب مختلفة */
.product .variations,
.product table.variations,
.product .single_variation_wrap,
.product .woocommerce-variation,
.product .variations_form,
.product form.variations_form {
    display: none !important;
}

/* حل للقوالب التي تستخدم flexbox أو grid */
.woocommerce div.product .summary .variations,
.woocommerce div.product .summary table.variations,
.woocommerce div.product .summary .single_variation_wrap,
.woocommerce div.product .summary .woocommerce-variation,
.woocommerce div.product .summary .variations_form,
.woocommerce div.product .summary form.variations_form {
    display: none !important;
    flex: none !important;
    grid-area: none !important;
}



/* حل لمشكلة العناصر المخفية التي تظهر مرة أخرى */
.woocommerce div.product .variations[style*="display: block"],
.woocommerce div.product table.variations[style*="display: block"],
.woocommerce div.product .single_variation_wrap[style*="display: block"],
.woocommerce div.product .variations_form[style*="display: block"] {
    display: none !important;
}

/* إخفاء رسائل ووكومرس التي قد تظهر */
.woocommerce-message:not(.pexlat-form-message),
.woocommerce-error:not(.pexlat-form-error),
.woocommerce-info:not(.pexlat-form-info) {
    display: none !important;
}

/* تحسين عرض فورم الطلب المخصص */
.pexlat-form-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 20px 0 !important;
    clear: both !important;
}

/* إخفاء عناصر ووكومرس في الهواتف المحمولة */
@media (max-width: 768px) {
    .woocommerce div.product table.variations,
    .woocommerce div.product .variations,
    .woocommerce div.product .single_variation_wrap,
    .woocommerce div.product .woocommerce-variation,
    .woocommerce div.product .variations_form,
    .woocommerce div.product form.variations_form {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        position: absolute !important;
        left: -9999px !important;
    }
}

/* حل لمشكلة العناصر التي تظهر بعد تحميل الصفحة */
.woocommerce div.product .variations.loaded,
.woocommerce div.product table.variations.loaded,
.woocommerce div.product .single_variation_wrap.loaded,
.woocommerce div.product .variations_form.loaded {
    display: none !important;
}

/* إخفاء عناصر ووكومرس في جميع الحالات */
body.single-product .woocommerce div.product table.variations,
body.single-product .woocommerce div.product .variations,
body.single-product .woocommerce div.product .single_variation_wrap,
body.single-product .woocommerce div.product .woocommerce-variation,
body.single-product .woocommerce div.product .variations_form,
body.single-product .woocommerce div.product form.variations_form {
    display: none !important;
}
