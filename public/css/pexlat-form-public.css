.pexlat-form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
    flex-direction: row-reverse; /* عكس الترتيب لجعل الكمية على اليمين */
}

/* إخفاء عنصر الكمية عند تعطيله */
.pexlat-form-form.hide-quantity .quantity-controls {
    display: none !important;
}

/* تعديل عرض زر الطلب عند إخفاء عنصر الكمية */
.pexlat-form-form.hide-quantity .pexlat-form-actions {
    justify-content: center;
}

.pexlat-form-form.hide-quantity .pexlat-form-submit {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
}

/* إخفاء عنصر الكمية عند تعطيله */
.pexlat-form-form[data-show-quantity="hide"] .quantity-controls {
    display: none;
}

/* تعديل عرض زر الطلب عند إخفاء عنصر الكمية */
.pexlat-form-form[data-show-quantity="hide"] .pexlat-form-submit {
    width: 100%;
    justify-content: center;
}

.pexlat-form-actions.no-quantity .quantity-controls {
    display: none;
}

/* تنسيقات موضع الكمية بجانب زر الطلب */
.pexlat-form-actions.inline-quantity {
    display: flex;
    flex-direction: row-reverse; /* الكمية على اليمين، الزر على اليسار */
    justify-content: stretch;
    align-items: stretch; /* محاذاة متساوية للارتفاع */
    gap: 12px;
    margin-top: 10px;
    width: 100%;
}

.pexlat-form-actions.inline-quantity .quantity-controls {
    order: 2;
    margin: 0;
    flex: 0 0 150px; /* زيادة العرض لإظهار الرقم بوضوح */
    width: 150px;
    height: 100%; /* نفس ارتفاع الحاوي */
    min-height: 50px; /* الحد الأدنى للارتفاع */
    display: flex !important;
    align-items: center;
    border: 1px solid var(--form-input-border, #e2e8f0);
    border-radius: var(--form-input-radius, 6px);
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    background: var(--form-input-bg, #ffffff);
}

.pexlat-form-actions.inline-quantity .pexlat-form-submit {
    order: 1;
    flex: 1; /* يأخذ باقي المساحة المتاحة */
    margin: 0;
    height: 50px !important; /* نفس ارتفاع عنصر الكمية */
    min-height: 50px !important;
    max-height: 50px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 0; /* السماح بالتقلص */
    line-height: 1.2;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 600;
}

/* إخفاء عنصر الكمية المنفصل عندما يكون في الوضع المضمن */
.pexlat-form-form[data-quantity-position="inline"] .quantity-controls-container {
    display: none !important;
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid var(--form-input-border);
    border-radius: var(--form-input-radius);
    overflow: hidden;
    height: 50px;
    min-height: 50px;  /* مطابقة لارتفاع زر الطلب الجديد */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* تحسين أزرار الكمية في الوضع المضمن */
.pexlat-form-actions.inline-quantity .quantity-controls .quantity-btn {
    flex: 0 0 35px; /* عرض أكبر قليلاً للأزرار */
    height: 100%;
    min-width: 35px;
    padding: 0;
    font-size: 16px;
    font-weight: 600;
    background: var(--form-input-bg, #f8fafc);
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pexlat-form-actions.inline-quantity .quantity-controls .quantity-btn:hover {
    background: var(--form-primary-color, #2563eb);
    color: white;
}

.pexlat-form-actions.inline-quantity .quantity-controls .quantity-input {
    flex: 1; /* يأخذ المساحة المتبقية */
    width: auto;
    min-width: 60px; /* عرض أكبر لإظهار الرقم */
    height: 100%;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    border: none;
    border-left: 1px solid var(--form-input-border, #e2e8f0);
    border-right: 1px solid var(--form-input-border, #e2e8f0);
    background: white;
    outline: none;
    color: var(--form-text-color, #1e293b);
}

.pexlat-form-actions.inline-quantity .quantity-controls .quantity-input:focus {
    background: white;
    box-shadow: inset 0 0 0 1px var(--form-primary-color, #2563eb);
}

@media (max-width: 480px) {
    .pexlat-form-actions {
        flex-direction: column-reverse;
        align-items: stretch;
    }

    .pexlat-form-submit {
        width: 100%;
    }

    .quantity-controls {
        width: 100%;
        justify-content: center;
    }

    /* تحسين العرض للموبايل عند استخدام الوضع المضمن */
    .pexlat-form-actions.inline-quantity {
        flex-direction: column-reverse;
        gap: 8px;
        align-items: stretch;
    }

    .pexlat-form-actions.inline-quantity .quantity-controls {
        order: 2;
        width: 100%;
        min-width: auto;
        flex: none;
        justify-content: center;
    }

    .pexlat-form-actions.inline-quantity .pexlat-form-submit {
        order: 1;
        width: 100%;
        max-width: 100%;
        min-width: auto;
        flex: none;
    }

    /* تحسين أزرار الكمية للموبايل */
    .pexlat-form-actions.inline-quantity .quantity-controls .quantity-btn {
        flex: 0 0 40px;
        min-width: 40px;
    }

    .pexlat-form-actions.inline-quantity .quantity-controls .quantity-input {
        min-width: 60px;
    }

    /* تحسين عرض زر السلة للموبايل في الوضع المضمن */
    .pexlat-form-form[data-quantity-position="inline"] .pexlat-form-add-to-cart {
        margin-top: 8px !important;
        width: 100%;
    }
}

.quantity-btn {
    background: var(--form-input-bg);
    border: none;
    padding: 0 15px;
    cursor: pointer;
    color: var(--form-text-color);
    font-size: 16px;
    font-weight: 600;
    transition: all 0.2s ease;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 45px;
}

.quantity-btn:hover {
    background-color: var(--form-primary-color);
    color: white;
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: none;
    border-left: 1px solid var(--form-input-border);
    border-right: 1px solid var(--form-input-border);
    padding: 0;
    height: 100%;
    background: white;
    -moz-appearance: textfield;
    font-size: 16px;
    font-weight: 600;
    color: var(--form-text-color);
}

.quantity-input::-webkit-outer-spin-button,
.quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
/* تنسيقات قسم تفاصيل الأسعار */
.total-price-container {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.price-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.price-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #e2e8f0;
}

.price-row:last-child {
    border-bottom: none;
}

.price-row.total {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 2px solid #e2e8f0;
    font-weight: bold;
    font-size: 1.1em;
    color: #2563eb;
}

.price-label {
    color: #4b5563;
    font-weight: 500;
}

.product-price-display,
.shipping-price-display,
.total-price-display {
    font-weight: 600;
    color: #1a5b8e;
}

/* تنسيق بطاقة التوصيل المجاني */
.free-shipping-badge {
    background-color: #10b981;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
    display: inline-block;
    font-size: 12px;
}

/* تنسيق رسالة اختيار الولاية */
.select-state-message {
    color: #f59e0b;
    font-weight: 500;
    font-size: 12px;
}

/* تنسيق بطاقة العبارة بجانب السعر */
.price-note-badge {
    background-color: #4f46e5;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
    display: inline-block;
    font-size: 12px;
    margin-right: 8px;
    vertical-align: middle;
}

/* تنسيق عرض الكمية بجانب سعر المنتج */
.product-quantity-display {
    display: inline-block;
    font-weight: 500;
    color: #64748b;
    margin-right: 5px;
}

/* تنسيق حاوية سعر المنتج */
.product-price-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

/* تنسيقات رسائل النموذج */
.form-message {
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: 6px;
    font-weight: 500;
    text-align: center;
}

.form-message.success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.form-message.error {
    background-color: #fee2e2;
    color: #b91c1c;
    border: 1px solid #fecaca;
    font-size: 14px;
}

/* تنسيقات رسائل الخطأ للحقول */
.field-error {
    color: #b91c1c;
    font-size: 12px;
    margin-top: 5px;
    font-weight: 500;
}

/* تقليل المسافات بين الأزرار */
.pexlat-form-buttons-container {
    margin-top: 10px !important;
}

.pexlat-form-buttons-container + .pexlat-form-buttons-container {
    margin-top: 8px !important;
}

/* تحسين المسافات للعناصر المتتالية */
.quantity-controls-container + .pexlat-form-actions {
    margin-top: 8px;
}

.pexlat-form-actions + .pexlat-form-buttons-container {
    margin-top: 8px;
}

/* ضمان ظهور زر السلة تحت الأزرار في الوضع المضمن */
.pexlat-form-actions.inline-quantity + .pexlat-form-buttons-container {
    margin-top: 10px;
    order: 999; /* التأكد من ظهوره في النهاية */
}

/* إصلاح ترتيب زر السلة في الوضع المضمن */
.pexlat-form-actions.inline-quantity ~ .pexlat-form-add-to-cart {
    order: 12 !important;
    margin-top: 10px !important;
}

/* ضمان ظهور زر السلة بعد زر الطلب مباشرة */
.pexlat-form-actions.inline-quantity .pexlat-form-add-to-cart {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    margin-top: 10px;
    order: 999;
}

/* تحسين ترتيب العناصر عندما تكون الكمية مضمنة */
.pexlat-form-form[data-quantity-position="inline"] {
    display: flex;
    flex-direction: column;
}

.pexlat-form-form[data-quantity-position="inline"] .pexlat-form-actions.inline-quantity {
    order: 10; /* زر الطلب مع الكمية */
    position: relative;
}

.pexlat-form-form[data-quantity-position="inline"] .pexlat-form-add-to-cart {
    order: 11 !important; /* زر السلة بعد زر الطلب */
    margin-top: 10px !important;
}

.pexlat-form-form[data-quantity-position="inline"] .pexlat-form-buttons-container {
    order: 12; /* باقي الأزرار */
}

/* تنسيقات رسالة الحظر */
.form-message.blocked-message {
    background-color: #fee2e2;
    color: #b91c1c;
    border: 1px solid #fecaca;
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    font-size: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}