/**
 * إصلاح نهائي وشامل للوضع المظلم
 * حل جذري لمشكلة العناصر البيضاء في الوضع المظلم
 */

/* إصلاح شامل للشريط العلوي - أقوى إصلاح ممكن */
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header {
    background-color: #374151 !important;
    background: #374151 !important;
    background-image: none !important;
    color: #f9fafb !important;
    border-color: #4b5563 !important;
    border: 1px solid #4b5563 !important;
}

/* إصلاح جميع العناصر الفرعية للشريط العلوي */
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header *,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header > *,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header div,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header span,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header h1,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header h2,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header h3,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header h4,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header h5,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header h6,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header p {
    background-color: transparent !important;
    background: transparent !important;
    color: inherit !important;
}

/* إصلاح شامل لطرق التوصيل غير المحددة */
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option:not(.selected) {
    background-color: #374151 !important;
    background: #374151 !important;
    background-image: none !important;
    color: #f9fafb !important;
    border-color: #4b5563 !important;
    border: 1px solid #4b5563 !important;
}

/* إصلاح جميع العناصر الفرعية لطرق التوصيل */
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option *,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option > *,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option div,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option span,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option h1,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option h2,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option h3,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option h4,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option h5,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option h6,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option p {
    background-color: transparent !important;
    background: transparent !important;
    color: inherit !important;
}

/* إصلاح للعناصر المحددة */
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option.selected {
    background-color: rgba(59, 130, 246, 0.15) !important;
    background: rgba(59, 130, 246, 0.15) !important;
    background-image: none !important;
    border-color: #3b82f6 !important;
    border: 2px solid #3b82f6 !important;
    color: #f9fafb !important;
}

/* إصلاح للعناصر عند التحويم */
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option:hover:not(.selected) {
    background-color: #4b5563 !important;
    background: #4b5563 !important;
    background-image: none !important;
    border-color: #6b7280 !important;
    color: #f9fafb !important;
}

/* إصلاح للعناصر التي تحتوي على أنماط مضمنة */
.pexlat-form-container[data-color-scheme="dark_mode"] [style*="background"] {
    background-color: #374151 !important;
    background: #374151 !important;
    color: #f9fafb !important;
}

/* إصلاح للعناصر التي تحتوي على فئات CSS محددة */
.pexlat-form-container[data-color-scheme="dark_mode"] .bg-white,
.pexlat-form-container[data-color-scheme="dark_mode"] .background-white,
.pexlat-form-container[data-color-scheme="dark_mode"] .white-bg,
.pexlat-form-container[data-color-scheme="dark_mode"] .white-background {
    background-color: #374151 !important;
    background: #374151 !important;
    color: #f9fafb !important;
}

/* إصلاح شامل لجميع العناصر البيضاء المحتملة */
.pexlat-form-container[data-color-scheme="dark_mode"] *[style*="background-color: white"],
.pexlat-form-container[data-color-scheme="dark_mode"] *[style*="background-color:#ffffff"],
.pexlat-form-container[data-color-scheme="dark_mode"] *[style*="background-color: #ffffff"],
.pexlat-form-container[data-color-scheme="dark_mode"] *[style*="background-color:#fff"],
.pexlat-form-container[data-color-scheme="dark_mode"] *[style*="background-color: #fff"],
.pexlat-form-container[data-color-scheme="dark_mode"] *[style*="background:white"],
.pexlat-form-container[data-color-scheme="dark_mode"] *[style*="background:#ffffff"],
.pexlat-form-container[data-color-scheme="dark_mode"] *[style*="background: #ffffff"],
.pexlat-form-container[data-color-scheme="dark_mode"] *[style*="background:#fff"],
.pexlat-form-container[data-color-scheme="dark_mode"] *[style*="background: #fff"] {
    background-color: #374151 !important;
    background: #374151 !important;
    color: #f9fafb !important;
}

/* إصلاح للعناصر المضافة ديناميكياً بواسطة JavaScript */
.pexlat-form-container[data-color-scheme="dark_mode"] [class*="shipping"],
.pexlat-form-container[data-color-scheme="dark_mode"] [class*="summary"],
.pexlat-form-container[data-color-scheme="dark_mode"] [class*="header"],
.pexlat-form-container[data-color-scheme="dark_mode"] [class*="option"],
.pexlat-form-container[data-color-scheme="dark_mode"] [class*="method"] {
    background-color: #374151 !important;
    background: #374151 !important;
    color: #f9fafb !important;
    border-color: #4b5563 !important;
}

/* إصلاح للعناصر المحددة بالهوية */
.pexlat-form-container[data-color-scheme="dark_mode"] #summary-header,
.pexlat-form-container[data-color-scheme="dark_mode"] #shipping-methods,
.pexlat-form-container[data-color-scheme="dark_mode"] #shipping-options,
.pexlat-form-container[data-color-scheme="dark_mode"] #order-summary {
    background-color: #374151 !important;
    background: #374151 !important;
    color: #f9fafb !important;
    border-color: #4b5563 !important;
}

/* إصلاح نهائي شامل لضمان عدم ظهور أي لون أبيض */
.pexlat-form-container[data-color-scheme="dark_mode"] {
    background-color: #1f2937 !important;
    color: #f9fafb !important;
}

/* إصلاح للعناصر التي تستخدم pseudo-elements */
.pexlat-form-container[data-color-scheme="dark_mode"] *::before,
.pexlat-form-container[data-color-scheme="dark_mode"] *::after {
    background-color: transparent !important;
    background: transparent !important;
    color: inherit !important;
}

/* إصلاح متقدم للعناصر المقاومة */
.pexlat-form-container[data-color-scheme="dark_mode"] div:not(.selected):not(:hover),
.pexlat-form-container[data-color-scheme="dark_mode"] span:not(.selected):not(:hover) {
    background-color: inherit !important;
    color: inherit !important;
}

/* إصلاح خاص للشريط العلوي بجميع الحالات */
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header.collapsed,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header.expanded,
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header:not(.expanded) {
    background-color: #374151 !important;
    background: #374151 !important;
    background-image: none !important;
    color: #f9fafb !important;
    border-color: #4b5563 !important;
}

/* إصلاح خاص لطرق التوصيل بجميع الحالات */
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option:not(.selected),
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option:not(:hover) {
    background-color: #374151 !important;
    background: #374151 !important;
    background-image: none !important;
    color: #f9fafb !important;
    border-color: #4b5563 !important;
}

/* إصلاح نهائي لضمان التطبيق */
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option {
    background: #374151 !important;
    color: #f9fafb !important;
    border: 1px solid #4b5563 !important;
}

/* إصلاح للنصوص والعناوين */
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-title,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-title,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-description {
    color: #f9fafb !important;
    background-color: transparent !important;
}

.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-price {
    color: #3b82f6 !important;
    background-color: rgba(59, 130, 246, 0.1) !important;
}

/* إصلاح للأيقونات */
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header i,
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option i {
    color: #3b82f6 !important;
    background-color: transparent !important;
}

/* إصلاحات للعرض المبسط لطرق التوصيل في الوضع المظلم */
.pexlat-form-container[data-color-scheme="dark_mode"] .simple-shipping-method-title,
.pexlat-form-container[data-color-scheme="dark_mode"] .simple-shipping-method-price {
    color: #f9fafb !important;
}

.pexlat-form-container[data-color-scheme="dark_mode"] .simple-shipping-method input[type="radio"] {
    accent-color: #3b82f6 !important;
}

/* إصلاح للأزرار */
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-toggle-btn {
    color: #9ca3af !important;
    background-color: transparent !important;
}

.pexlat-form-container[data-color-scheme="dark_mode"] .summary-toggle-btn:hover {
    background-color: #4b5563 !important;
    color: #f9fafb !important;
}

/* إصلاح شامل نهائي */
.pexlat-form-container[data-color-scheme="dark_mode"] * {
    border-color: #4b5563 !important;
}

/* إصلاح للعناصر التي قد تظهر بيضاء بسبب JavaScript أو CSS ديناميكي */
.pexlat-form-container[data-color-scheme="dark_mode"] .summary-header[style],
.pexlat-form-container[data-color-scheme="dark_mode"] .shipping-method-option[style] {
    background-color: #374151 !important;
    background: #374151 !important;
    color: #f9fafb !important;
}
