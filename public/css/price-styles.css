/**
 * أنماط CSS للأسعار في نماذج Form Pexlat
 */

/* تنسيق السعر الافتراضي المشطوب في جميع أنحاء الموقع */
del .woocommerce-Price-amount,
.price del .woocommerce-Price-amount,
.product-regular-price .woocommerce-Price-amount {
    font-size: 11px !important;
    color: #bbb !important;
    font-weight: 300 !important;
    opacity: 0.8 !important;
}

/* تنسيق رمز العملة داخل السعر الافتراضي المشطوب */
del .woocommerce-Price-currencySymbol,
.price del .woocommerce-Price-currencySymbol,
.product-regular-price .woocommerce-Price-currencySymbol {
    font-size: 10px !important;
    color: #bbb !important;
    font-weight: 300 !important;
}

/* تنسيق شارة الكمية في ملخص الطلب */
.product-quantity-badge {
    display: inline-block;
    background-color: var(--form-primary-color, #2563eb);
    color: white;
    font-size: 12px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
    margin-right: 5px;
    line-height: 1.2;
}

/* تنسيق شارة الكمية في بطاقات العروض */
.offer-min-quantity {
    font-size: 14px;
    color: #444;
    margin-top: 4px;
    font-weight: 500;
}

/* تنسيق طرق التوصيل المبسطة في ملخص الطلب - نمط بسيط مثل الصورة */
.shipping-methods-simple {
    margin-bottom: 10px;
}

.shipping-methods-simple .price-label {
    margin-bottom: 8px;
    display: block;
    font-weight: 500;
}

.simple-shipping-methods-container {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 0;
}

.simple-shipping-method {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2px 0;
    border: none;
    background: none;
    cursor: pointer;
    transition: none;
    position: relative;
    width: 100%;
}

.simple-shipping-method-right {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.simple-shipping-method input[type="radio"] {
    margin: 0;
    width: 14px;
    height: 14px;
    accent-color: var(--form-primary-color, #2563eb);
    cursor: pointer;
}

/* تحسين لون تحديد طرق الشحن في العرض التفصيلي */
.shipping-method-option input[type="radio"] {
    accent-color: var(--form-primary-color, #2563eb);
}

.simple-shipping-method-title {
    font-size: 14px;
    font-weight: 400;
    color: var(--form-text-color, #1e293b);
    margin: 0;
    cursor: pointer;
    white-space: nowrap;
}

.simple-shipping-method-price {
    font-size: 14px;
    font-weight: 400;
    color: var(--form-text-color, #1e293b);
    margin: 0;
    cursor: pointer;
    margin-left: auto;
}

/* إخفاء العرض التفصيلي عند تفعيل العرض المبسط */
.pexlat-form-container[data-shipping-display="simple"] .shipping-methods-container {
    display: none !important;
}

.pexlat-form-container[data-shipping-display="simple"] .pexlat-form-address-fields .shipping-methods-container {
    display: none !important;
}

/* تنسيق متجاوب للعرض المبسط */
@media (max-width: 576px) {
    .simple-shipping-method {
        gap: 4px;
    }
}
