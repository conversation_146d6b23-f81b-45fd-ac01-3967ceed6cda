/**
 * CSS styles for compatibility with different themes
 */

/* General compatibility styles */
.pexlat-form-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto !important;
}

/* Twenty Twenty-Four theme compatibility */
.wp-block-post-content .pexlat-form-container,
.wp-site-blocks .pexlat-form-container,
.entry-content .pexlat-form-container,
.woocommerce-product-details__short-description .pexlat-form-container,
.woocommerce-Tabs-panel .pexlat-form-container,
.woocommerce-product-gallery + .pexlat-form-container,
.summary + .pexlat-form-container,
.pexlat-form-after-title .pexlat-form-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 0 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Fix for Twenty Twenty-Four theme */
.wp-block-post-content .pexlat-form-form,
.wp-site-blocks .pexlat-form-form,
.entry-content .pexlat-form-form,
.woocommerce-product-details__short-description .pexlat-form-form,
.woocommerce-Tabs-panel .pexlat-form-form,
.pexlat-form-after-title .pexlat-form-form {
    width: 100% !important;
    max-width: 100% !important;
}

/* Specific fixes for Twenty Twenty-Four theme */
.theme-twentytwentyfour .pexlat-form-after-title,
.wp-site-blocks .pexlat-form-after-title {
    margin-top: 0 !important;
    margin-bottom: 30px !important;
}

/* Fix for Twenty Twenty-Four theme product title */
.theme-twentytwentyfour .woocommerce-products-header + .pexlat-form-after-title,
.wp-site-blocks .woocommerce-products-header + .pexlat-form-after-title {
    margin-top: 20px !important;
}

/* Form container styles */
.pexlat-form-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 0 !important;
    clear: both !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Form styles for better centering and margins */
.pexlat-form-form {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 15px 10px !important;
    box-sizing: border-box !important;
}

/* Ensure form is centered in all themes */
.woocommerce-product-gallery + .summary .pexlat-form-container,
.woocommerce-product-gallery + .summary .pexlat-form-form,
.summary .pexlat-form-container,
.summary .pexlat-form-form {
    margin-left: auto !important;
    margin-right: auto !important;
    text-align: right !important;
}

/* Reduce side margins for better appearance */
@media (min-width: 768px) {
    .pexlat-form-form {
        padding-left: 5px !important;
        padding-right: 5px !important;
    }
}

/* Containers for different positions */
.pexlat-form-after-summary {
    clear: both;
    margin-top: 30px;
    padding: 20px 0;
    border-top: 1px solid #eee;
}

.pexlat-form-before-summary {
    margin-bottom: 30px;
    clear: both;
}

.pexlat-form-in-description {
    margin-top: 30px;
    clear: both;
}

.pexlat-form-after-product {
    margin-top: 50px;
    clear: both;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.pexlat-form-before-product {
    margin-bottom: 30px;
    clear: both;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
}

/* Fix for tabs in WooCommerce */
.woocommerce-tabs .pexlat-form-container,
.woocommerce-Tabs-panel .pexlat-form-container {
    padding: 20px !important;
    background-color: #f9f9f9;
    border-radius: 8px;
    margin-bottom: 20px;
}

/* Fix for Botiga theme */
.botiga-product-wrapper .pexlat-form-container {
    width: 100% !important;
    max-width: 100% !important;
}

/* Fix for Blocksy theme */
.ct-container .pexlat-form-container {
    width: 100% !important;
    max-width: 100% !important;
}

/* Fix for other common themes */
.product .pexlat-form-container,
.woocommerce-product-gallery + .pexlat-form-container,
.summary + .pexlat-form-container {
    width: 100% !important;
    max-width: 100% !important;
    margin-top: 20px !important;
}

/* Ensure form is visible in all themes */
.pexlat-form-container {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Fix for FSE themes */
.wp-block-group .pexlat-form-container {
    width: 100% !important;
    max-width: 100% !important;
}

/* Fix for mobile devices */
@media (max-width: 768px) {
    .pexlat-form-container {
        padding: 10px !important;
    }

    .pexlat-form-after-product,
    .pexlat-form-before-product,
    .pexlat-form-main-wrapper {
        padding: 15px !important;
    }
}

/* إخفاء عناصر ووكومرس التي تتداخل مع فورم الطلب المخصص */
/* هذا الحل يمنع ظهور فورم الطلب مرتين ويحل مشكلة عدم عمل الفورم */

/* إخفاء جدول المتغيرات الأصلي - الحل الرئيسي للمشكلة */
.woocommerce div.product table.variations,
.woocommerce div.product .variations,
.woocommerce div.product .single_variation_wrap,
.woocommerce div.product .woocommerce-variation,
.woocommerce div.product .variations_form,
.woocommerce div.product form.variations_form {
    display: none !important;
}

/* إخفاء فورم إضافة إلى السلة الأصلي */
.woocommerce div.product form.cart:not(.pexlat-form-form) {
    display: none !important;
}

/* إخفاء زر إضافة إلى السلة الأصلي */
.woocommerce div.product .single_add_to_cart_button:not(.pexlat-form-submit) {
    display: none !important;
}

/* إخفاء كمية المنتج الأصلية */
.woocommerce div.product form.cart .quantity:not(.pexlat-form-quantity) {
    display: none !important;
}

/* إخفاء معلومات المتغيرات الأصلية */
.woocommerce div.product .woocommerce-variation-description,
.woocommerce div.product .woocommerce-variation-price,
.woocommerce div.product .woocommerce-variation-availability {
    display: none !important;
}

/* إخفاء أي عناصر أخرى قد تتداخل */
.woocommerce div.product .woocommerce-variation-add-to-cart {
    display: none !important;
}

/* التأكد من ظهور فورم الطلب المخصص فقط */
.pexlat-form-form {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}
