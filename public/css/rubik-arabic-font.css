/**
 * خط Rubik Arabic للنماذج
 * يطبق خط Rubik Arabic على جميع النصوص في النموذج
 */

/* استيراد خط Rubik Arabic من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* تطبيق خط Rubik Arabic على حاوية النموذج الرئيسية */
.pexlat-form-container {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}

/* تطبيق الخط على النموذج نفسه */
.pexlat-form-form {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}

/* تطبيق الخط على جميع العناصر داخل النموذج */
.pexlat-form-form *,
.pexlat-form-container * {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}

/* تطبيق الخط على العناوين */
.pexlat-form-form h1,
.pexlat-form-form h2,
.pexlat-form-form h3,
.pexlat-form-form h4,
.pexlat-form-form h5,
.pexlat-form-form h6 {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    font-weight: 600;
}

/* تطبيق الخط على التسميات */
.pexlat-form-form label {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    font-weight: 500;
}

/* تطبيق الخط على حقول الإدخال */
.pexlat-form-form input[type="text"],
.pexlat-form-form input[type="email"],
.pexlat-form-form input[type="tel"],
.pexlat-form-form input[type="number"],
.pexlat-form-form input[type="password"],
.pexlat-form-form textarea,
.pexlat-form-form select {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    font-weight: 400;
}

/* تطبيق الخط على الأزرار */
.pexlat-form-form button,
.pexlat-form-form .pexlat-form-submit,
.pexlat-form-form .whatsapp-order-button,
.pexlat-form-form input[type="submit"],
.pexlat-form-form input[type="button"] {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    font-weight: 600;
}

/* تطبيق الخط على النصوص الوصفية */
.pexlat-form-form .pexlat-form-description,
.pexlat-form-form .field-description,
.pexlat-form-form .help-text {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    font-weight: 400;
}

/* تطبيق الخط على رسائل الخطأ */
.pexlat-form-form .field-error,
.pexlat-form-form .error-message {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    font-weight: 500;
}

/* تطبيق الخط على خيارات الراديو والتشيك بوكس */
.pexlat-form-form .checkbox-label,
.pexlat-form-form .radio-label {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    font-weight: 400;
}

/* تطبيق الخط على عناصر المتغيرات */
.pexlat-form-variations,
.pexlat-form-variations *,
.variations-title,
.variation-attribute-label,
.variation-button,
.variation-dropdown {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}

/* تطبيق الخط على ملخص الطلب */
.total-price-container,
.total-price-container *,
.summary-title,
.price-label,
.product-price-display,
.shipping-price-display,
.total-price-display {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}

/* تطبيق الخط على طرق الشحن */
.shipping-methods-container,
.shipping-methods-container *,
.shipping-method-option,
.shipping-method-title,
.shipping-method-description,
.shipping-method-cost {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}

/* تطبيق الخط على عناصر الكمية */
.quantity-container,
.quantity-container *,
.quantity-label,
.quantity-input,
.quantity-btn {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}

/* تطبيق الخط على الشريط المثبت */
.pexlat-form-sticky-bar,
.pexlat-form-sticky-bar *,
.sticky-order-button {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}

/* تطبيق الخط على رسائل التنبيه */
.pexlat-form-toast {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    font-weight: 500;
}

/* تطبيق الخط على عناصر إضافية */
.form-field,
.form-field *,
.input-group,
.input-group *,
.input-group-text {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}

/* تحسين عرض الخط العربي */
.pexlat-form-container,
.pexlat-form-form {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تطبيق الخط على عناصر placeholder */
.pexlat-form-form input::placeholder,
.pexlat-form-form textarea::placeholder {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    font-weight: 400;
}

/* تطبيق الخط على خيارات القوائم المنسدلة */
.pexlat-form-form select option {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
    font-weight: 400;
}

/* تأكيد تطبيق الخط على جميع العناصر */
.pexlat-form-container .pexlat-form-form,
.pexlat-form-container .pexlat-form-form * {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}
