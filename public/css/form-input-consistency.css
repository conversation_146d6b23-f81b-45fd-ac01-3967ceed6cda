/**
 * توحيد ارتفاع حقول النموذج
 * هذا الملف يضمن أن جميع حقول النموذج لها نفس الارتفاع
 */

/* توحيد ارتفاع جميع حقول الإدخال */
.pexlat-form-form input[type="text"],
.pexlat-form-form input[type="email"],
.pexlat-form-form input[type="tel"],
.pexlat-form-form input[type="number"],
.pexlat-form-form input[type="password"],
.pexlat-form-form input[type="url"],
.pexlat-form-form select,
.pexlat-form-form .input-group-text {
    height: 45px !important;
    min-height: 45px !important;
    line-height: 1.4 !important;
    padding: 10px 12px !important;
    box-sizing: border-box !important;
    display: flex !important;
    align-items: center !important;
}

/* توحيد ارتفاع مناطق النص */
.pexlat-form-form textarea {
    min-height: 80px !important;
    padding: 10px 12px !important;
    box-sizing: border-box !important;
    line-height: 1.4 !important;
}

/* توحيد ارتفاع مجموعات الإدخال */
.pexlat-form-form .input-group {
    display: flex !important;
    align-items: stretch !important;
    height: 45px !important;
    min-height: 45px !important;
}

/* توحيد ارتفاع أيقونات الحقول */
.pexlat-form-form .input-group-text {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 45px !important;
    min-width: 45px !important;
    max-width: 45px !important;
    padding: 0 !important;
    border-radius: 6px 0 0 6px !important;
}

/* توحيد ارتفاع الحقول داخل مجموعات الإدخال */
.pexlat-form-form .input-group input,
.pexlat-form-form .input-group select {
    flex: 1 !important;
    border-radius: 0 6px 6px 0 !important;
    border-right: none !important;
}

/* إصلاح مشكلة الحدود المزدوجة */
.pexlat-form-form .input-group .input-group-text + input,
.pexlat-form-form .input-group .input-group-text + select {
    border-right: none !important;
}

/* توحيد الخطوط والألوان */
.pexlat-form-form input,
.pexlat-form-form select,
.pexlat-form-form textarea,
.pexlat-form-form .input-group-text {
    font-family: inherit !important;
    font-size: 14px !important;
    color: var(--form-text-color, #1e293b) !important;
    background-color: var(--form-input-bg, #f8fafc) !important;
    border: 1px solid var(--form-input-border, #e2e8f0) !important;
}

/* توحيد تأثيرات التركيز */
.pexlat-form-form input:focus,
.pexlat-form-form select:focus,
.pexlat-form-form textarea:focus {
    border-color: var(--form-input-focus-border, #93c5fd) !important;
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(147, 197, 253, 0.1) !important;
}

/* إزالة الأنماط الافتراضية للمتصفح */
.pexlat-form-form input,
.pexlat-form-form select,
.pexlat-form-form textarea {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    margin: 0 !important;
}

/* توحيد أنماط القوائم المنسدلة */
.pexlat-form-form select {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E") !important;
    background-repeat: no-repeat !important;
    background-position: left 12px center !important;
    background-size: 16px 12px !important;
    padding-left: 40px !important;
    cursor: pointer !important;
}

/* إصلاح مشكلة الارتفاع في Safari */
@supports (-webkit-appearance: none) {
    .pexlat-form-form input,
    .pexlat-form-form select {
        height: 45px !important;
        line-height: normal !important;
    }
}

/* إصلاح مشكلة الارتفاع في Firefox */
@-moz-document url-prefix() {
    .pexlat-form-form input,
    .pexlat-form-form select {
        height: 45px !important;
        line-height: 1.4 !important;
    }
}

/* توحيد الهوامش والتباعد */
.pexlat-form-form .form-group {
    margin-bottom: 15px !important;
}

.pexlat-form-form label {
    margin-bottom: 5px !important;
    display: block !important;
    font-weight: 600 !important;
    color: var(--form-label-color, #475569) !important;
}

/* إصلاح مشكلة التداخل مع أنماط القوالب */
.pexlat-form-container * {
    box-sizing: border-box !important;
}

/* ضمان عدم تأثر الحقول بأنماط القوالب الخارجية */
.pexlat-form-form input:not([type="submit"]):not([type="button"]):not([type="reset"]),
.pexlat-form-form select,
.pexlat-form-form textarea {
    width: 100% !important;
    max-width: 100% !important;
    border-radius: 6px !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

/* إصلاح مشكلة الحقول المطلوبة */
.pexlat-form-form input:required,
.pexlat-form-form select:required,
.pexlat-form-form textarea:required {
    box-shadow: none !important;
}

.pexlat-form-form input:invalid,
.pexlat-form-form select:invalid,
.pexlat-form-form textarea:invalid {
    box-shadow: none !important;
    border-color: var(--form-input-border, #e2e8f0) !important;
}

/* توحيد أنماط الأزرار */
.pexlat-form-form button,
.pexlat-form-form input[type="submit"],
.pexlat-form-form input[type="button"] {
    height: 45px !important;
    min-height: 45px !important;
    padding: 10px 20px !important;
    border-radius: 6px !important;
    border: none !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.15s ease-in-out !important;
}

/* إصلاح مشكلة الحقول في الأجهزة المحمولة */
@media (max-width: 768px) {
    .pexlat-form-form input,
    .pexlat-form-form select,
    .pexlat-form-form textarea,
    .pexlat-form-form .input-group-text {
        font-size: 16px !important; /* منع التكبير في iOS */
    }
}

/* إصلاح مشكلة الحقول في الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .pexlat-form-form input,
    .pexlat-form-form select,
    .pexlat-form-form textarea {
        color-scheme: light !important; /* فرض الوضع الفاتح للحقول */
    }
}
