/**
 * إصلاح JavaScript للوضع المظلم
 * يضمن تطبيق الألوان الصحيحة ديناميكياً
 */

(function($) {
    'use strict';

    // دالة لإصلاح الألوان في الوضع المظلم
    function fixDarkModeColors() {
        const container = $('.pexlat-form-container[data-color-scheme="dark_mode"]');
        
        if (container.length === 0) {
            return; // ليس في الوضع المظلم
        }

        // إصلاح الشريط العلوي
        container.find('.summary-header').each(function() {
            $(this).css({
                'background-color': '#374151 !important',
                'background': '#374151 !important',
                'color': '#f9fafb !important',
                'border-color': '#4b5563 !important'
            });
            
            // إصلاح العناصر الفرعية
            $(this).find('*').css({
                'background-color': 'transparent !important',
                'background': 'transparent !important',
                'color': 'inherit !important'
            });
        });

        // إصلاح طرق التوصيل غير المحددة
        container.find('.shipping-method-option:not(.selected)').each(function() {
            $(this).css({
                'background-color': '#374151 !important',
                'background': '#374151 !important',
                'color': '#f9fafb !important',
                'border-color': '#4b5563 !important'
            });
            
            // إصلاح العناصر الفرعية
            $(this).find('*').css({
                'background-color': 'transparent !important',
                'background': 'transparent !important',
                'color': 'inherit !important'
            });
        });

        // إصلاح طرق التوصيل المحددة
        container.find('.shipping-method-option.selected').each(function() {
            $(this).css({
                'background-color': 'rgba(59, 130, 246, 0.15) !important',
                'background': 'rgba(59, 130, 246, 0.15) !important',
                'color': '#f9fafb !important',
                'border-color': '#3b82f6 !important'
            });
        });

        // إصلاح العناصر التي تحتوي على أنماط مضمنة
        container.find('[style*="background"]').each(function() {
            const $this = $(this);
            if (!$this.hasClass('selected') && !$this.is(':hover')) {
                $this.css({
                    'background-color': '#374151 !important',
                    'background': '#374151 !important',
                    'color': '#f9fafb !important'
                });
            }
        });

        // إصلاح العناصر البيضاء
        container.find('*').each(function() {
            const $this = $(this);
            const bgColor = $this.css('background-color');
            
            if (bgColor === 'rgb(255, 255, 255)' || bgColor === '#ffffff' || bgColor === '#fff' || bgColor === 'white') {
                if (!$this.hasClass('selected') && !$this.is(':hover')) {
                    $this.css({
                        'background-color': '#374151 !important',
                        'background': '#374151 !important',
                        'color': '#f9fafb !important'
                    });
                }
            }
        });
    }

    // دالة لمراقبة التغييرات في DOM
    function observeDOMChanges() {
        const container = $('.pexlat-form-container[data-color-scheme="dark_mode"]');
        
        if (container.length === 0) {
            return;
        }

        // إنشاء MutationObserver لمراقبة التغييرات
        const observer = new MutationObserver(function(mutations) {
            let shouldFix = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    shouldFix = true;
                }
            });
            
            if (shouldFix) {
                setTimeout(fixDarkModeColors, 100);
            }
        });

        // بدء مراقبة التغييرات
        observer.observe(container[0], {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class']
        });
    }

    // دالة لإضافة أحداث التفاعل
    function addInteractionEvents() {
        const container = $('.pexlat-form-container[data-color-scheme="dark_mode"]');
        
        if (container.length === 0) {
            return;
        }

        // إضافة حدث للنقر على طرق التوصيل
        container.on('click', '.shipping-method-option', function() {
            setTimeout(fixDarkModeColors, 50);
        });

        // إضافة حدث للنقر على الشريط العلوي
        container.on('click', '.summary-header', function() {
            setTimeout(fixDarkModeColors, 50);
        });

        // إضافة حدث للتحويم
        container.on('mouseenter mouseleave', '.shipping-method-option, .summary-header', function() {
            setTimeout(fixDarkModeColors, 10);
        });
    }

    // دالة لإصلاح الألوان بشكل دوري
    function periodicFix() {
        setInterval(function() {
            const container = $('.pexlat-form-container[data-color-scheme="dark_mode"]');
            if (container.length > 0) {
                fixDarkModeColors();
            }
        }, 2000); // كل ثانيتين
    }

    // تشغيل الإصلاحات عند تحميل الصفحة
    $(document).ready(function() {
        // إصلاح فوري
        fixDarkModeColors();
        
        // إصلاح بعد تحميل كامل
        setTimeout(fixDarkModeColors, 500);
        setTimeout(fixDarkModeColors, 1000);
        setTimeout(fixDarkModeColors, 2000);
        
        // إضافة مراقبة التغييرات
        observeDOMChanges();
        
        // إضافة أحداث التفاعل
        addInteractionEvents();
        
        // بدء الإصلاح الدوري
        periodicFix();
    });

    // إصلاح عند تغيير حجم النافذة
    $(window).on('resize', function() {
        setTimeout(fixDarkModeColors, 100);
    });

    // إصلاح عند التمرير
    $(window).on('scroll', function() {
        setTimeout(fixDarkModeColors, 100);
    });

    // إصلاح عند تحديث AJAX
    $(document).ajaxComplete(function() {
        setTimeout(fixDarkModeColors, 200);
    });

    // إصلاح عند تغيير التركيز
    $(document).on('focus blur', 'input, select, textarea', function() {
        setTimeout(fixDarkModeColors, 50);
    });

    // دالة عامة يمكن استدعاؤها من الخارج
    window.fixPexlatDarkMode = fixDarkModeColors;

})(jQuery);
