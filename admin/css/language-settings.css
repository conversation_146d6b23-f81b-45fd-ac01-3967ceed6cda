/**
 * أنماط CSS لقسم إعدادات اللغة
 */

.language-list {
    list-style: none;
    padding: 0;
    margin: 0 0 15px 0;
}

.language-list li {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.language-list li:hover {
    background-color: #f0f0f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.language-icon {
    font-size: 24px;
    margin-right: 10px;
}

.language-name {
    flex: 1;
    font-weight: 500;
}

.language-status {
    background-color: #4CAF50;
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
}

/* تنسيق القائمة المنسدلة للغات */
#pexlat_form_language {
    width: 100%;
    max-width: 300px;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

#pexlat_form_language:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

/* تنسيق معلومات الترجمة */
#language-settings .settings-field h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 16px;
    color: #23282d;
}

#language-settings code {
    background-color: #f6f7f7;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 13px;
}

/* تنسيق الوصف */
#language-settings .description {
    margin-top: 8px;
    color: #646970;
    font-style: italic;
}

/* تنسيق حالة اللغة */
.language-status.available {
    background-color: #4CAF50;
}

.language-status.coming-soon {
    background-color: #FF9800;
}

/* تنسيق متجاوب */
@media screen and (max-width: 782px) {
    #pexlat_form_language {
        max-width: 100%;
    }
    
    .language-list li {
        padding: 8px;
    }
    
    .language-icon {
        font-size: 20px;
    }
}
