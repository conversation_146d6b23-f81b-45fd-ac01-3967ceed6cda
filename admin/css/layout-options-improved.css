/**
 * تحسين مظهر خيارات التخطيط في إعدادات النموذج
 * هذا الملف يحسن من تجربة المستخدم في اختيار تخطيط الحقول
 */

/* تحسين حاوية خيارات التخطيط */
.form-layout-preview {
    display: flex;
    gap: 12px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

/* تحسين مظهر خيارات التخطيط */
.layout-option {
    flex: 1;
    min-width: 80px;
    max-width: 120px;
    padding: 12px 8px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    user-select: none;
}

.layout-option:hover {
    border-color: #cbd5e1;
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.layout-option.active {
    border-color: #3b82f6 !important;
    background: #eff6ff !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2) !important;
}

/* إخفاء أزرار الراديو */
.layout-option .layout-radio {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

/* تحسين معاينة التخطيط */
.layout-preview {
    margin-bottom: 8px;
}

.layout-icon {
    margin-bottom: 6px;
}

.layout-icon .dashicons {
    font-size: 20px;
    color: #64748b;
    transition: color 0.3s ease;
}

.layout-option.active .layout-icon .dashicons {
    color: #3b82f6;
}

/* تحسين عناصر المعاينة */
.layout-preview-items {
    display: flex;
    flex-direction: column;
    gap: 3px;
    align-items: center;
}

.layout-preview-items.layout-columns {
    flex-direction: row;
    justify-content: center;
    gap: 4px;
}

.preview-field {
    width: 20px;
    height: 4px;
    background: #cbd5e1;
    border-radius: 2px;
    transition: background-color 0.3s ease;
}

.layout-option.active .preview-field {
    background: #3b82f6;
}

/* تحسين تسميات التخطيط */
.layout-label {
    font-size: 11px;
    font-weight: 500;
    color: #475569;
    transition: color 0.3s ease;
}

.layout-option.active .layout-label {
    color: #3b82f6;
    font-weight: 600;
}

/* تحسين المعاينة المتجاوبة */
.layout-preview-items.layout-responsive {
    gap: 2px;
}

.desktop-view {
    display: flex;
    gap: 2px;
    justify-content: center;
}

.mobile-view {
    display: flex;
    flex-direction: column;
    gap: 1px;
    align-items: center;
    margin-top: 2px;
}

.desktop-view .preview-field {
    width: 8px;
    height: 3px;
}

.mobile-view .preview-field {
    width: 16px;
    height: 2px;
}

/* تحسين وصف التخطيط */
.layout-description {
    color: #64748b;
    font-size: 12px;
    line-height: 1.4;
    margin-top: 8px;
    padding: 8px;
    background: #f8fafc;
    border-radius: 6px;
    border-left: 3px solid #3b82f6;
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .form-layout-preview {
        flex-direction: column;
        gap: 8px;
    }
    
    .layout-option {
        max-width: none;
        padding: 10px;
    }
    
    .layout-preview-items {
        flex-direction: row;
        justify-content: center;
    }
    
    .layout-preview-items.layout-columns {
        gap: 3px;
    }
}

/* تحسين التفاعل */
.layout-option:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* إضافة أيقونة التحديد */
.layout-option.active::after {
    content: "✓";
    position: absolute;
    top: 4px;
    right: 4px;
    width: 16px;
    height: 16px;
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* تحسين الانتقالات */
.layout-option * {
    transition: all 0.3s ease;
}

/* إزالة التأثيرات غير المرغوبة */
.layout-option:active {
    transform: translateY(0);
}

/* تحسين التباعد */
.form-layout-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.form-layout-section h5 {
    margin-bottom: 12px;
    color: #1e293b;
    font-weight: 600;
    font-size: 13px;
}

/* تحسين للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .layout-option {
        background: #1e293b;
        border-color: #334155;
        color: #e2e8f0;
    }
    
    .layout-option:hover {
        background: #334155;
        border-color: #475569;
    }
    
    .layout-option.active {
        background: #1e40af !important;
        border-color: #3b82f6 !important;
    }
    
    .preview-field {
        background: #475569;
    }
    
    .layout-option.active .preview-field {
        background: #60a5fa;
    }
    
    .layout-description {
        background: #1e293b;
        color: #cbd5e1;
        border-left-color: #3b82f6;
    }
    
    .form-layout-section {
        background: #1e293b;
        border-color: #334155;
    }
    
    .form-layout-section h5 {
        color: #e2e8f0;
    }
}

/* تحسين إمكانية الوصول */
.layout-option[aria-selected="true"] {
    border-color: #3b82f6;
    background: #eff6ff;
}

.layout-option:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* تحسين الرسوم المتحركة */
@keyframes selectOption {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.layout-option.active {
    animation: selectOption 0.3s ease;
}
