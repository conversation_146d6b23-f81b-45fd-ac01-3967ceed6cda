/**
 * إصلاح مشكلة ظهور الشعار والأيقونات في Form Pexlat
 * هذا الملف يحتوي على تنسيقات لإزالة الحدود السوداء والبيضاء من الشعار
 * وإصلاح مظهر أيقونة Form Pexlat في الشريط الجانبي
 */

/* إزالة الحدود السوداء من الشعار في شريط العنوان */
.pexlat-header-logo img {
    width: 40px;
    height: auto;
    display: block;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    border-radius: 0 !important;
}

/* إزالة الحدود البيضاء من الشعار في الشريط الجانبي - تحديد فقط أيقونة Form Pexlat */
#adminmenu .toplevel_page_pexlat-form img {
    width: 20px;
    height: 20px;
    padding-top: 7px;
    opacity: 1;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
}

/* إصلاح مظهر الشعار في صفحة إعدادات النموذج */
.form-settings-page .pexlat-header-logo img {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
}

/* إزالة أي حدود أو تأثيرات غير مرغوبة من الشعار في جميع الصفحات - تحديد فقط شعار Pexlat */
.pexlat-header-bar img,
.logo-card img {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
}

/* تحسين مظهر الشعار في القائمة الجانبية عند التحويم - تحديد فقط أيقونة Form Pexlat */
#adminmenu .toplevel_page_pexlat-form:hover img,
#adminmenu .toplevel_page_pexlat-form.current img,
#adminmenu .toplevel_page_pexlat-form.wp-has-current-submenu img {
    opacity: 1;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
}

/* إزالة الحدود من أيقونة القائمة الجانبية - تحديد فقط أيقونة Form Pexlat */
#adminmenu .toplevel_page_pexlat-form .wp-menu-image.dashicons-before {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
}

/* تأكيد إزالة الحدود من الشعار في جميع الحالات */
img[src*="pexlat-form-logo"] {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    padding: 0 !important;
}

/* إصلاح مشكلة الحدود السوداء في صفحة إعدادات النموذج */
.form-settings-page .pexlat-header-logo img,
.form-settings-page .wp-menu-image.dashicons-before img {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    outline: none !important;
}

/* تطبيق تنسيقات على الشعار في شريط العنوان */
.pexlat-header-bar .pexlat-header-logo img {
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    margin: 0 !important;
    display: block !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    outline: none !important;
}

/* إصلاح مشكلة ضغط أيقونة الإضافة في صفحة إعدادات النموذج */
body.admin_page_pexlat-form-edit #adminmenu .toplevel_page_pexlat-form img,
body.admin_page_pexlat-form-settings #adminmenu .toplevel_page_pexlat-form img {
    width: 20px !important;
    height: 20px !important;
    padding-top: 7px !important;
    opacity: 1 !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    transform: none !important;
    max-width: 20px !important;
    max-height: 20px !important;
}

/* التأكد من عدم تأثر أيقونة الإضافة بأي تنسيقات أخرى في صفحات الإعدادات */
body[class*="admin_page_pexlat-form"] #adminmenu .toplevel_page_pexlat-form img {
    width: 20px !important;
    height: 20px !important;
    padding-top: 7px !important;
    opacity: 1 !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    transform: none !important;
    max-width: 20px !important;
    max-height: 20px !important;
    object-fit: contain !important;
}
