/* تحسينات تخطيط النظام الموحد للألوان والتصميم */

/* تخطيط أفقي للعناصر الأساسية */
.unified-controls-row {
    display: flex;
    gap: 30px;
    margin: 20px 0;
    align-items: flex-start;
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

/* قسم اختيار مجموعة الألوان */
.color-scheme-section {
    flex: 2;
    min-width: 0;
}

.color-scheme-section label {
    display: block;
    font-weight: 600;
    margin-bottom: 12px;
    color: #2c3e50;
    font-size: 14px;
}

.color-schemes-compact {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;
}

.color-scheme-compact {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    text-align: center;
    position: relative;
}

.color-scheme-compact:hover {
    border-color: #3498db;
    box-shadow: 0 2px 6px rgba(52, 152, 219, 0.1);
    transform: translateY(-1px);
}

.color-scheme-compact.active {
    border-color: #3498db;
    background: #f8f9ff;
    box-shadow: 0 2px 6px rgba(52, 152, 219, 0.2);
}

.color-scheme-compact.active::after {
    content: "✓";
    position: absolute;
    top: 4px;
    right: 6px;
    color: #3498db;
    font-weight: bold;
    font-size: 12px;
}

.scheme-preview-compact {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.color-palette-compact {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin-bottom: 4px;
}

.color-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.scheme-name {
    font-size: 12px;
    font-weight: 500;
    color: #2c3e50;
    line-height: 1.3;
}

/* قسم زوايا العناصر */
.border-radius-section {
    flex: 1;
    min-width: 200px;
}

.border-radius-section label {
    display: block;
    font-weight: 600;
    margin-bottom: 12px;
    color: #2c3e50;
    font-size: 14px;
}

.range-control-compact {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.range-control-compact input[type="range"] {
    width: 100%;
    margin: 0;
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
    outline: none;
    -webkit-appearance: none;
}

.range-control-compact input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #3498db;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.range-control-compact input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #3498db;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.range-control-compact output {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
    font-weight: 500;
    min-width: 70px;
}

.description-compact {
    font-size: 12px;
    color: #7f8c8d;
    margin: 8px 0 0 0;
    line-height: 1.4;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .unified-controls-row {
        flex-direction: column;
        gap: 20px;
        padding: 15px;
    }

    .color-schemes-compact {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
    }

    .color-scheme-compact {
        padding: 10px;
    }

    .scheme-name {
        font-size: 11px;
    }

    .border-radius-section {
        min-width: auto;
    }

    .range-control-compact output {
        padding: 4px 8px;
        font-size: 11px;
        min-width: 60px;
    }
}

@media (max-width: 480px) {
    .color-schemes-compact {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .unified-controls-row {
        padding: 12px;
        gap: 15px;
    }
}

/* تحسينات إضافية للتفاعل */
.color-scheme-compact:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

.range-control-compact input[type="range"]:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* تأثيرات الحركة */
.color-scheme-compact {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.range-control-compact output {
    transition: background-color 0.2s ease;
}

.range-control-compact input[type="range"]:hover + output {
    background: #2980b9;
}
