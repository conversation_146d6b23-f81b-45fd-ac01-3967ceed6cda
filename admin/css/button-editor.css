/**
 * تنسيقات محرر تخصيص الزر
 */

/* التخطيط الأساسي */
.button-editor-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 12px;
    max-width: 900px;
}

/* معاينة الزر */
.button-preview-container {
    width: 160px;
    padding: 12px;
    background: #f9f9f9;
    border-radius: 4px;
    text-align: center;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.button-preview {
    margin: 10px 0;
    text-align: center;
}

.button-preview button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 100px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    border: none;
    border-radius: 4px;
    background-color: #4CAF50;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* قسم الإعدادات */
.button-settings-container {
    flex: 1;
    min-width: 400px;
}

.button-tabs-nav {
    display: flex;
    list-style: none;
    margin: 0 0 10px 0;
    padding: 0;
    border-bottom: 1px solid #ccc;
}

.button-tabs-nav li {
    margin: 0;
    padding: 0;
}

.button-tabs-nav li a {
    padding: 6px 10px;
    display: block;
    text-decoration: none;
    font-weight: 500;
    color: #555;
    border: 1px solid transparent;
    border-bottom: none;
    margin-bottom: -1px;
    font-size: 12px;
}

.button-tabs-nav li.active a {
    background-color: #ffffff;
    border-color: #ccc;
    border-bottom-color: #fff;
    color: #000;
}

.button-setting-item {
    margin-bottom: 12px;
}

.button-setting-item label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    font-size: 12px;
}

/* خيارات أحجام الزر */
.button-size-options {
    display: flex;
    gap: 8px;
}

.image-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
}

.image-option input[type="radio"] {
    display: none;
}

.image-option-label {
    margin-bottom: 3px;
    font-size: 11px;
}

.image-option-preview {
    width: 50px;
    height: 24px;
    background-color: #4CAF50;
    border-radius: 3px;
    transition: all 0.2s ease;
    position: relative;
    border: 1px solid transparent;
}

.small-button {
    width: 40px;
    height: 20px;
}

.medium-button {
    width: 50px;
    height: 24px;
}

.large-button {
    width: 60px;
    height: 28px;
}

.image-option input[type="radio"]:checked + .image-option-label + .image-option-preview {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
}

/* قوس الدائري للزر */
.range-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.range-control input[type="range"] {
    flex: 1;
}

.range-control output {
    min-width: 55px;
    text-align: right;
}

.border-radius-previews {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
}

.radius-preview {
    width: 40px;
    height: 25px;
    background-color: #4CAF50;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.radius-preview[data-value="0"] {
    border-radius: 0;
}

.radius-preview[data-value="6"] {
    border-radius: 6px;
}

.radius-preview[data-value="12"] {
    border-radius: 12px;
}

.radius-preview[data-value="20"] {
    border-radius: 20px;
}

.radius-preview[data-value="30"] {
    border-radius: 30px;
}

.radius-preview:hover, .radius-preview.active {
    border-color: #2271b1;
}

/* خيارات الألوان */
.color-picker-with-presets {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.color-presets {
    display: flex;
    gap: 8px;
}

.color-preset {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid #ddd;
    transition: all 0.2s ease;
}

.color-preset:hover, .color-preset.active {
    transform: scale(1.15);
    border-color: #fff;
    box-shadow: 0 0 0 2px #2271b1;
}

/* مفتاح تبديل للتدرج اللوني */
.form-toggle-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.toggle-switch-container {
    display: flex;
    align-items: center;
}

.switch {
    position: relative;
    display: inline-block;
    width: 36px;
    height: 18px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .3s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 12px;
    width: 12px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .3s;
}

input:checked + .slider {
    background-color: #2196F3;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
    transform: translateX(18px);
}

.slider.round {
    border-radius: 18px;
}

.slider.round:before {
    border-radius: 50%;
}

.toggle-status {
    margin-left: 8px;
    font-size: 11px;
    color: #555;
}

/* خيارات الأيقونات */
.icon-options, .direction-options {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.icon-option, .direction-option {
    text-align: center;
    cursor: pointer;
}

.icon-option input[type="radio"],
.direction-option input[type="radio"] {
    display: none;
}

.icon-preview, .direction-preview {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f1f1f1;
    border-radius: 3px;
    margin-bottom: 3px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.icon-option input[type="radio"]:checked + .icon-preview,
.direction-option input[type="radio"]:checked + .direction-preview {
    border-color: #2271b1;
    background-color: #f0f7ff;
}

.direction-preview {
    background: linear-gradient(to bottom, #4CAF50, #2e7d32);
}

.direction-preview.to-right {
    background: linear-gradient(to right, #4CAF50, #2e7d32);
}

.direction-preview.to-top {
    background: linear-gradient(to top, #4CAF50, #2e7d32);
}

.direction-preview.to-left {
    background: linear-gradient(to left, #4CAF50, #2e7d32);
}

.direction-preview.to-bottom-right {
    background: linear-gradient(to bottom right, #4CAF50, #2e7d32);
}

.direction-preview.to-bottom-left {
    background: linear-gradient(to bottom left, #4CAF50, #2e7d32);
}

/* خيارات موضع الأيقونة */
.icon-position-options {
    display: flex;
    gap: 12px;
}

.position-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
}

.position-option input[type="radio"] {
    display: none;
}

.position-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 28px;
    background-color: #4CAF50;
    color: white;
    border-radius: 3px;
    padding: 0 6px;
    margin-bottom: 3px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.text-placeholder {
    font-size: 10px;
    margin: 0 3px;
}

.position-option input[type="radio"]:checked + .position-preview {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
}

.position-label {
    font-size: 11px;
}

/* التأثيرات */
.effect-options, .animation-options {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.effect-option, .animation-option {
    text-align: center;
    cursor: pointer;
}

.effect-option input[type="radio"],
.animation-option input[type="radio"] {
    display: none;
}

.effect-preview, .animation-preview {
    width: 60px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #4CAF50;
    color: white;
    border-radius: 3px;
    font-size: 10px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.effect-option input[type="radio"]:checked + .effect-preview,
.animation-option input[type="radio"]:checked + .animation-preview {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
}

.hover-shadow {
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.hover-scale {
    transform: scale(1.05);
}

.hover-glow {
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.8);
}

/* تأثيرات حركية */
.animation-pulse {
    animation: preview_pulse 1.5s infinite;
}

.animation-bounce {
    animation: preview_bounce 1.5s infinite;
}

.animation-tada {
    animation: preview_tada 1.5s infinite;
}

.animation-shake {
    animation: preview_shake 1.5s infinite;
}

@keyframes preview_pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes preview_bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

@keyframes preview_tada {
    0% { transform: scale(1); }
    10%, 20% { transform: scale(0.9) rotate(-3deg); }
    30%, 50%, 70%, 90% { transform: scale(1.1) rotate(3deg); }
    40%, 60%, 80% { transform: scale(1.1) rotate(-3deg); }
    100% { transform: scale(1) rotate(0); }
}

@keyframes preview_shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* دعم اللغة العربية والـ RTL */
.rtl .button-preview button {
    flex-direction: row-reverse;
}

.rtl .toggle-status {
    margin-left: 0;
    margin-right: 10px;
}

.rtl .position-preview.position-right {
    flex-direction: row-reverse;
}

.rtl .position-preview.position-left {
    flex-direction: row;
}