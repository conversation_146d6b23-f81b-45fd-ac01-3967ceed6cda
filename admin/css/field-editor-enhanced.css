/**
 * تحسينات نافذة تعديل الحقل وإضافة حقل جديد
 */

/* تنسيق النافذة المنبثقة */
#field-editor-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.6);
    display: none;
    backdrop-filter: blur(3px);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    width: 90%;
    max-width: 600px;
    border-radius: 10px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    animation: modalFadeIn 0.3s ease;
    overflow: hidden;
    border: 1px solid #e2e8f0;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* رأس النافذة */
.modal-header {
    padding: 15px 20px;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
}

.modal-header .close {
    font-size: 24px;
    font-weight: bold;
    color: #64748b;
    cursor: pointer;
    transition: color 0.2s ease;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.modal-header .close:hover {
    color: #ef4444;
    background-color: #fee2e2;
}

/* جسم النافذة */
.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

/* تنسيق النموذج */
.field-editor-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.field-editor-form .field-edit-row {
    margin-bottom: 15px;
}

.field-editor-form .field-edit-row.full-width {
    grid-column: span 2;
}

.field-editor-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #1e293b;
    font-size: 14px;
}

.field-editor-form input[type="text"],
.field-editor-form select,
.field-editor-form textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
    background-color: #f8fafc;
}

.field-editor-form input[type="text"]:focus,
.field-editor-form select:focus,
.field-editor-form textarea:focus {
    border-color: #3730a3;
    box-shadow: 0 0 0 2px rgba(55, 48, 163, 0.2);
    outline: none;
    background-color: #fff;
}

.field-editor-form textarea {
    min-height: 100px;
    resize: vertical;
}

/* تنسيق خيارات التبديل */
.field-toggles {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.toggle-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.toggle-label input[type="checkbox"] {
    margin-left: 8px;
    width: 16px;
    height: 16px;
}

.toggle-label span {
    font-size: 14px;
    color: #1e293b;
}

/* تذييل النافذة */
.modal-footer {
    padding: 15px 20px;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.modal-footer button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

#save-field {
    background-color: #3730a3;
    color: #fff;
    border: 1px solid #312e81;
}

#save-field:hover {
    background-color: #312e81;
}

#cancel-edit-field {
    background-color: #fff;
    color: #64748b;
    border: 1px solid #cbd5e1;
}

#cancel-edit-field:hover {
    background-color: #f1f5f9;
    color: #1e293b;
}

/* تحسينات إضافية */
.field-options {
    display: none;
}

.field-placeholder {
    display: block;
}

/* تنسيق زر إضافة حقل جديد */
#add-field-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #3730a3;
    color: #fff;
    padding: 10px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(55, 48, 163, 0.3);
    margin: 20px 0;
}

#add-field-button:hover {
    background-color: #312e81;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(55, 48, 163, 0.4);
}

#add-field-button .dashicons {
    margin-left: 8px;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* تنسيق للحقول المعطلة */
.field-editor-form input:disabled,
.field-editor-form select:disabled {
    background-color: #f1f5f9;
    color: #94a3b8;
    cursor: not-allowed;
    border-color: #e2e8f0;
}

/* تنسيق للحقول الإلزامية */
.field-editor-form label.required::after {
    content: '*';
    color: #ef4444;
    margin-right: 4px;
}

/* تحسين مظهر الحقول عند التحويم */
.field-editor-form input[type="text"]:hover,
.field-editor-form select:hover,
.field-editor-form textarea:hover {
    border-color: #cbd5e1;
}

/* تنسيق للحقول الأساسية */
.editing-permanent-field .field-id-row,
.editing-permanent-field .field-type-row {
    opacity: 0.7;
    position: relative;
}

.editing-permanent-field .field-id-row::after,
.editing-permanent-field .field-type-row::after {
    content: 'لا يمكن تعديل هذا الحقل';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
    color: #64748b;
    font-size: 12px;
    border-radius: 6px;
    pointer-events: none;
}

/* تحسين مظهر قائمة الحقول */
#form-elements-sortable-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.no-fields {
    padding: 30px;
    text-align: center;
    background-color: #f8fafc;
    border: 1px dashed #cbd5e1;
    border-radius: 8px;
    color: #64748b;
    margin: 20px 0;
}

.no-fields p {
    margin: 0;
    font-size: 14px;
}

/* تنسيق مؤشر السحب والإفلات */
.ui-sortable-placeholder {
    visibility: visible !important;
    background-color: #f1f5f9;
    border: 2px dashed #cbd5e1;
    border-radius: 8px;
    margin-bottom: 15px;
    height: 60px;
}
