<?php
/**
 * مراقب الطلبات المتروكة
 * 
 * @package Pexlat_Form
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// التحقق من الصلاحيات
if (!current_user_can('manage_options')) {
    wp_die(__('ليس لديك صلاحية للوصول إلى هذه الصفحة.'));
}

// الحصول على إحصائيات الطلبات المتروكة
function get_abandoned_orders_stats() {
    global $wpdb;
    
    $stats = array();
    
    // إجمالي الطلبات المتروكة
    $stats['total'] = $wpdb->get_var("
        SELECT COUNT(*) 
        FROM {$wpdb->posts} p
        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'shop_order'
        AND p.post_status = 'wc-draft'
        AND (pm.meta_key = '_pexlat_form_draft_data' OR pm.meta_key = '_pexlat_form_form_id')
    ");
    
    // الطلبات المتروكة اليوم
    $stats['today'] = $wpdb->get_var("
        SELECT COUNT(*) 
        FROM {$wpdb->posts} p
        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'shop_order'
        AND p.post_status = 'wc-draft'
        AND (pm.meta_key = '_pexlat_form_draft_data' OR pm.meta_key = '_pexlat_form_form_id')
        AND DATE(p.post_date) = CURDATE()
    ");
    
    // الطلبات المتروكة هذا الأسبوع
    $stats['week'] = $wpdb->get_var("
        SELECT COUNT(*) 
        FROM {$wpdb->posts} p
        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'shop_order'
        AND p.post_status = 'wc-draft'
        AND (pm.meta_key = '_pexlat_form_draft_data' OR pm.meta_key = '_pexlat_form_form_id')
        AND p.post_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    ");
    
    // الطلبات القديمة (أكثر من المدة المحددة)
    $cleanup_hours = intval(get_option('pexlat_form_abandoned_orders_cleanup_time', 24));
    $stats['old'] = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(*) 
        FROM {$wpdb->posts} p
        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'shop_order'
        AND p.post_status = 'wc-draft'
        AND (pm.meta_key = '_pexlat_form_draft_data' OR pm.meta_key = '_pexlat_form_form_id')
        AND p.post_date < DATE_SUB(NOW(), INTERVAL %d HOUR)
    ", $cleanup_hours));
    
    return $stats;
}

// الحصول على قائمة الطلبات المتروكة الحديثة
function get_recent_abandoned_orders($limit = 10) {
    global $wpdb;
    
    $orders = $wpdb->get_results($wpdb->prepare("
        SELECT DISTINCT p.ID, p.post_date, 
               pm_phone.meta_value as phone,
               pm_name.meta_value as customer_name
        FROM {$wpdb->posts} p
        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        LEFT JOIN {$wpdb->postmeta} pm_phone ON p.ID = pm_phone.post_id AND pm_phone.meta_key = '_customer_phone'
        LEFT JOIN {$wpdb->postmeta} pm_name ON p.ID = pm_name.post_id AND pm_name.meta_key = '_customer_full_name'
        WHERE p.post_type = 'shop_order'
        AND p.post_status = 'wc-draft'
        AND (pm.meta_key = '_pexlat_form_draft_data' OR pm.meta_key = '_pexlat_form_form_id')
        ORDER BY p.post_date DESC
        LIMIT %d
    ", $limit));
    
    return $orders;
}

$stats = get_abandoned_orders_stats();
$recent_orders = get_recent_abandoned_orders();

// معالجة طلبات التنظيف اليدوي
if (isset($_POST['cleanup_old_orders']) && wp_verify_nonce($_POST['_wpnonce'], 'cleanup_abandoned_orders')) {
    $cleanup_hours = intval(get_option('pexlat_form_abandoned_orders_cleanup_time', 24));
    
    global $wpdb;
    $old_orders = $wpdb->get_col($wpdb->prepare("
        SELECT DISTINCT p.ID 
        FROM {$wpdb->posts} p
        INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
        WHERE p.post_type = 'shop_order'
        AND p.post_status = 'wc-draft'
        AND (pm.meta_key = '_pexlat_form_draft_data' OR pm.meta_key = '_pexlat_form_form_id')
        AND p.post_date < DATE_SUB(NOW(), INTERVAL %d HOUR)
    ", $cleanup_hours));
    
    $cleaned_count = 0;
    foreach ($old_orders as $order_id) {
        wp_delete_post($order_id, true);
        $cleaned_count++;
    }
    
    echo '<div class="notice notice-success"><p>تم حذف ' . $cleaned_count . ' طلب متروك قديم بنجاح.</p></div>';
    
    // تحديث الإحصائيات
    $stats = get_abandoned_orders_stats();
}
?>

<div class="wrap">
    <h1>مراقب الطلبات المتروكة</h1>
    
    <!-- إحصائيات سريعة -->
    <div class="abandoned-orders-stats">
        <div class="stats-grid">
            <div class="stat-card">
                <h3>إجمالي الطلبات المتروكة</h3>
                <span class="stat-number"><?php echo number_format($stats['total']); ?></span>
            </div>
            
            <div class="stat-card">
                <h3>اليوم</h3>
                <span class="stat-number"><?php echo number_format($stats['today']); ?></span>
            </div>
            
            <div class="stat-card">
                <h3>هذا الأسبوع</h3>
                <span class="stat-number"><?php echo number_format($stats['week']); ?></span>
            </div>
            
            <div class="stat-card warning">
                <h3>طلبات قديمة</h3>
                <span class="stat-number"><?php echo number_format($stats['old']); ?></span>
                <small>أكثر من <?php echo get_option('pexlat_form_abandoned_orders_cleanup_time', 24); ?> ساعة</small>
            </div>
        </div>
    </div>
    
    <!-- أدوات التحكم -->
    <div class="abandoned-orders-controls">
        <h2>أدوات التحكم</h2>
        
        <form method="post" style="display: inline-block;">
            <?php wp_nonce_field('cleanup_abandoned_orders'); ?>
            <input type="submit" name="cleanup_old_orders" class="button button-secondary" 
                   value="تنظيف الطلبات القديمة (<?php echo $stats['old']; ?>)" 
                   onclick="return confirm('هل أنت متأكد من حذف جميع الطلبات المتروكة القديمة؟');">
        </form>
        
        <a href="<?php echo admin_url('edit.php?post_type=shop_order&post_status=wc-draft'); ?>" 
           class="button">عرض جميع الطلبات المتروكة</a>
    </div>
    
    <!-- الطلبات الحديثة -->
    <div class="recent-abandoned-orders">
        <h2>الطلبات المتروكة الحديثة</h2>
        
        <?php if (!empty($recent_orders)): ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>رقم الطلب</th>
                    <th>اسم العميل</th>
                    <th>رقم الهاتف</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($recent_orders as $order): ?>
                <tr>
                    <td>
                        <a href="<?php echo admin_url('post.php?post=' . $order->ID . '&action=edit'); ?>">
                            #<?php echo $order->ID; ?>
                        </a>
                    </td>
                    <td><?php echo esc_html($order->customer_name ?: 'غير محدد'); ?></td>
                    <td><?php echo esc_html($order->phone ?: 'غير محدد'); ?></td>
                    <td><?php echo date('Y-m-d H:i', strtotime($order->post_date)); ?></td>
                    <td>
                        <a href="<?php echo admin_url('post.php?post=' . $order->ID . '&action=edit'); ?>" 
                           class="button button-small">عرض</a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <p>لا توجد طلبات متروكة حديثة.</p>
        <?php endif; ?>
    </div>
</div>

<style>
.abandoned-orders-stats {
    margin: 20px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-card.warning {
    border-color: #f0ad4e;
    background: #fcf8e3;
}

.stat-card h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #666;
}

.stat-number {
    display: block;
    font-size: 32px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.stat-card.warning .stat-number {
    color: #8a6d3b;
}

.abandoned-orders-controls {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.recent-abandoned-orders {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}
</style>
