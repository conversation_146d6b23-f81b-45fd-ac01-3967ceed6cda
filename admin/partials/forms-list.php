<?php
/**
 * Forms list template
 */

// Get forms from database
global $wpdb;
$table_name = $wpdb->prefix . 'pexlat_form_forms';
$forms = $wpdb->get_results("SELECT * FROM {$table_name} ORDER BY id DESC");

// Get default form ID
$default_form_id = get_option('pexlat_form_default_form_id', 0);

// Display messages if any
if (isset($_GET['message'])) {
    $message_type = intval($_GET['message']);

    $messages = array(
        1 => __('تم حذف النموذج بنجاح.', 'pexlat-form'),
        2 => __('تم تحديث حالة النموذج بنجاح.', 'pexlat-form'),
    );

    if (isset($messages[$message_type])) {
        echo '<div class="notice notice-success is-dismissible"><p>' . $messages[$message_type] . '</p></div>';
    }
}

// Display settings errors
settings_errors('pexlat_form_settings');
?>

<div class="wrap">
    <h1 class="wp-heading-inline">Pexlat Form</h1>
    <a href="<?php echo admin_url('admin.php?page=pexlat-form-edit&id=new'); ?>" class="page-title-action"><?php _e('إضافة نموذج جديد', 'pexlat-form'); ?></a>
    <hr class="wp-header-end">

    <!-- شريط الشعار -->
    <?php include(plugin_dir_path(dirname(__FILE__)) . 'partials/header-bar.php'); ?>

    <?php if (empty($forms)) : ?>
        <div class="notice notice-info">
            <p><?php _e('لا توجد نماذج بعد.', 'pexlat-form'); ?> <a href="<?php echo admin_url('admin.php?page=pexlat-form-edit&id=new'); ?>"><?php _e('إضافة نموذج جديد', 'pexlat-form'); ?></a>.</p>
        </div>
    <?php else : ?>
        <div class="tablenav top">
            <form method="post" action="" class="alignleft">
                <?php wp_nonce_field('update_default_form', 'pexlat_form_update_default_form_nonce'); ?>
                <label for="default-form-id"><?php _e('النموذج الافتراضي للمنتجات:', 'pexlat-form'); ?></label>
                <select name="default_form_id" id="default-form-id">
                    <option value="0"<?php selected($default_form_id, 0); ?>><?php _e('-- اختر النموذج الافتراضي --', 'pexlat-form'); ?></option>
                    <?php foreach ($forms as $form) : ?>
                        <?php if ($form->status === 'active') : ?>
                            <option value="<?php echo $form->id; ?>"<?php selected($default_form_id, $form->id); ?>>
                                <?php echo $form->title; ?>
                            </option>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </select>
                <input type="submit" class="button" value="تحديث">
                <p class="description">سيتم استخدام هذا النموذج في جميع صفحات المنتجات ما لم يتم تحديد نموذج مخصص.</p>
            </form>
            <br class="clear">
        </div>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th scope="col" class="manage-column column-id">ID</th>
                    <th scope="col" class="manage-column column-title column-primary">العنوان</th>
                    <th scope="col" class="manage-column column-description">الوصف</th>
                    <th scope="col" class="manage-column column-status">الحالة</th>
                    <th scope="col" class="manage-column column-shortcode">الشورت كود</th>
                    <th scope="col" class="manage-column column-date">تاريخ الإنشاء</th>
                    <th scope="col" class="manage-column column-actions">إجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($forms as $form) : ?>
                    <tr>
                        <td><?php echo $form->id; ?></td>
                        <td class="title column-title has-row-actions column-primary">
                            <strong>
                                <a href="<?php echo admin_url('admin.php?page=pexlat-form-edit&id=' . $form->id); ?>">
                                    <?php echo $form->title; ?>
                                </a>
                                <?php if ($form->id == $default_form_id) : ?>
                                    <span class="post-state"> — <span class="dashicons dashicons-yes"></span> افتراضي</span>
                                <?php endif; ?>
                            </strong>
                            <div class="row-actions">
                                <span class="edit">
                                    <a href="<?php echo admin_url('admin.php?page=pexlat-form-edit&id=' . $form->id); ?>">
                                        تعديل
                                    </a> |
                                </span>
                                <span class="status">
                                    <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=pexlat-form&action=toggle_status&id=' . $form->id), 'toggle_form_status_' . $form->id); ?>">
                                        <?php echo $form->status === 'active' ? 'تعطيل' : 'تفعيل'; ?>
                                    </a> |
                                </span>
                                <span class="submissions">
                                    <a href="<?php echo admin_url('admin.php?page=pexlat-form-registrations&form_id=' . $form->id); ?>">
                                        عرض الطلبات
                                    </a> |
                                </span>
                                <span class="delete">
                                    <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=pexlat-form&action=delete&id=' . $form->id), 'delete_form_' . $form->id); ?>" class="submitdelete" onclick="return confirm('هل أنت متأكد من حذف هذا النموذج؟');">
                                        حذف
                                    </a>
                                </span>
                            </div>
                        </td>
                        <td><?php echo $form->description; ?></td>
                        <td>
                            <?php
                            if ($form->status === 'active') {
                                echo '<span class="status-active">مفعّل</span>';
                            } else {
                                echo '<span class="status-inactive">غير مفعّل</span>';
                            }
                            ?>
                        </td>
                        <td>
                            <code onclick="navigator.clipboard.writeText(this.innerText); alert('تم نسخ الكود!');">
                                [pexlat_form id="<?php echo $form->id; ?>"]
                            </code>
                        </td>
                        <td>
                            <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($form->created_at)); ?>
                        </td>
                        <td>
                            <a href="<?php echo admin_url('admin.php?page=pexlat-form-edit&id=' . $form->id); ?>" class="button button-small">
                                <span class="dashicons dashicons-edit"></span> تعديل
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
            <tfoot>
                <tr>
                    <th scope="col" class="manage-column column-id">ID</th>
                    <th scope="col" class="manage-column column-title column-primary">العنوان</th>
                    <th scope="col" class="manage-column column-description">الوصف</th>
                    <th scope="col" class="manage-column column-status">الحالة</th>
                    <th scope="col" class="manage-column column-shortcode">الشورت كود</th>
                    <th scope="col" class="manage-column column-date">تاريخ الإنشاء</th>
                    <th scope="col" class="manage-column column-actions">إجراءات</th>
                </tr>
            </tfoot>
        </table>
    <?php endif; ?>

    <div class="pexlat-form-info-box">
        <h3><?php _e('كيفية استخدام النماذج', 'pexlat-form'); ?></h3>
        <p><?php _e('هناك عدة طرق لعرض النماذج على موقعك:', 'pexlat-form'); ?></p>

        <ol>
            <li><strong><?php _e('في صفحات المنتجات:', 'pexlat-form'); ?></strong> <?php _e('يمكنك تعيين النموذج الافتراضي أعلاه، وسيظهر تلقائياً في جميع صفحات المنتجات.', 'pexlat-form'); ?></li>
            <li><strong><?php _e('باستخدام الشورت كود:', 'pexlat-form'); ?></strong> <?php _e('يمكنك نسخ الشورت كود من الجدول أعلاه ولصقه في أي مكان من موقعك مع تحديد رقم النموذج و id المنتج مثال : [pexlat_form id="1" product_id="123"] .', 'pexlat-form'); ?></li>
            <li><strong><?php _e('برمجياً:', 'pexlat-form'); ?></strong> <?php _e('يمكنك استخدام هذا الكود', 'pexlat-form'); ?> <code>&lt;?php echo do_shortcode('[pexlat_form id="1" product_id="123"]'); ?&gt;</code> <?php _e('في قالب الووردبريس الخاص بك.', 'pexlat-form'); ?></li>
        </ol>
    </div>
</div>

<style>
.status-active {
    color: #46b450;
    font-weight: bold;
}
.status-inactive {
    color: #dc3232;
    font-weight: bold;
}
.pexlat-form-info-box {
    background: #fff;
    border: 1px solid #ccd0d4;
    padding: 15px;
    margin-top: 20px;
    border-radius: 4px;
}
</style>