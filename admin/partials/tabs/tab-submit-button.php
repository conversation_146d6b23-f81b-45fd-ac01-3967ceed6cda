<?php
/**
 * قالب إعدادات زر الإرسال
 *
 * @var array $form_settings إعدادات النموذج
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="settings-group">
    <!-- قسم معاينة الزر -->
    <div class="button-preview-section">
        <h3>معاينة الزر</h3>
        <div class="button-preview-container">
            <button type="button" id="button-preview" class="form-submit-button">
                <span class="button-icon"></span>
                <span class="button-text">إتمام الطلب</span>
            </button>
        </div>
    </div>

    <!-- إعدادات الزر الأساسية -->
    <div class="settings-field">
        <label for="button-text">نص الزر</label>
        <input type="text" name="settings[button_text]" id="button-text" class="regular-text"
               value="<?php echo esc_attr($form_settings['button_text'] ?? 'إتمام الطلب'); ?>">
        <p class="description">النص الذي سيظهر على زر إرسال النموذج</p>
    </div>

    <div class="settings-field">
        <label for="button-color">لون الزر</label>
        <input type="text" name="settings[button_color]" id="button-color" class="color-picker"
               value="<?php echo esc_attr($form_settings['button_color'] ?? '#4CAF50'); ?>">
    </div>

    <div class="settings-field">
        <label for="button-text-color">لون النص</label>
        <input type="text" name="settings[button_text_color]" id="button-text-color" class="color-picker"
               value="<?php echo esc_attr($form_settings['button_text_color'] ?? '#ffffff'); ?>">
    </div>

    <div class="settings-field">
        <label for="button-size">حجم الزر</label>
        <select name="settings[button_size]" id="button-size">
            <option value="small" <?php selected($form_settings['button_size'] ?? 'medium', 'small'); ?>>صغير</option>
            <option value="medium" <?php selected($form_settings['button_size'] ?? 'medium', 'medium'); ?>>متوسط</option>
            <option value="large" <?php selected($form_settings['button_size'] ?? 'medium', 'large'); ?>>كبير</option>
        </select>
    </div>

    <div class="settings-field">
        <label for="button-border-radius">استدارة الحواف</label>
        <input type="range" name="settings[button_border_radius]" id="button-border-radius"
               min="0" max="50" step="1" value="<?php echo esc_attr($form_settings['button_border_radius'] ?? '4'); ?>"
               oninput="this.nextElementSibling.value = this.value + 'px'">
        <output><?php echo esc_attr($form_settings['button_border_radius'] ?? '4'); ?>px</output>
    </div>

    <div class="settings-field">
        <label for="button-hover-effect">تأثير التحويم</label>
        <select name="settings[button_hover_effect]" id="button-hover-effect">
            <option value="none" <?php selected($form_settings['button_hover_effect'] ?? 'shadow', 'none'); ?>>بدون</option>
            <option value="shadow" <?php selected($form_settings['button_hover_effect'] ?? 'shadow', 'shadow'); ?>>ظل</option>
            <option value="lift" <?php selected($form_settings['button_hover_effect'] ?? 'shadow', 'lift'); ?>>رفع</option>
            <option value="glow" <?php selected($form_settings['button_hover_effect'] ?? 'shadow', 'glow'); ?>>توهج</option>
        </select>
    </div>

    <div class="settings-field">
        <label for="button-animation">تأثير حركي</label>
        <select name="settings[button_animation]" id="button-animation">
            <option value="none" <?php selected($form_settings['button_animation'] ?? 'none', 'none'); ?>>بدون</option>
            <option value="pulse" <?php selected($form_settings['button_animation'] ?? 'none', 'pulse'); ?>>نبضات</option>
            <option value="bounce" <?php selected($form_settings['button_animation'] ?? 'none', 'bounce'); ?>>قفز</option>
            <option value="tada" <?php selected($form_settings['button_animation'] ?? 'none', 'tada'); ?>>تادا</option>
            <option value="shake" <?php selected($form_settings['button_animation'] ?? 'none', 'shake'); ?>>اهتزاز</option>
        </select>
        <p class="description">تأثير حركي مستمر للزر لجذب الانتباه</p>
    </div>

    <!-- إعدادات الأيقونة -->
    <div class="settings-field">
        <label for="button-icon">أيقونة الزر</label>
        <select name="settings[button_icon]" id="button-icon">
            <option value="none" <?php selected($form_settings['button_icon'] ?? 'check', 'none'); ?>>بدون أيقونة</option>
            <option value="check" <?php selected($form_settings['button_icon'] ?? 'check', 'check'); ?>>علامة صح</option>
            <option value="arrow" <?php selected($form_settings['button_icon'] ?? 'check', 'arrow'); ?>>سهم</option>

        </select>
    </div>

    <div class="settings-field">
        <label for="button-icon-position">موضع الأيقونة</label>
        <select name="settings[button_icon_position]" id="button-icon-position">
            <option value="left" <?php selected($form_settings['button_icon_position'] ?? 'right', 'left'); ?>>يسار</option>
            <option value="right" <?php selected($form_settings['button_icon_position'] ?? 'right', 'right'); ?>>يمين</option>
        </select>
    </div>

    <!-- إعدادات عرض السعر في الزر -->
    <div class="settings-field">
        <div class="toggle-switch-container">
            <label class="switch-toggle">
                <input type="checkbox" id="show-total-price-in-button" name="settings[show_total_price_in_button]" value="1"
                    <?php checked(isset($form_settings['show_total_price_in_button']) && $form_settings['show_total_price_in_button'] == 1 ? 1 : 0, 1); ?>>
                <span class="toggle-slider"></span>
            </label>
            <span>عرض السعر الإجمالي في زر الطلب</span>
        </div>
        <!-- حقل مخفي للتأكد من إرسال القيمة حتى لو لم يتم تحديد الخيار -->
        <input type="hidden" name="settings[show_total_price_in_button_submitted]" value="1">
        <p class="description">عند تفعيل هذا الخيار، سيظهر السعر الإجمالي بجانب نص الزر ويتم تحديثه تلقائياً</p>
    </div>

    <!-- إعدادات زر الواتساب -->
    <h3>إعدادات زر الواتساب</h3>

    <div class="settings-field">
        <div class="toggle-switch-container">
            <label class="switch-toggle">
                <input type="checkbox" id="enable-whatsapp-button" name="settings[enable_whatsapp_button]" value="1"
                    <?php checked(isset($form_settings['enable_whatsapp_button']) && $form_settings['enable_whatsapp_button'] == 1 ? 1 : 0, 1); ?>>
                <span class="toggle-slider"></span>
            </label>
            <span>تفعيل زر الطلب عبر الواتساب</span>
        </div>
        <p class="description">عند تفعيل هذا الخيار، سيظهر زر الطلب عبر الواتساب بجانب زر إتمام الطلب</p>
    </div>

    <div class="settings-field whatsapp-settings" style="<?php echo isset($form_settings['enable_whatsapp_button']) && $form_settings['enable_whatsapp_button'] == 1 ? '' : 'display: none;'; ?>">
        <label for="whatsapp-number">رقم الواتساب</label>
        <input type="text" name="settings[whatsapp_number]" id="whatsapp-number" class="regular-text"
               value="<?php echo esc_attr(isset($form_settings['whatsapp_number']) ? $form_settings['whatsapp_number'] : ''); ?>" placeholder="213123456789">
        <p class="description">أدخل رقم الواتساب الخاص بالمتجر مع رمز البلد (مثال: 213 للجزائر)</p>
    </div>

    <div class="settings-field whatsapp-settings" style="<?php echo isset($form_settings['enable_whatsapp_button']) && $form_settings['enable_whatsapp_button'] == 1 ? '' : 'display: none;'; ?>">
        <label for="whatsapp-button-text">نص زر الواتساب</label>
        <input type="text" name="settings[whatsapp_button_text]" id="whatsapp-button-text" class="regular-text"
               value="<?php echo esc_attr(isset($form_settings['whatsapp_button_text']) ? $form_settings['whatsapp_button_text'] : 'طلب عبر الواتساب'); ?>">
        <p class="description">النص الذي سيظهر على زر الطلب عبر الواتساب</p>
    </div>

    <div class="settings-field whatsapp-settings" style="<?php echo isset($form_settings['enable_whatsapp_button']) && $form_settings['enable_whatsapp_button'] == 1 ? '' : 'display: none;'; ?>">
        <label for="whatsapp-button-color">لون زر الواتساب</label>
        <input type="text" name="settings[whatsapp_button_color]" id="whatsapp-button-color" class="color-picker"
               value="<?php echo esc_attr(isset($form_settings['whatsapp_button_color']) ? $form_settings['whatsapp_button_color'] : '#25D366'); ?>">
        <p class="description">اختر لون زر الطلب عبر الواتساب (الافتراضي هو لون الواتساب الرسمي)</p>
    </div>





    <!-- إعدادات التدرج اللوني -->
    <div class="settings-field gradient-settings">
        <div class="toggle-switch-container">
            <label class="switch-toggle">
                <input type="checkbox" id="button-gradient-toggle"
                       <?php checked($form_settings['button_gradient'] ?? 'no', 'yes'); ?>>
                <span class="toggle-slider"></span>
            </label>
            <span class="toggle-label">تدرج لوني</span>
            <input type="hidden" name="settings[button_gradient]" id="button-gradient"
                   value="<?php echo esc_attr($form_settings['button_gradient'] ?? 'no'); ?>">
        </div>

        <div class="gradient-options" style="<?php echo ($form_settings['button_gradient'] ?? 'no') === 'no' ? 'display: none;' : ''; ?>">
            <div class="gradient-field">
                <label for="button-gradient-color">لون التدرج الثانوي</label>
                <input type="text" name="settings[button_gradient_color]" id="button-gradient-color" class="color-picker"
                       value="<?php echo esc_attr($form_settings['button_gradient_color'] ?? '#38a169'); ?>">
            </div>

            <div class="gradient-field">
                <label for="button-gradient-direction">اتجاه التدرج</label>
                <select name="settings[button_gradient_direction]" id="button-gradient-direction">
                    <option value="to right" <?php selected($form_settings['button_gradient_direction'] ?? 'to bottom', 'to right'); ?>>من اليسار إلى اليمين</option>
                    <option value="to left" <?php selected($form_settings['button_gradient_direction'] ?? 'to bottom', 'to left'); ?>>من اليمين إلى اليسار</option>
                    <option value="to bottom" <?php selected($form_settings['button_gradient_direction'] ?? 'to bottom', 'to bottom'); ?>>من الأعلى إلى الأسفل</option>
                    <option value="to top" <?php selected($form_settings['button_gradient_direction'] ?? 'to bottom', 'to top'); ?>>من الأسفل إلى الأعلى</option>
                    <option value="to bottom right" <?php selected($form_settings['button_gradient_direction'] ?? 'to bottom', 'to bottom right'); ?>>قطري إلى الأسفل يمين</option>
                    <option value="to bottom left" <?php selected($form_settings['button_gradient_direction'] ?? 'to bottom', 'to bottom left'); ?>>قطري إلى الأسفل يسار</option>
                </select>
            </div>
        </div>
    </div>
</div>

<style>
/* تنسيق قسم المعاينة */
.button-preview-section {
    background: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

.button-preview-section h3 {
    margin: 0 0 15px 0;
    color: #1d2327;
    font-size: 14px;
}

.button-preview-container {
    background: #fff;
    border: 1px dashed #e2e4e7;
    border-radius: 4px;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.form-submit-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.form-submit-button .button-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.form-submit-button .dashicons {
    width: 16px;
    height: 16px;
    font-size: 16px;
    margin: 0;
}

/* تأثيرات التحويم */
.form-submit-button.hover-shadow:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.form-submit-button.hover-lift:hover {
    transform: translateY(-3px);
}

.form-submit-button.hover-glow:hover {
    box-shadow: 0 0 20px rgba(0,0,0,0.2);
}

/* تأثيرات حركية */
.form-submit-button.animation-pulse {
    animation: button_pulse 1.5s infinite;
}

.form-submit-button.animation-bounce {
    animation: button_bounce 1.5s infinite;
}

.form-submit-button.animation-tada {
    animation: button_tada 1.5s infinite;
}

.form-submit-button.animation-shake {
    animation: button_shake 1.5s infinite;
}

@keyframes button_pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes button_bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

@keyframes button_tada {
    0% { transform: scale(1); }
    10%, 20% { transform: scale(0.9) rotate(-3deg); }
    30%, 50%, 70%, 90% { transform: scale(1.1) rotate(3deg); }
    40%, 60%, 80% { transform: scale(1.1) rotate(-3deg); }
    100% { transform: scale(1) rotate(0); }
}

@keyframes button_shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* تنسيق قسم التدرج */
.gradient-settings {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #e2e4e7;
}

.gradient-options {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e2e4e7;
}

.gradient-field {
    margin-bottom: 15px;
}

.gradient-field:last-child {
    margin-bottom: 0;
}

.gradient-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

#button-gradient-direction {
    width: 100%;
    max-width: 300px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // تحديث معاينة الزر
    function updateButtonPreview() {
        var $button = $('#button-preview');
        var $buttonText = $button.find('.button-text');
        var $buttonIcon = $button.find('.button-icon');

        // تحديث النص
        $buttonText.text($('#button-text').val() || 'إتمام الطلب');

        // تحديث الألوان والتدرج
        var buttonColor = $('#button-color').val();
        var textColor = $('#button-text-color').val();
        var isGradient = $('#button-gradient-toggle').is(':checked');

        if (isGradient) {
            var gradientColor = $('#button-gradient-color').val();
            var direction = $('#button-gradient-direction').val();
            $button.css('background', `linear-gradient(${direction}, ${buttonColor}, ${gradientColor})`);
        } else {
            $button.css('background', buttonColor);
        }

        $button.css('color', textColor);

        // تحديث الحجم
        var sizes = {
            'small': { padding: '8px 16px', fontSize: '14px' },
            'medium': { padding: '12px 24px', fontSize: '16px' },
            'large': { padding: '16px 32px', fontSize: '18px' }
        };
        var selectedSize = sizes[$('#button-size').val()] || sizes.medium;
        $button.css({
            padding: selectedSize.padding,
            fontSize: selectedSize.fontSize
        });

        // تحديث الاستدارة
        $button.css('border-radius', $('#button-border-radius').val() + 'px');

        // تحديث الأيقونة
        var icon = $('#button-icon').val();
        var iconPosition = $('#button-icon-position').val();

        $buttonIcon.empty();
        if (icon !== 'none') {
            var iconClass = {
                'check': 'dashicons-yes',
                'arrow': 'dashicons-arrow-left',

            }[icon];

            $buttonIcon.html('<span class="dashicons ' + iconClass + '"></span>');
            $button.attr('data-icon-position', iconPosition);

            if (iconPosition === 'right') {
                $buttonIcon.appendTo($button);
            } else {
                $buttonIcon.prependTo($button);
            }
        }

        // تحديث تأثير التحويم
        $button.removeClass('hover-shadow hover-lift hover-glow')
              .addClass('hover-' + $('#button-hover-effect').val());

        // تحديث التأثير الحركي
        $button.removeClass('animation-pulse animation-bounce animation-tada animation-shake');
        var animation = $('#button-animation').val();
        if (animation !== 'none') {
            $button.addClass('animation-' + animation);
        }
    }

    // معالجة تغيير التدرج
    $('#button-gradient-toggle').on('change', function() {
        var isChecked = $(this).is(':checked');
        $('#button-gradient').val(isChecked ? 'yes' : 'no');
        $('.gradient-options').slideToggle(300);
        updateButtonPreview();
    });

    // تحديث المعاينة عند تغيير أي إعداد
    $('.settings-group input, .settings-group select').on('change input', updateButtonPreview);
    $('.color-picker').wpColorPicker({
        change: updateButtonPreview,
        clear: updateButtonPreview
    });

    // تحديث التدرج عند تغيير الاتجاه
    $('#button-gradient-direction').on('change', function() {
        if ($('#button-gradient-toggle').is(':checked')) {
            updateButtonPreview();
        }
    });

    // إظهار/إخفاء إعدادات الواتساب عند تفعيل/تعطيل الزر
    $('#enable-whatsapp-button').on('change', function() {
        var isEnabled = $(this).is(':checked');
        $('.whatsapp-settings').toggle(isEnabled);
        console.log('تم تغيير حالة زر الواتساب إلى: ' + (isEnabled ? 'مفعل' : 'معطل'));
    });

    // تحديث حالة حقول الواتساب عند التحميل
    var isWhatsappEnabled = $('#enable-whatsapp-button').is(':checked');
    $('.whatsapp-settings').toggle(isWhatsappEnabled);
    console.log('حالة زر الواتساب عند التحميل: ' + (isWhatsappEnabled ? 'مفعل' : 'معطل'));



    // التحديث المبدئي
    updateButtonPreview();
});
</script>