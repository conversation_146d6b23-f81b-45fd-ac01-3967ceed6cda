<?php
/**
 * تبويب تصدير/استيراد إعدادات النموذج
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="tab-content" id="import-export-tab">
    <div class="import-export-container">
        
        <!-- قسم تصدير الإعدادات -->
        <div class="export-section">
            <div class="section-header">
                <h3 class="section-title">
                    <span class="dashicons dashicons-download"></span>
                    تصدير إعدادات النموذج
                </h3>
                <p class="section-description">
                    قم بتصدير جميع إعدادات النموذج الحالية كملف JSON لحفظ نسخة احتياطية أو نقلها إلى موقع آخر.
                </p>
            </div>
            
            <div class="export-options">
                <div class="export-option-group">
                    <h4>اختر البيانات المراد تصديرها:</h4>
                    <div class="export-checkboxes">
                        <label class="export-checkbox">
                            <input type="checkbox" name="export_data[]" value="form_info" checked>
                            <span class="checkmark"></span>
                            معلومات النموذج الأساسية
                        </label>
                        <label class="export-checkbox">
                            <input type="checkbox" name="export_data[]" value="form_settings" checked>
                            <span class="checkmark"></span>
                            إعدادات النموذج العامة
                        </label>
                        <label class="export-checkbox">
                            <input type="checkbox" name="export_data[]" value="form_fields" checked>
                            <span class="checkmark"></span>
                            حقول النموذج
                        </label>
                        <label class="export-checkbox">
                            <input type="checkbox" name="export_data[]" value="shipping_settings" checked>
                            <span class="checkmark"></span>
                            إعدادات طرق التوصيل
                        </label>
                        <label class="export-checkbox">
                            <input type="checkbox" name="export_data[]" value="design_settings" checked>
                            <span class="checkmark"></span>
                            إعدادات التصميم والألوان
                        </label>
                        <label class="export-checkbox">
                            <input type="checkbox" name="export_data[]" value="advanced_settings" checked>
                            <span class="checkmark"></span>
                            الإعدادات المتقدمة
                        </label>
                    </div>
                </div>
                
                <div class="export-actions">
                    <button type="button" id="export-settings-btn" class="button button-primary">
                        <span class="dashicons dashicons-download"></span>
                        تصدير الإعدادات
                    </button>
                    <div class="export-status" id="export-status"></div>
                </div>
            </div>
        </div>

        <!-- فاصل -->
        <div class="section-divider"></div>

        <!-- قسم استيراد الإعدادات -->
        <div class="import-section">
            <div class="section-header">
                <h3 class="section-title">
                    <span class="dashicons dashicons-upload"></span>
                    استيراد إعدادات النموذج
                </h3>
                <p class="section-description">
                    قم برفع ملف JSON يحتوي على إعدادات النموذج لاستيرادها. سيتم استبدال الإعدادات الحالية.
                </p>
            </div>
            
            <div class="import-options">
                <div class="import-file-section">
                    <div class="file-upload-area" id="file-upload-area">
                        <div class="upload-icon">
                            <span class="dashicons dashicons-cloud-upload"></span>
                        </div>
                        <div class="upload-text">
                            <p class="upload-main-text">اسحب وأفلت ملف JSON هنا أو <span class="click-to-select">انقر للاختيار</span></p>
                            <p class="upload-sub-text">الحد الأقصى لحجم الملف: 5MB</p>
                        </div>
                        <input type="file" id="import-file-input" accept=".json,application/json" style="position: absolute; left: -9999px; opacity: 0;" multiple="false">
                    </div>
                    
                    <div class="file-info" id="file-info" style="display: none;">
                        <div class="file-details">
                            <span class="dashicons dashicons-media-document"></span>
                            <span class="file-name" id="file-name"></span>
                            <span class="file-size" id="file-size"></span>
                        </div>
                        <button type="button" class="remove-file-btn" id="remove-file-btn">
                            <span class="dashicons dashicons-no"></span>
                        </button>
                    </div>
                </div>
                
                <div class="import-options-group">
                    <h4>خيارات الاستيراد:</h4>
                    <label class="import-option">
                        <input type="checkbox" name="backup_current" checked>
                        <span class="checkmark"></span>
                        إنشاء نسخة احتياطية من الإعدادات الحالية قبل الاستيراد
                    </label>
                    <label class="import-option">
                        <input type="checkbox" name="validate_data" checked>
                        <span class="checkmark"></span>
                        التحقق من صحة البيانات قبل التطبيق
                    </label>
                </div>
                
                <div class="import-actions">
                    <button type="button" id="import-settings-btn" class="button button-primary" disabled>
                        <span class="dashicons dashicons-upload"></span>
                        استيراد الإعدادات
                    </button>
                    <div class="import-status" id="import-status"></div>
                </div>
            </div>
        </div>

        <!-- قسم النسخ الاحتياطية -->
        <div class="backup-section">
            <div class="section-header">
                <h3 class="section-title">
                    <span class="dashicons dashicons-backup"></span>
                    النسخ الاحتياطية التلقائية
                </h3>
                <p class="section-description">
                    النسخ الاحتياطية التي تم إنشاؤها تلقائياً عند استيراد إعدادات جديدة.
                </p>
            </div>
            
            <div class="backup-list" id="backup-list">
                <div class="no-backups">
                    <span class="dashicons dashicons-info"></span>
                    لا توجد نسخ احتياطية حالياً
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* تنسيقات تبويب تصدير/استيراد الإعدادات */
.import-export-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px 0;
}

.export-section,
.import-section,
.backup-section {
    background: #fff;
    border: 1px solid #e2e4e7;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
    margin-bottom: 25px;
    border-bottom: 1px solid #f0f0f1;
    padding-bottom: 15px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 0 10px;
    font-size: 18px;
    font-weight: 600;
    color: #1d2327;
}

.section-title .dashicons {
    color: #2271b1;
    font-size: 20px;
}

.section-description {
    margin: 0;
    color: #646970;
    font-size: 14px;
    line-height: 1.5;
}

.section-divider {
    height: 1px;
    background: linear-gradient(to right, transparent, #e2e4e7, transparent);
    margin: 30px 0;
}

/* تنسيقات قسم التصدير */
.export-option-group h4,
.import-options-group h4 {
    margin: 0 0 15px;
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
}

.export-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
    margin-bottom: 25px;
}

.export-checkbox,
.import-option {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.export-checkbox:hover,
.import-option:hover {
    background-color: #f6f7f7;
}

.export-checkbox input[type="checkbox"],
.import-option input[type="checkbox"] {
    margin: 0;
    width: 16px;
    height: 16px;
    accent-color: #2271b1;
}

.export-actions,
.import-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
}

.export-status,
.import-status {
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 4px;
    display: none;
}

.export-status.success,
.import-status.success {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.export-status.error,
.import-status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* تنسيقات منطقة رفع الملفات */
.file-upload-area {
    border: 2px dashed #c3c4c7;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    user-select: none;
}

.file-upload-area:hover,
.file-upload-area.dragover {
    border-color: #2271b1;
    background-color: #f0f6fc;
}

.upload-icon .dashicons {
    font-size: 48px;
    color: #c3c4c7;
    margin-bottom: 15px;
}

.upload-main-text {
    font-size: 16px;
    font-weight: 500;
    color: #1d2327;
    margin: 0 0 5px;
}

.upload-sub-text {
    font-size: 13px;
    color: #646970;
    margin: 0;
}

.click-to-select {
    color: #2271b1;
    text-decoration: underline;
    cursor: pointer;
    font-weight: 500;
}

.click-to-select:hover {
    color: #135e96;
}

.file-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #f0f6fc;
    border: 1px solid #c3c4c7;
    border-radius: 6px;
    margin-bottom: 20px;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-details .dashicons {
    color: #2271b1;
}

.file-name {
    font-weight: 500;
    color: #1d2327;
}

.file-size {
    color: #646970;
    font-size: 13px;
}

.remove-file-btn {
    background: none;
    border: none;
    color: #d63638;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.remove-file-btn:hover {
    background-color: rgba(214, 54, 56, 0.1);
}

/* تنسيقات قسم النسخ الاحتياطية */
.backup-list {
    min-height: 60px;
}

.no-backups {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #646970;
    font-style: italic;
    padding: 20px;
}

.no-backups .dashicons {
    color: #c3c4c7;
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
    .import-export-container {
        padding: 10px;
    }

    .export-section,
    .import-section,
    .backup-section {
        padding: 20px 15px;
    }

    .export-checkboxes {
        grid-template-columns: 1fr;
    }

    .export-actions,
    .import-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    var selectedFile = null;

    // التحقق من وجود المتغيرات المطلوبة
    if (typeof ajaxurl === 'undefined') {
        console.error('ajaxurl غير معرف');
        return;
    }

    // معالج تصدير الإعدادات
    $('#export-settings-btn').on('click', function() {
        var $btn = $(this);
        var $status = $('#export-status');

        // جمع البيانات المحددة للتصدير
        var exportData = [];
        $('input[name="export_data[]"]:checked').each(function() {
            exportData.push($(this).val());
        });

        if (exportData.length === 0) {
            $status.removeClass('success').addClass('error').text('يرجى اختيار البيانات المراد تصديرها').show();
            return;
        }

        // التحقق من وجود معرف النموذج
        var formId = $('input[name="form_id"]').val();
        if (!formId) {
            $status.removeClass('success').addClass('error').text('معرف النموذج غير موجود').show();
            return;
        }

        // التحقق من وجود nonce
        var nonce = $('input[name="pexlat_form_form_nonce"]').val();
        if (!nonce) {
            $status.removeClass('success').addClass('error').text('رمز الأمان غير موجود').show();
            return;
        }

        // تعطيل الزر وإظهار حالة التحميل
        $btn.prop('disabled', true).html('<span class="dashicons dashicons-update-alt"></span> جاري التصدير...');
        $status.hide();

        // إرسال طلب AJAX
        console.log('إرسال طلب تصدير الإعدادات...', {
            action: 'pexlat_form_export_settings',
            form_id: formId,
            export_data: exportData,
            nonce: nonce
        });

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_export_settings',
                form_id: formId,
                export_data: exportData,
                nonce: nonce
            },
            success: function(response) {
                console.log('استجابة تصدير الإعدادات:', response);

                if (response.success) {
                    // إنشاء وتحميل الملف - استخدام البيانات المُصدرة فقط
                    var exportedData = response.data.data; // البيانات الفعلية المُصدرة
                    var dataStr = JSON.stringify(exportedData, null, 2);
                    var dataBlob = new Blob([dataStr], {type: 'application/json'});
                    var url = URL.createObjectURL(dataBlob);
                    var link = document.createElement('a');
                    link.href = url;
                    link.download = response.data.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);

                    $status.removeClass('error').addClass('success').text(response.data.message).show();
                } else {
                    console.error('خطأ في تصدير الإعدادات:', response);
                    $status.removeClass('success').addClass('error').text(response.data || 'حدث خطأ أثناء التصدير').show();
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ AJAX في تصدير الإعدادات:', {xhr: xhr, status: status, error: error});
                $status.removeClass('success').addClass('error').text('حدث خطأ في الاتصال: ' + error).show();
            },
            complete: function() {
                // إعادة تفعيل الزر
                $btn.prop('disabled', false).html('<span class="dashicons dashicons-download"></span> تصدير الإعدادات');
            }
        });
    });

    // معالج منطقة رفع الملفات
    var $uploadArea = $('#file-upload-area');
    var $fileInput = $('#import-file-input');
    var $fileInfo = $('#file-info');
    var $importBtn = $('#import-settings-btn');

    // النقر على منطقة الرفع - طريقة محسنة
    function triggerFileInput() {
        console.log('محاولة فتح نافذة اختيار الملف');
        var fileInput = document.getElementById('import-file-input');
        if (fileInput) {
            fileInput.click();
        } else {
            console.error('لم يتم العثور على input الملف');
        }
    }

    // معالجات متعددة للتأكد من عمل النقر
    $uploadArea.on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('تم النقر على منطقة الرفع');
        triggerFileInput();
    });

    // معالج خاص للنص القابل للنقر
    $(document).on('click', '.click-to-select', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('تم النقر على نص "انقر للاختيار"');
        triggerFileInput();
    });

    // معالج للعناصر الداخلية
    $uploadArea.find('.upload-icon, .upload-text, .upload-main-text').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('تم النقر على عنصر داخلي');
        triggerFileInput();
    });

    // السحب والإفلات
    $uploadArea.on('dragover dragenter', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).addClass('dragover');
    });

    $uploadArea.on('dragleave dragend', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('dragover');
    });

    $uploadArea.on('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $(this).removeClass('dragover');

        var files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelection(files[0]);
        }
    });

    // اختيار الملف
    $fileInput.on('change', function() {
        console.log('تم اختيار ملف:', this.files);
        if (this.files.length > 0) {
            handleFileSelection(this.files[0]);
        }
    });

    // إضافة معالج إضافي للتأكد من عمل input الملف
    $fileInput.on('click', function(e) {
        console.log('تم النقر على input الملف');
        // لا نمنع الحدث الافتراضي هنا
    });

    // معالج إضافي باستخدام addEventListener
    document.getElementById('file-upload-area').addEventListener('click', function(e) {
        console.log('معالج addEventListener - تم النقر على منطقة الرفع');
        e.preventDefault();
        var fileInput = document.getElementById('import-file-input');
        if (fileInput) {
            fileInput.click();
        }
    }, true);

    // معالجة اختيار الملف
    function handleFileSelection(file) {
        // التحقق من نوع الملف
        if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
            alert('يرجى اختيار ملف JSON فقط');
            return;
        }

        // التحقق من حجم الملف (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('حجم الملف كبير جداً. الحد الأقصى 5MB');
            return;
        }

        selectedFile = file;

        // إظهار معلومات الملف
        $('#file-name').text(file.name);
        $('#file-size').text(formatFileSize(file.size));
        $uploadArea.hide();
        $fileInfo.show();
        $importBtn.prop('disabled', false);
    }

    // إزالة الملف
    $('#remove-file-btn').on('click', function() {
        selectedFile = null;
        $fileInput.val('');
        $fileInfo.hide();
        $uploadArea.show();
        $importBtn.prop('disabled', true);
    });

    // معالج استيراد الإعدادات
    $importBtn.on('click', function() {
        if (!selectedFile) {
            alert('يرجى اختيار ملف للاستيراد');
            return;
        }

        var $btn = $(this);
        var $status = $('#import-status');

        // تعطيل الزر وإظهار حالة التحميل
        $btn.prop('disabled', true).html('<span class="dashicons dashicons-update-alt"></span> جاري الاستيراد...');
        $status.hide();

        // إنشاء FormData
        var formData = new FormData();
        formData.append('action', 'pexlat_form_import_settings');
        formData.append('form_id', $('input[name="form_id"]').val());
        formData.append('import_file', selectedFile);
        formData.append('backup_current', $('input[name="backup_current"]:checked').length > 0 ? 'true' : 'false');
        formData.append('validate_data', $('input[name="validate_data"]:checked').length > 0 ? 'true' : 'false');
        formData.append('nonce', $('input[name="pexlat_form_form_nonce"]').val());

        // إرسال طلب AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    $status.removeClass('error').addClass('success').text(response.data.message).show();

                    // إعادة تعيين النموذج
                    setTimeout(function() {
                        selectedFile = null;
                        $fileInput.val('');
                        $fileInfo.hide();
                        $uploadArea.show();
                        $btn.prop('disabled', true);

                        // إعادة تحميل الصفحة بعد 2 ثانية
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    }, 1000);
                } else {
                    $status.removeClass('success').addClass('error').text(response.data || 'حدث خطأ أثناء الاستيراد').show();
                }
            },
            error: function() {
                $status.removeClass('success').addClass('error').text('حدث خطأ في الاتصال').show();
            },
            complete: function() {
                // إعادة تفعيل الزر
                $btn.prop('disabled', false).html('<span class="dashicons dashicons-upload"></span> استيراد الإعدادات');
            }
        });
    });

    // دالة تنسيق حجم الملف
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // جلب وعرض النسخ الاحتياطية
    function loadBackups() {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_get_backups',
                nonce: $('input[name="pexlat_form_form_nonce"]').val()
            },
            success: function(response) {
                var $backupList = $('#backup-list');

                if (response.success && response.data.length > 0) {
                    var html = '';
                    $.each(response.data, function(index, backup) {
                        html += '<div class="backup-item">';
                        html += '<div class="backup-info">';
                        html += '<div class="backup-date">' + backup.formatted_date + '</div>';
                        html += '<div class="backup-type">نسخة احتياطية تلقائية - النموذج #' + backup.form_id + '</div>';
                        if (backup.form_title) {
                            html += '<div class="backup-form-title">' + backup.form_title + '</div>';
                        }
                        html += '</div>';
                        html += '<div class="backup-actions">';
                        html += '<button type="button" class="button button-small restore-backup-btn" data-backup-id="' + backup.id + '">';
                        html += '<span class="dashicons dashicons-backup"></span> استعادة';
                        html += '</button>';
                        html += '<button type="button" class="button button-small delete-backup-btn" data-backup-id="' + backup.id + '">';
                        html += '<span class="dashicons dashicons-trash"></span> حذف';
                        html += '</button>';
                        html += '</div>';
                        html += '</div>';
                    });
                    $backupList.html(html);
                } else {
                    $backupList.html('<div class="no-backups"><span class="dashicons dashicons-info"></span>لا توجد نسخ احتياطية حالياً</div>');
                }
            },
            error: function() {
                $('#backup-list').html('<div class="no-backups"><span class="dashicons dashicons-warning"></span>حدث خطأ في جلب النسخ الاحتياطية</div>');
            }
        });
    }

    // تحميل النسخ الاحتياطية عند تحميل الصفحة
    loadBackups();

    // معالج زر الاستعادة
    $(document).on('click', '.restore-backup-btn', function() {
        var $btn = $(this);
        var backupId = $btn.data('backup-id');

        if (!backupId) {
            alert('معرف النسخة الاحتياطية غير صحيح');
            return;
        }

        // تأكيد الاستعادة
        if (!confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم إنشاء نسخة احتياطية من الحالة الحالية تلقائياً.')) {
            return;
        }

        // تعطيل الزر وإظهار حالة التحميل
        $btn.prop('disabled', true).html('<span class="dashicons dashicons-update-alt"></span> جاري الاستعادة...');

        // إرسال طلب AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_restore_backup',
                backup_id: backupId,
                form_id: $('input[name="form_id"]').val(),
                nonce: $('input[name="pexlat_form_form_nonce"]').val()
            },
            success: function(response) {
                console.log('استجابة استعادة النسخة الاحتياطية:', response);

                if (response.success) {
                    alert(response.data.message);

                    // إعادة تحميل الصفحة بعد الاستعادة الناجحة
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    console.error('خطأ في استعادة النسخة الاحتياطية:', response);
                    alert('حدث خطأ: ' + (response.data || 'فشل في استعادة النسخة الاحتياطية'));
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ AJAX في استعادة النسخة الاحتياطية:', {xhr: xhr, status: status, error: error});
                alert('حدث خطأ في الاتصال: ' + error);
            },
            complete: function() {
                // إعادة تفعيل الزر
                $btn.prop('disabled', false).html('<span class="dashicons dashicons-backup"></span> استعادة');
            }
        });
    });

    // معالج زر حذف النسخة الاحتياطية
    $(document).on('click', '.delete-backup-btn', function() {
        var $btn = $(this);
        var backupId = $btn.data('backup-id');

        if (!backupId) {
            alert('معرف النسخة الاحتياطية غير صحيح');
            return;
        }

        // تأكيد الحذف
        if (!confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }

        // تعطيل الزر وإظهار حالة التحميل
        $btn.prop('disabled', true).html('<span class="dashicons dashicons-update-alt"></span> جاري الحذف...');

        // إرسال طلب AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_delete_backup',
                backup_id: backupId,
                nonce: $('input[name="pexlat_form_form_nonce"]').val()
            },
            success: function(response) {
                console.log('استجابة حذف النسخة الاحتياطية:', response);

                if (response.success) {
                    // إزالة العنصر من القائمة
                    $btn.closest('.backup-item').fadeOut(300, function() {
                        $(this).remove();

                        // التحقق من وجود نسخ احتياطية أخرى
                        if ($('#backup-list .backup-item').length === 0) {
                            $('#backup-list').html('<div class="no-backups"><span class="dashicons dashicons-info"></span>لا توجد نسخ احتياطية حالياً</div>');
                        }
                    });

                    // إظهار رسالة نجاح
                    alert(response.data.message);
                } else {
                    console.error('خطأ في حذف النسخة الاحتياطية:', response);
                    alert('حدث خطأ: ' + (response.data || 'فشل في حذف النسخة الاحتياطية'));

                    // إعادة تفعيل الزر
                    $btn.prop('disabled', false).html('<span class="dashicons dashicons-trash"></span> حذف');
                }
            },
            error: function(xhr, status, error) {
                console.error('خطأ AJAX في حذف النسخة الاحتياطية:', {xhr: xhr, status: status, error: error});
                alert('حدث خطأ في الاتصال: ' + error);

                // إعادة تفعيل الزر
                $btn.prop('disabled', false).html('<span class="dashicons dashicons-trash"></span> حذف');
            }
        });
    });
});
</script>

<style>
/* تنسيقات إضافية للنسخ الاحتياطية */
.backup-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border: 1px solid #e2e4e7;
    border-radius: 6px;
    margin-bottom: 8px;
    background: #f9f9f9;
}

.backup-info {
    flex: 1;
}

.backup-date {
    font-weight: 500;
    color: #1d2327;
    margin-bottom: 4px;
}

.backup-type {
    font-size: 13px;
    color: #646970;
}

.backup-form-title {
    font-size: 12px;
    color: #2271b1;
    font-style: italic;
    margin-top: 2px;
}

.backup-actions {
    display: flex;
    gap: 8px;
}

.restore-backup-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    background-color: #2271b1;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.restore-backup-btn:hover {
    background-color: #135e96;
}

.restore-backup-btn:disabled {
    background-color: #c3c4c7;
    cursor: not-allowed;
}

.delete-backup-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    background-color: #d63638;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-left: 8px;
}

.delete-backup-btn:hover {
    background-color: #b32d2e;
}

.delete-backup-btn:disabled {
    background-color: #c3c4c7;
    cursor: not-allowed;
}
</style>
