<?php
/**
 * قسم إعدادات اللغة في صفحة الإعدادات
 *
 * @package Pexlat_Form
 */

// منع الوصول المباشر
if (!defined('WPINC')) {
    die;
}

// استيراد فئة الترجمة
require_once plugin_dir_path(dirname(dirname(dirname(__FILE__)))) . 'includes/class-pexlat-form-i18n.php';

// الحصول على اللغة الحالية
$current_language = get_option('pexlat_form_language', 'auto');

// الحصول على قائمة اللغات المتاحة
$available_languages = Pexlat_Form_i18n::get_available_languages();
?>

<div class="settings-field">
    <label for="pexlat_form_language">لغة الإضافة</label>
    <select name="pexlat_form_language" id="pexlat_form_language" class="regular-text">
        <?php foreach ($available_languages as $code => $name) : ?>
            <option value="<?php echo esc_attr($code); ?>" <?php selected($current_language, $code); ?>>
                <?php echo esc_html($name); ?>
            </option>
        <?php endforeach; ?>
    </select>
    <p class="description">اختر اللغة التي تريد عرض واجهة الإضافة بها. اختيار "تلقائي" سيعتمد على لغة الموقع.</p>
</div>

<div class="settings-field">
    <h4>اللغات المتاحة</h4>
    <ul class="language-list">
        <li>
            <span class="language-icon">🇸🇦</span>
            <span class="language-name">العربية</span>
            <span class="language-status">متاح</span>
        </li>
        <li>
            <span class="language-icon">🇫🇷</span>
            <span class="language-name">الفرنسية</span>
            <span class="language-status">متاح</span>
        </li>
        <li>
            <span class="language-icon">🇺🇸</span>
            <span class="language-name">الإنجليزية</span>
            <span class="language-status">متاح</span>
        </li>
    </ul>
    <p class="description">تنبيه: الترجمة الفرنسية والإنجليزية ليست كاملة بل في بدايتها فقط ولاحقاً سيتم إكمالها.</p>
</div>

<div class="settings-field">
    <h4>معلومات حول الترجمة</h4>
    <p>
        تستخدم الإضافة نظام ترجمة مخصص لواجهة النموذج. يتم تحديد اللغة تلقائياً بناءً على لغة الموقع أو يمكن تحديدها يدوياً من الإعدادات أعلاه.
    </p>
    <p>
        سيتم إضافة المزيد من اللغات في التحديثات القادمة للإضافة.
    </p>
</div>

<style>
.language-list {
    list-style: none;
    padding: 0;
    margin: 0 0 15px 0;
}

.language-list li {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.language-icon {
    font-size: 24px;
    margin-right: 10px;
}

.language-name {
    flex: 1;
    font-weight: 500;
}

.language-status {
    background-color: #4CAF50;
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
}
</style>

<script>
jQuery(document).ready(function($) {
    // عند تغيير اللغة
    $('#pexlat_form_language').on('change', function() {
        // يمكن إضافة تأثيرات إضافية هنا إذا لزم الأمر
    });
});
</script>
