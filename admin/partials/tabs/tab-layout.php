<?php
/**
 * تبويب إعدادات تخطيط النموذج.
 */

// منع الوصول المباشر للملف
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="layout-settings-container">
    <!-- تم إزالة قسم تخطيط الحقول المكرر -->

    <!-- قسم مساحات النموذج -->
    <div class="form-padding-section">
        <h3>مساحات النموذج</h3>

        <div class="settings-field-row">
            <div class="settings-field">
                <label for="form-padding">المسافة الداخلية</label>
                <div class="settings-control">
                    <div class="radio-button-group">
                        <label>
                            <input type="radio" name="settings[form_padding]" value="small" <?php checked($form_settings['form_padding'] ?? 'medium', 'small'); ?>>
                            <span>ضيقة</span>
                        </label>
                        <label>
                            <input type="radio" name="settings[form_padding]" value="medium" <?php checked($form_settings['form_padding'] ?? 'medium', 'medium'); ?>>
                            <span>متوسطة</span>
                        </label>
                        <label>
                            <input type="radio" name="settings[form_padding]" value="large" <?php checked($form_settings['form_padding'] ?? 'medium', 'large'); ?>>
                            <span>واسعة</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="settings-field">
                <label for="form-margin">المسافة الخارجية</label>
                <div class="settings-control">
                    <div class="radio-button-group">
                        <label>
                            <input type="radio" name="settings[form_margin]" value="none" <?php checked($form_settings['form_margin'] ?? 'medium', 'none'); ?>>
                            <span>بدون</span>
                        </label>
                        <label>
                            <input type="radio" name="settings[form_margin]" value="small" <?php checked($form_settings['form_margin'] ?? 'medium', 'small'); ?>>
                            <span>ضيقة</span>
                        </label>
                        <label>
                            <input type="radio" name="settings[form_margin]" value="medium" <?php checked($form_settings['form_margin'] ?? 'medium', 'medium'); ?>>
                            <span>متوسطة</span>
                        </label>
                        <label>
                            <input type="radio" name="settings[form_margin]" value="large" <?php checked($form_settings['form_margin'] ?? 'medium', 'large'); ?>>
                            <span>واسعة</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- إعدادات العناصر الإضافية -->
    <div class="form-elements-section">
        <h3>إعدادات العناصر</h3>

        <div class="settings-field-row">


                    <div class="settings-field">
                        <label for="show_quantity_controls">عنصر اختيار الكمية</label>
                        <div class="settings-control">
                            <div class="toggle-switch-container">
                                <label class="switch-toggle">
                                    <input type="checkbox" id="quantity-controls-toggle"
                                        <?php checked(($form_settings['show_quantity_controls'] ?? 'show'), 'show'); ?>
                                        onchange="updateQuantityControlsVisibility(this)">
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label quantity-visibility-label">
                                    <?php echo ($form_settings['show_quantity_controls'] ?? 'show') === 'show' ? 'ظاهر' : 'مخفي'; ?>
                                </span>
                                <input type="hidden" name="settings[show_quantity_controls]"
                                       id="show_quantity_controls"
                                       value="<?php echo esc_attr($form_settings['show_quantity_controls'] ?? 'show'); ?>">
                            </div>
                        </div>
                        <p class="description">إظهار أو إخفاء عنصر اختيار الكمية في النموذج</p>
                    </div>

                    <div class="settings-field quantity-position-field" <?php echo ($form_settings['show_quantity_controls'] ?? 'show') === 'hide' ? 'style="display: none;"' : ''; ?>>
                        <label for="quantity_position">موضع عنصر الكمية</label>
                        <div class="settings-control">
                            <select id="quantity_position" name="settings[quantity_position]">
                                <option value="center" <?php selected(($form_settings['quantity_position'] ?? 'center'), 'center'); ?>>في الوسط (افتراضي)</option>
                                <option value="inline" <?php selected(($form_settings['quantity_position'] ?? 'center'), 'inline'); ?>>بجانب زر الطلب</option>
                            </select>
                        </div>
                        <p class="description">اختر موضع عنصر الكمية في النموذج</p>
                    </div>

        <script>
        function updateQuantityControlsVisibility(checkbox) {
            var isChecked = checkbox.checked;
            var value = isChecked ? 'show' : 'hide';
            document.getElementById('show_quantity_controls').value = value;
            document.querySelector('.quantity-visibility-label').textContent = isChecked ? 'ظاهر' : 'مخفي';

            // إظهار أو إخفاء حقل موضع الكمية
            var quantityPositionField = document.querySelector('.quantity-position-field');
            if (quantityPositionField) {
                quantityPositionField.style.display = isChecked ? 'block' : 'none';
            }

            console.log('تم تحديث حالة عنصر الكمية:', value);
        }


        </script>

            <!-- تم إزالة إعداد موضع ملخص الطلب بناءً على طلب المستخدم -->
            <!-- ملخص الطلب يظهر دائمًا أسفل النموذج بشكل افتراضي -->
        </div>
    </div>





    <!-- قسم الشريط المثبت -->
    <div class="form-sticky-bar-section">
        <h3>الشريط المثبت</h3>

        <div class="settings-field-row">
            <div class="settings-field">
                <div class="toggle-switch-container">
                    <label class="switch-toggle">
                        <input type="checkbox" id="show-sticky-bar-toggle" <?php checked(($form_settings['show_sticky_bar'] ?? 'yes'), 'yes'); ?>>
                        <span class="toggle-slider"></span>
                    </label>
                    <span class="toggle-label toggle-large">عرض شريط الطلب في أسفل الصفحة</span>
                    <input type="hidden" name="settings[show_sticky_bar]" id="show-sticky-bar" value="<?php echo esc_attr($form_settings['show_sticky_bar'] ?? 'yes'); ?>">
                </div>
                <p class="description">شريط يظهر في أسفل الصفحة عند التمرير، يحتوي على بيانات المنتج وزر للانتقال إلى النموذج</p>
            </div>
        </div>

        <div class="sticky-bar-settings" <?php echo ($form_settings['show_sticky_bar'] ?? 'yes') === 'no' ? 'style="display: none;"' : ''; ?>>
            <div class="sticky-bar-preview-container">
                <h4>معاينة الشريط المثبت</h4>
                <div class="sticky-bar-preview <?php echo ($form_settings['sticky_bar_show_product'] ?? 'yes') === 'no' ? 'center-button' : ''; ?>">
                    <div class="preview-product-info" id="sticky-preview-product" <?php echo ($form_settings['sticky_bar_show_product'] ?? 'yes') === 'no' ? 'style="display: none;"' : ''; ?>>
                        <div class="preview-product-image"><div class="img-placeholder"></div></div>
                        <div class="preview-product-details">
                            <div class="preview-product-title">عنوان المنتج</div>
                            <div class="preview-product-price">99.99 دج</div>
                        </div>
                    </div>
                    <button id="sticky-button-preview" class="sticky-button-preview <?php echo ($form_settings['sticky_bar_button_animation'] ?? 'none') !== 'none' ? 'animation-' . esc_attr($form_settings['sticky_bar_button_animation']) : ''; ?>">
                        <span class="sticky-icon-preview" <?php echo ($form_settings['sticky_bar_button_icon'] ?? 'cart') === 'none' ? 'style="display: none;"' : ''; ?>>
                            <i class="fas <?php
                                $icon = $form_settings['sticky_bar_button_icon'] ?? 'cart';
                                echo $icon === 'check' ? 'fa-check' :
                                    ($icon === 'cart' ? 'fa-shopping-cart' :
                                    ($icon === 'arrow' ? 'fa-arrow-right' :
                                    ($icon === 'box' ? 'fa-box' : 'fa-shopping-cart')));
                            ?>"></i>
                        </span>
                        <span class="sticky-text-preview"><?php echo esc_html($form_settings['sticky_bar_button_text'] ?? $form_settings['button_text'] ?? 'اطلب الآن'); ?></span>
                    </button>
                </div>
            </div>

            <div class="sticky-bar-options-grid">
                <div class="settings-field">
                    <div class="toggle-switch-container">
                        <label class="switch-toggle">
                            <input type="checkbox" id="sticky-bar-show-product-toggle" <?php checked(($form_settings['sticky_bar_show_product'] ?? 'yes'), 'yes'); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">عرض معلومات المنتج</span>
                        <input type="hidden" name="settings[sticky_bar_show_product]" id="sticky-bar-show-product" value="<?php echo esc_attr($form_settings['sticky_bar_show_product'] ?? 'yes'); ?>">
                    </div>
                </div>

                <div class="settings-field">
                    <div class="toggle-switch-container">
                        <label class="switch-toggle">
                            <input type="checkbox" id="sticky-bar-always-visible-toggle" <?php checked(($form_settings['sticky_bar_always_visible'] ?? 'no'), 'yes'); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">عرض الشريط السفلي دائمًا</span>
                        <input type="hidden" name="settings[sticky_bar_always_visible]" id="sticky-bar-always-visible" value="<?php echo esc_attr($form_settings['sticky_bar_always_visible'] ?? 'no'); ?>">
                    </div>
                    <p class="description">عند تفعيل هذا الخيار، سيبقى الشريط السفلي ظاهرًا دائمًا حتى عند التمرير إلى منطقة النموذج</p>
                </div>

                <div class="settings-field">
                    <div class="toggle-switch-container">
                        <label class="switch-toggle">
                            <input type="checkbox" id="sticky-bar-button-submit-toggle" <?php checked(($form_settings['sticky_bar_button_submit'] ?? 'no'), 'yes'); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">جعل زر الشريط يقوم بالطلب</span>
                        <input type="hidden" name="settings[sticky_bar_button_submit]" id="sticky-bar-button-submit" value="<?php echo esc_attr($form_settings['sticky_bar_button_submit'] ?? 'no'); ?>">
                    </div>
                    <p class="description">عند تفعيل هذا الخيار، سيقوم زر الشريط المثبت بنفس وظيفة زر الطلب الرئيسي بدلاً من التمرير إلى النموذج</p>
                </div>

                <div class="settings-field">
                    <label for="sticky-bar-button-text">نص زر الشريط</label>
                    <div class="input-with-button">
                        <input type="text" id="sticky-bar-button-text-input" class="regular-text"
                               value="<?php echo esc_attr($form_settings['sticky_bar_button_text'] ?? $form_settings['button_text'] ?? 'اطلب الآن'); ?>" placeholder="اطلب الآن">
                        <button type="button" id="copy-main-button-text" class="button">نسخ من الزر الرئيسي</button>
                        <input type="hidden" name="settings[sticky_bar_button_text]" id="sticky-bar-button-text"
                               value="<?php echo esc_attr($form_settings['sticky_bar_button_text'] ?? $form_settings['button_text'] ?? 'اطلب الآن'); ?>">
                    </div>
                </div>

                <div class="settings-field">
                    <label for="sticky-bar-button-color">لون زر الشريط</label>
                    <input type="text" id="sticky-bar-button-color-input" class="color-picker" name="settings[sticky_bar_button_color]" value="<?php echo esc_attr($form_settings['sticky_bar_button_color'] ?? '#2271b1'); ?>">
                </div>

                <div class="settings-field">
                    <label for="sticky-bar-button-text-color">لون نص زر الشريط</label>
                    <input type="text" id="sticky-bar-button-text-color-input" class="color-picker" name="settings[sticky_bar_button_text_color]" value="<?php echo esc_attr($form_settings['sticky_bar_button_text_color'] ?? '#ffffff'); ?>">
                </div>

                <div class="settings-field">
                    <label for="sticky-bar-button-border-radius">تدوير زوايا زر الشريط</label>
                    <div class="range-slider-container">
                        <input type="range" id="sticky-bar-button-border-radius" name="settings[sticky_bar_button_border_radius]" min="0" max="50" step="1" value="<?php echo esc_attr($form_settings['sticky_bar_button_border_radius'] ?? '5'); ?>">
                        <span class="range-value sticky-bar-button-border-radius-value"><?php echo esc_html($form_settings['sticky_bar_button_border_radius'] ?? '5'); ?>px</span>
                    </div>
                </div>

                <div class="settings-field">
                    <label for="sticky-bar-button-icon">أيقونة زر الشريط</label>
                    <select id="sticky-bar-button-icon" name="settings[sticky_bar_button_icon]">
                        <option value="none" <?php selected(($form_settings['sticky_bar_button_icon'] ?? 'cart'), 'none'); ?>>بدون أيقونة</option>
                        <option value="cart" <?php selected(($form_settings['sticky_bar_button_icon'] ?? 'cart'), 'cart'); ?>>سلة تسوق</option>
                        <option value="check" <?php selected(($form_settings['sticky_bar_button_icon'] ?? 'cart'), 'check'); ?>>علامة صح</option>
                        <option value="arrow" <?php selected(($form_settings['sticky_bar_button_icon'] ?? 'cart'), 'arrow'); ?>>سهم</option>
                        <option value="box" <?php selected(($form_settings['sticky_bar_button_icon'] ?? 'cart'), 'box'); ?>>صندوق</option>
                    </select>
                </div>

                <div class="settings-field">
                    <label for="sticky-bar-button-animation">تأثير حركي لزر الشريط</label>
                    <select id="sticky-bar-button-animation" name="settings[sticky_bar_button_animation]">
                        <option value="none" <?php selected(($form_settings['sticky_bar_button_animation'] ?? 'none'), 'none'); ?>>بدون تأثير</option>
                        <option value="pulse" <?php selected(($form_settings['sticky_bar_button_animation'] ?? 'none'), 'pulse'); ?>>نبض</option>
                        <option value="bounce" <?php selected(($form_settings['sticky_bar_button_animation'] ?? 'none'), 'bounce'); ?>>قفز</option>
                        <option value="tada" <?php selected(($form_settings['sticky_bar_button_animation'] ?? 'none'), 'tada'); ?>>تادا</option>
                        <option value="swing" <?php selected(($form_settings['sticky_bar_button_animation'] ?? 'none'), 'swing'); ?>>تأرجح</option>
                    </select>
                </div>

                <div class="settings-field">
                    <div class="toggle-switch-container">
                        <label class="switch-toggle">
                            <input type="checkbox" id="sticky-bar-button-gradient-toggle" <?php checked(($form_settings['sticky_bar_button_gradient'] ?? 'yes'), 'yes'); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">تدرج لوني للزر</span>
                        <input type="hidden" name="settings[sticky_bar_button_gradient]" id="sticky-bar-button-gradient"
                               value="<?php echo esc_attr($form_settings['sticky_bar_button_gradient'] ?? 'yes'); ?>">
                    </div>
                </div>

                <div class="settings-field sticky-gradient-options" <?php echo ($form_settings['sticky_bar_button_gradient'] ?? 'yes') === 'no' ? 'style="display: none;"' : ''; ?>>
                    <label for="sticky-bar-button-gradient-color">لون التدرج الثانوي</label>
                    <input type="text" id="sticky-bar-button-gradient-color-input" class="color-picker"
                           name="settings[sticky_bar_button_gradient_color]"
                           value="<?php echo esc_attr($form_settings['sticky_bar_button_gradient_color'] ?? '#312e81'); ?>">
                </div>

                <div class="settings-field sticky-gradient-options" <?php echo ($form_settings['sticky_bar_button_gradient'] ?? 'yes') === 'no' ? 'style="display: none;"' : ''; ?>>
                    <label for="sticky-bar-button-gradient-direction">اتجاه التدرج</label>
                    <select id="sticky-bar-button-gradient-direction" name="settings[sticky_bar_button_gradient_direction]">
                        <option value="to bottom" <?php selected(($form_settings['sticky_bar_button_gradient_direction'] ?? 'to bottom'), 'to bottom'); ?>>من أعلى إلى أسفل</option>
                        <option value="to right" <?php selected(($form_settings['sticky_bar_button_gradient_direction'] ?? 'to bottom'), 'to right'); ?>>من اليمين إلى اليسار</option>
                        <option value="to top" <?php selected(($form_settings['sticky_bar_button_gradient_direction'] ?? 'to bottom'), 'to top'); ?>>من أسفل إلى أعلى</option>
                        <option value="to left" <?php selected(($form_settings['sticky_bar_button_gradient_direction'] ?? 'to bottom'), 'to left'); ?>>من اليسار إلى اليمين</option>
                        <option value="to bottom right" <?php selected(($form_settings['sticky_bar_button_gradient_direction'] ?? 'to bottom'), 'to bottom right'); ?>>قطري إلى الأسفل اليمين</option>
                        <option value="to bottom left" <?php selected(($form_settings['sticky_bar_button_gradient_direction'] ?? 'to bottom'), 'to bottom left'); ?>>قطري إلى الأسفل اليسار</option>
                        <option value="to top right" <?php selected(($form_settings['sticky_bar_button_gradient_direction'] ?? 'to bottom'), 'to top right'); ?>>قطري إلى الأعلى اليمين</option>
                        <option value="to top left" <?php selected(($form_settings['sticky_bar_button_gradient_direction'] ?? 'to bottom'), 'to top left'); ?>>قطري إلى الأعلى اليسار</option>
                    </select>
                </div>

                <div class="settings-field">
                    <div class="toggle-switch-container">
                        <label class="switch-toggle">
                            <input type="checkbox" id="sticky-bar-shortcode-always-visible-toggle" <?php checked(($form_settings['sticky_bar_shortcode_always_visible'] ?? 'yes'), 'yes'); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">إظهار الشريط دائماً في الشورت كود</span>
                        <input type="hidden" name="settings[sticky_bar_shortcode_always_visible]" id="sticky-bar-shortcode-always-visible" value="<?php echo esc_attr($form_settings['sticky_bar_shortcode_always_visible'] ?? 'yes'); ?>">
                    </div>
                    <p class="description">عند التفعيل، سيظهر الشريط السفلي دائماً عند استخدام النموذج في الشورت كود</p>
                </div>
            </div>
        </div>
    </div>



</div>

<style>
/* تصميم محسن لتبويب التخطيط */
.layout-settings-container {
    margin-top: 20px;
}

.layout-settings-container h3 {
    margin-top: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
}

.form-layout-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 15px;
}

.layout-option {
    flex: 0 0 180px;
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.layout-option.active {
    border-color: #2271b1;
    background-color: #f0f6fc;
}

.layout-preview {
    height: 100px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
    justify-content: center;
}

.layout-icon {
    font-size: 24px;
    margin-bottom: 10px;
    color: #555;
}

.layout-preview-items {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.layout-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.preview-field {
    height: 15px;
    background-color: #e2e2e2;
    border-radius: 4px;
}

.layout-responsive {
    width: 100%;
}

.desktop-view {
    margin-bottom: 15px;
    position: relative;
}

.desktop-view::after {
    content: "🖥️";
    position: absolute;
    top: -12px;
    right: -5px;
    font-size: 10px;
}

.mobile-view {
    display: flex;
    flex-direction: column;
    gap: 5px;
    position: relative;
}

.mobile-view::after {
    content: "📱";
    position: absolute;
    top: -10px;
    right: -5px;
    font-size: 10px;
}

.layout-label {
    display: block;
    margin-top: 10px;
    font-weight: 500;
}

.layout-description {
    margin-top: 15px;
    color: #666;
}

.layout-radio {
    position: absolute;
    opacity: 0;
}

/* مساحات الأعمدة */
.spacing-options {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.spacing-option {
    flex: 0 0 100px;
    text-align: center;
    cursor: pointer;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 6px;
    transition: all 0.3s;
}

.spacing-option.active {
    border-color: #2271b1;
    background-color: #f0f6fc;
}

.spacing-preview {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
    height: 40px;
    align-items: center;
}

.spacing-preview span {
    width: 30px;
    height: 20px;
    background: #e2e2e2;
    border-radius: 4px;
}

.small-gap span {
    margin: 0 5px;
}

.medium-gap span {
    margin: 0 10px;
}

.large-gap span {
    margin: 0 15px;
}

.spacing-radio {
    position: absolute;
    opacity: 0;
}

/* إعدادات المساحات */
.settings-field-row {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 15px;
}

.settings-field {
    flex: 1 1 300px;
    margin-bottom: 15px;
}

.settings-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.radio-button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.radio-button-group label {
    display: inline-flex;
    align-items: center;
    border: 1px solid #ddd;
    padding: 5px 12px;
    border-radius: 4px;
    transition: all 0.3s;
    cursor: pointer;
    margin: 0;
}

.radio-button-group input[type="radio"] {
    margin-right: 5px;
}

.radio-button-group label:has(input:checked) {
    background-color: #f0f6fc;
    border-color: #2271b1;
}

/* مفاتيح التبديل */
.toggle-switch-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.switch-toggle {
    position: relative;
    display: inline-block;
    width: 46px;
    height: 24px;
}

.switch-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #2271b1;
}

input:checked + .toggle-slider:before {
    transform: translateX(22px);
}

.toggle-label {
    font-weight: normal;
}

.toggle-large {
    font-size: 14px;
    font-weight: 500;
}

/* معاينة الشريط المثبت */
.sticky-bar-preview-container {
    margin: 20px 0;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background-color: #f8f8f8;
}

.sticky-bar-preview {
    margin-top: 10px;
    background-color: white;
    padding: 10px 15px;
    border-radius: 6px;
    border: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* توسيط الزر عندما تكون معلومات المنتج مخفية في المعاينة */
.sticky-bar-preview.center-button {
    justify-content: center;
}

.preview-product-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.preview-product-image {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    overflow: hidden;
    background-color: #f1f1f1;
}

.img-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #e6e6e6, #f5f5f5);
}

.preview-product-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.preview-product-title {
    font-weight: 600;
    font-size: 14px;
}

.preview-product-price {
    color: #2271b1;
    font-weight: 600;
}

.sticky-button-preview {
    background-color: #2271b1;
    border: none;
    color: white;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
}

.sticky-icon-preview {
    display: inline-flex;
}

/* خيارات الشريط المثبت */
.sticky-bar-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.input-with-button {
    display: flex;
    gap: 10px;
    align-items: flex-start;
}

.input-with-button input {
    flex: 1;
}

.column-settings-container {
    background-color: #f8f8f8;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    border: 1px solid #eee;
}

.column-settings-container h4 {
    margin-top: 0;
    margin-bottom: 15px;
}

/* قسم معاينة النموذج المتجاوبة */
.form-preview-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.form-preview-section h3 {
    margin-top: 0;
    padding-bottom: 10px;
    font-size: 16px;
}

/* تنسيقات قسم ترتيب العناصر */
.form-elements-order-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.form-elements-order-container {
    margin-top: 20px;
    background-color: #f8f8f8;
    border: 1px solid #e2e2e2;
    border-radius: 8px;
    padding: 15px;
}

.form-elements-sortable {
    list-style: none;
    padding: 0;
    margin: 0;
}

.form-element-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px 15px;
    margin-bottom: 10px;
    cursor: move;
    transition: all 0.2s ease;
}

.form-element-item:hover {
    border-color: #2271b1;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.form-element-item.ui-sortable-helper {
    box-shadow: 0 5px 10px rgba(0,0,0,0.15);
    border-color: #2271b1;
    background-color: #f0f6fc;
}

.element-handle {
    margin-right: 10px;
    color: #999;
    cursor: move;
}

.element-icon {
    margin-right: 10px;
    color: #2271b1;
}

.element-label {
    flex: 1;
    font-weight: 500;
}
</style>

<script>
// هذه التعليمات البرمجية سيتم تنفيذها عند تحميل التبويب
jQuery(document).ready(function($) {
    // إعداد حالة توسيط الزر عند تحميل الصفحة
    var showProduct = $('#sticky-bar-show-product').val();
    if (showProduct === 'no') {
        $('.sticky-bar-preview').addClass('center-button');
    }

    // تفعيل خيارات تخطيط الحقول
    $('.layout-option').on('click', function() {
        // إزالة الفئة النشطة من جميع الخيارات
        $('.layout-option').removeClass('active');
        // إضافة الفئة النشطة للخيار المختار
        $(this).addClass('active');
        // تحديث قيمة الزر الراديو المخفي
        $(this).find('input[type="radio"]').prop('checked', true);

        // عرض أو إخفاء إعدادات الأعمدة
        var layout = $(this).data('layout');
        if (layout === 'vertical') {
            $('.column-settings-container').slideUp(300);
        } else {
            $('.column-settings-container').slideDown(300);
        }
    });

    // تفعيل خيارات المسافات
    $('.spacing-option').on('click', function() {
        // إزالة الفئة النشطة من جميع الخيارات
        $('.spacing-option').removeClass('active');
        // إضافة الفئة النشطة للخيار المختار
        $(this).addClass('active');
        // تحديث قيمة الزر الراديو المخفي
        $(this).find('input[type="radio"]').prop('checked', true);
    });



    // مفتاح تبديل الشريط المثبت
    $('#show-sticky-bar-toggle').on('change', function() {
        var isChecked = $(this).is(':checked');
        $('#show-sticky-bar').val(isChecked ? 'yes' : 'no');
        $('.sticky-bar-settings').toggle(isChecked);
    });

    // مفتاح تبديل عرض المنتج في الشريط المثبت
    $('#sticky-bar-show-product-toggle').on('change', function() {
        var isChecked = $(this).is(':checked');
        $('#sticky-bar-show-product').val(isChecked ? 'yes' : 'no');
        $('#sticky-preview-product').toggle(isChecked);

        // تبديل فئة توسيط الزر حسب حالة العرض
        $('.sticky-bar-preview').toggleClass('center-button', !isChecked);
    });

    // مفتاح تبديل عرض الشريط السفلي دائمًا
    $('#sticky-bar-always-visible-toggle').on('change', function() {
        var isChecked = $(this).is(':checked');
        $('#sticky-bar-always-visible').val(isChecked ? 'yes' : 'no');
        $(this).closest('.toggle-switch-container').find('.toggle-label').text(isChecked ? 'مفعل' : 'غير مفعل');
        console.log('تم تغيير حالة عرض الشريط السفلي دائمًا إلى: ' + (isChecked ? 'مفعل' : 'معطل'));
    });

    // مفتاح تبديل جعل زر الشريط يقوم بالطلب
    $('#sticky-bar-button-submit-toggle').on('change', function() {
        var isChecked = $(this).is(':checked');
        $('#sticky-bar-button-submit').val(isChecked ? 'yes' : 'no');
        $(this).closest('.toggle-switch-container').find('.toggle-label').text(isChecked ? 'مفعل' : 'غير مفعل');
        console.log('تم تغيير حالة جعل زر الشريط يقوم بالطلب إلى: ' + (isChecked ? 'مفعل' : 'معطل'));
    });

    // تحديث لون زر الشريط المثبت
    $('#sticky-bar-button-color-input').wpColorPicker({
        change: function(event, ui) {
            var color = ui.color.toString();
            $('.sticky-button-preview').css('background-color', color);
        }
    });

    // تحديث لون نص زر الشريط المثبت
    $('#sticky-bar-button-text-color-input').wpColorPicker({
        change: function(event, ui) {
            var color = ui.color.toString();
            $('.sticky-button-preview').css('color', color);
        }
    });

    // تحديث تدوير زوايا زر الشريط المثبت
    $('#sticky-bar-button-border-radius').on('input', function() {
        var radius = $(this).val();
        $('.sticky-bar-button-border-radius-value').text(radius + 'px');
        $('.sticky-button-preview').css('border-radius', radius + 'px');
    });

    // تحديث نص زر الشريط المثبت
    $('#sticky-bar-button-text-input').on('input', function() {
        var buttonText = $(this).val();
        $('#sticky-bar-button-text').val(buttonText);
        $('.sticky-text-preview').text(buttonText);
    });

    // تحديث أيقونة زر الشريط المثبت
    $('#sticky-bar-button-icon').on('change', function() {
        var selectedIcon = $(this).val();
        var $iconPreview = $('.sticky-icon-preview');

        if (selectedIcon === 'none') {
            $iconPreview.hide();
        } else {
            var iconClass = selectedIcon === 'cart' ? 'fa-shopping-cart' :
                           (selectedIcon === 'check' ? 'fa-check' :
                           (selectedIcon === 'arrow' ? 'fa-arrow-right' :
                           (selectedIcon === 'box' ? 'fa-box' : 'fa-shopping-cart')));

            $iconPreview.show().find('i').attr('class', 'fas ' + iconClass);
        }
    });

    // تحديث التأثير الحركي لزر الشريط المثبت
    $('#sticky-bar-button-animation').on('change', function() {
        var selectedAnimation = $(this).val();
        var $buttonPreview = $('.sticky-button-preview');

        // إزالة جميع فئات الحركة
        $buttonPreview.removeClass('animation-pulse animation-bounce animation-tada animation-swing');

        // إضافة الفئة الجديدة إذا كانت ليست "none"
        if (selectedAnimation !== 'none') {
            $buttonPreview.addClass('animation-' + selectedAnimation);
        }
    });

    // مفتاح تبديل التدرج اللوني
    $('#sticky-bar-button-gradient-toggle').on('change', function() {
        var isChecked = $(this).is(':checked');
        $('#sticky-bar-button-gradient').val(isChecked ? 'yes' : 'no');
        $(this).closest('.toggle-switch-container').find('.toggle-label').text(isChecked ? 'مفعل' : 'غير مفعل');
        $('.sticky-gradient-options').toggle(isChecked);

        // تحديث مظهر زر المعاينة
        var $buttonPreview = $('.sticky-button-preview');
        if (isChecked) {
            $buttonPreview.addClass('gradient');
            updateGradient();
        } else {
            $buttonPreview.removeClass('gradient');
        }
    });

    // تحديث لون التدرج الثانوي
    $('#sticky-bar-button-gradient-color-input').wpColorPicker({
        change: function(event, ui) {
            updateGradient();
        }
    });

    // تحديث اتجاه التدرج
    $('#sticky-bar-button-gradient-direction').on('change', function() {
        updateGradient();
    });

    // وظيفة لتحديث خصائص التدرج
    function updateGradient() {
        var primaryColor = $('#sticky-bar-button-color-input').val();
        var secondaryColor = $('#sticky-bar-button-gradient-color-input').val();
        var direction = $('#sticky-bar-button-gradient-direction').val();

        // تطبيق التدرج على معاينة الزر
        $('.sticky-button-preview').css('background-image', 'linear-gradient(' + direction + ', ' + primaryColor + ', ' + secondaryColor + ')');
    }

    // نسخ نص الزر الرئيسي
    $('#copy-main-button-text').on('click', function() {
        var mainButtonText = $('input[name="settings[button_text]"]').val();
        $('#sticky-bar-button-text-input').val(mainButtonText);
        $('#sticky-bar-button-text').val(mainButtonText);
        $('.sticky-text-preview').text(mainButtonText);
    });

    // ملاحظة: تم نقل وظيفة السحب والإفلات لقائمة ترتيب العناصر إلى form-settings-sidebar.php
});
</script>