<?php
/**
 * تبويب طرق الدفع
 *
 * @var array $form_settings إعدادات النموذج
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// التحقق من وجود WooCommerce
if (!class_exists('WooCommerce')) {
    ?>
    <div class="notice notice-warning">
        <p><strong>تنبيه:</strong> يتطلب نظام طرق الدفع تفعيل إضافة WooCommerce أولاً.</p>
    </div>
    <?php
    return;
}

// الحصول على طرق الدفع المتاحة في WooCommerce
$available_gateways = WC()->payment_gateways->get_available_payment_gateways();
$all_gateways = WC()->payment_gateways->payment_gateways();
?>

<div class="collapsible-section open" id="payment-methods-section">
    <div class="collapsible-header">
        <h3><span class="subsection-icon dashicons dashicons-money-alt"></span>طرق الدفع</h3>
        <span class="collapsible-toggle dashicons dashicons-arrow-up-alt2"></span>
    </div>
    <div class="collapsible-content">
        <!-- إعداد تفعيل/تعطيل نظام طرق الدفع -->
        <div class="settings-group">
            <div class="settings-field">
                <label class="main-toggle-label">
                    <input type="checkbox" id="payment_methods_enabled" name="settings[payment_methods_enabled]" value="1"
                        <?php checked(isset($form_settings['payment_methods_enabled']) ? intval($form_settings['payment_methods_enabled']) : 0, 1); ?>>
                    <span class="toggle-text">تفعيل نظام طرق الدفع</span>
                </label>
                <p class="description">عند التفعيل، ستظهر طرق الدفع المتاحة في WooCommerce في النموذج ويمكن للعملاء اختيار طريقة الدفع المناسبة</p>
            </div>
        </div>



        <!-- إعدادات طرق الدفع من WooCommerce -->
        <div id="payment-methods-settings" style="<?php echo (isset($form_settings['payment_methods_enabled']) && $form_settings['payment_methods_enabled']) ? '' : 'display: none;'; ?>">

            <?php if (empty($all_gateways)) : ?>
                <div class="notice notice-info">
                    <p>لا توجد طرق دفع متاحة في WooCommerce. يرجى تفعيل طرق الدفع من إعدادات WooCommerce أولاً.</p>
                    <p><a href="<?php echo admin_url('admin.php?page=wc-settings&tab=checkout'); ?>" class="button">إعدادات طرق الدفع في WooCommerce</a></p>
                </div>
            <?php else : ?>
                <p class="description" style="margin-bottom: 20px;">
                    <strong>ملاحظة:</strong> يتم عرض طرق الدفع المتاحة في WooCommerce. يمكنك تفعيل/تعطيل الطرق التي تريد إظهارها في النموذج.
                    <br>لإضافة أو تعديل طرق الدفع، يرجى الذهاب إلى <a href="<?php echo admin_url('admin.php?page=wc-settings&tab=checkout'); ?>" target="_blank">إعدادات WooCommerce</a>.
                </p>

                <?php foreach ($all_gateways as $gateway_id => $gateway) :
                    $is_enabled = $gateway->enabled === 'yes';
                    $form_enabled = isset($form_settings['payment_gateway_' . $gateway_id]) ? intval($form_settings['payment_gateway_' . $gateway_id]) : ($is_enabled ? 1 : 0);
                ?>
                <div class="payment-method-card">
                    <div class="payment-method-header">
                        <div class="payment-icon <?php echo $is_enabled ? 'payment-icon-enabled' : 'payment-icon-disabled'; ?>">
                            <?php if ($gateway_id === 'cod') : ?>
                                <span class="dashicons dashicons-money"></span>
                            <?php elseif ($gateway_id === 'bacs') : ?>
                                <span class="dashicons dashicons-bank"></span>
                            <?php elseif (strpos($gateway_id, 'paypal') !== false) : ?>
                                <span class="dashicons dashicons-admin-users"></span>
                            <?php elseif (strpos($gateway_id, 'stripe') !== false || strpos($gateway_id, 'card') !== false) : ?>
                                <span class="dashicons dashicons-credit-card"></span>
                            <?php else : ?>
                                <span class="dashicons dashicons-money-alt"></span>
                            <?php endif; ?>
                        </div>
                        <div class="payment-method-info">
                            <h5 class="payment-method-title"><?php echo esc_html($gateway->get_title()); ?></h5>
                            <p class="payment-method-id">معرف الطريقة: <?php echo esc_html($gateway_id); ?></p>
                            <?php if (!$is_enabled) : ?>
                                <p class="payment-method-status disabled">غير مفعل في WooCommerce</p>
                            <?php else : ?>
                                <p class="payment-method-status enabled">مفعل في WooCommerce</p>
                            <?php endif; ?>
                        </div>
                        <div class="payment-method-toggle">
                            <label class="switch-toggle">
                                <input type="checkbox"
                                       id="payment_gateway_<?php echo esc_attr($gateway_id); ?>"
                                       name="settings[payment_gateway_<?php echo esc_attr($gateway_id); ?>]"
                                       value="1"
                                       <?php checked($form_enabled, 1); ?>
                                       <?php echo !$is_enabled ? 'disabled' : ''; ?>>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>

                    <?php if ($is_enabled) : ?>
                    <div class="payment-method-content">
                        <div class="payment-field">
                            <label>وصف طريقة الدفع:</label>
                            <p class="payment-description"><?php echo wp_kses_post($gateway->get_description()); ?></p>
                        </div>

                        <!-- إعداد صورة شعار طريقة الدفع -->
                        <div class="payment-field">
                            <label for="payment_logo_<?php echo esc_attr($gateway_id); ?>">شعار طريقة الدفع:</label>
                            <div class="payment-logo-upload">
                                <?php
                                $logo_url = isset($form_settings['payment_logo_' . $gateway_id]) ? $form_settings['payment_logo_' . $gateway_id] : '';
                                ?>
                                <input type="hidden" name="settings[payment_logo_<?php echo esc_attr($gateway_id); ?>]"
                                       id="payment_logo_<?php echo esc_attr($gateway_id); ?>"
                                       value="<?php echo esc_url($logo_url); ?>">

                                <div class="logo-preview" id="logo_preview_<?php echo esc_attr($gateway_id); ?>">
                                    <?php if (!empty($logo_url)) : ?>
                                        <img src="<?php echo esc_url($logo_url); ?>" alt="شعار <?php echo esc_attr($gateway->get_title()); ?>" style="max-width: 100px; max-height: 50px;">
                                    <?php else : ?>
                                        <span class="no-logo">لا يوجد شعار</span>
                                    <?php endif; ?>
                                </div>

                                <div class="logo-controls">
                                    <button type="button" class="button upload-logo-btn" data-gateway="<?php echo esc_attr($gateway_id); ?>">
                                        <?php echo !empty($logo_url) ? 'تغيير الشعار' : 'رفع شعار'; ?>
                                    </button>
                                    <?php if (!empty($logo_url)) : ?>
                                        <button type="button" class="button remove-logo-btn" data-gateway="<?php echo esc_attr($gateway_id); ?>">حذف الشعار</button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <p class="description">يمكنك رفع صورة شعار لطريقة الدفع لتظهر في النموذج. الحجم المفضل: 100x50 بكسل</p>
                        </div>

                        <?php if ($gateway->has_fields()) : ?>
                        <div class="payment-field">
                            <p class="notice-info"><strong>ملاحظة:</strong> هذه الطريقة تحتوي على حقول إضافية سيتم عرضها في صفحة الدفع.</p>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php else : ?>
                    <div class="payment-method-content">
                        <p class="notice-warning">هذه الطريقة غير مفعلة في WooCommerce. يرجى تفعيلها من <a href="<?php echo admin_url('admin.php?page=wc-settings&tab=checkout&section=' . $gateway_id); ?>" target="_blank">إعدادات WooCommerce</a> أولاً.</p>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>

        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل/تعطيل إعدادات طرق الدفع
    const paymentMethodsEnabled = document.getElementById('payment_methods_enabled');
    const paymentMethodsSettings = document.getElementById('payment-methods-settings');

    if (paymentMethodsEnabled && paymentMethodsSettings) {
        paymentMethodsEnabled.addEventListener('change', function() {
            if (this.checked) {
                paymentMethodsSettings.style.display = 'block';
            } else {
                paymentMethodsSettings.style.display = 'none';
            }
        });
    }

    // معالجة رفع الصور
    jQuery(document).ready(function($) {
        // رفع الصورة
        $('.upload-logo-btn').on('click', function(e) {
            e.preventDefault();

            const gatewayId = $(this).data('gateway');
            const button = $(this);

            // إنشاء media uploader
            const mediaUploader = wp.media({
                title: 'اختر شعار طريقة الدفع',
                button: {
                    text: 'استخدام هذه الصورة'
                },
                multiple: false,
                library: {
                    type: 'image'
                }
            });

            // عند اختيار الصورة
            mediaUploader.on('select', function() {
                const attachment = mediaUploader.state().get('selection').first().toJSON();

                // تحديث الحقل المخفي
                $('#payment_logo_' + gatewayId).val(attachment.url);

                // تحديث المعاينة
                const preview = $('#logo_preview_' + gatewayId);
                preview.html('<img src="' + attachment.url + '" alt="شعار طريقة الدفع" style="max-width: 100px; max-height: 50px;">');

                // تحديث النص والأزرار
                button.text('تغيير الشعار');
                if (!button.siblings('.remove-logo-btn').length) {
                    button.after('<button type="button" class="button remove-logo-btn" data-gateway="' + gatewayId + '">حذف الشعار</button>');
                }
            });

            mediaUploader.open();
        });

        // حذف الصورة
        $(document).on('click', '.remove-logo-btn', function(e) {
            e.preventDefault();

            const gatewayId = $(this).data('gateway');
            const button = $(this);

            // مسح الحقل المخفي
            $('#payment_logo_' + gatewayId).val('');

            // تحديث المعاينة
            const preview = $('#logo_preview_' + gatewayId);
            preview.html('<span class="no-logo">لا يوجد شعار</span>');

            // تحديث النص والأزرار
            button.siblings('.upload-logo-btn').text('رفع شعار');
            button.remove();
        });
    });
});
</script>

<style>
/* تطبيق متغيرات CSS للألوان الموحدة */
:root {
    --pexlat-primary-color: <?php echo isset($form_settings['primary_color']) ? esc_attr($form_settings['primary_color']) : '#2563eb'; ?>;
    --pexlat-primary-hover: <?php echo isset($form_settings['primary_hover']) ? esc_attr($form_settings['primary_hover']) : '#1d4ed8'; ?>;
    --pexlat-secondary-color: <?php echo isset($form_settings['secondary_color']) ? esc_attr($form_settings['secondary_color']) : '#64748b'; ?>;
}

.payment-method-card {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin-bottom: 20px;
    background: #fff;
    overflow: hidden;
}

.payment-method-header {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
}

.payment-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.payment-icon-enabled {
    background: var(--pexlat-primary-color, #2563eb);
    color: white;
}

.payment-icon-disabled {
    background: #6c757d;
    color: white;
}

.payment-method-info {
    flex: 1;
}

.payment-method-title {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: 600;
}

.payment-method-id {
    margin: 0 0 5px 0;
    font-size: 12px;
    color: #666;
    font-family: monospace;
}

.payment-method-status {
    margin: 0;
    font-size: 12px;
    font-weight: 500;
}

.payment-method-status.enabled {
    color: var(--pexlat-primary-color, #2563eb);
}

.payment-method-status.disabled {
    color: #dc3545;
}

.payment-description {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
}

.notice-info {
    background: #e7f3ff;
    border-left: 4px solid #007cba;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
}

.notice-warning {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    color: #856404;
}

.payment-method-toggle {
    margin-left: auto;
}

/* تنسيق زر التبديل */
.switch-toggle {
    position: relative;
    display: inline-block;
    width: 46px;
    height: 26px;
}

.switch-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--pexlat-primary-color, #2563eb);
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px var(--pexlat-primary-color, #2563eb);
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

.payment-method-content {
    padding: 20px;
}

.payment-field {
    margin-bottom: 15px;
}

.payment-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

/* تنسيق رفع صور طرق الدفع */
.payment-logo-upload {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 8px;
}

.logo-preview {
    width: 120px;
    height: 60px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f9f9f9;
    flex-shrink: 0;
}

.logo-preview img {
    max-width: 100px;
    max-height: 50px;
    border-radius: 8px;
}

.logo-preview .no-logo {
    color: #666;
    font-size: 12px;
    text-align: center;
}

.logo-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.logo-controls .button {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.4;
}

.payment-field input,
.payment-field textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.payment-field .description {
    margin-top: 5px;
    font-size: 13px;
    color: #666;
}

.main-toggle-label {
    display: flex;
    align-items: center;
    font-weight: 600;
    margin-bottom: 10px;
}

.main-toggle-label input[type="checkbox"] {
    margin-right: 10px;
}

.switch-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #007cba;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}
</style>
