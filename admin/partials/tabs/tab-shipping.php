<?php
/**
 * قالب إعدادات طرق التوصيل
 *
 * @var array $form_settings إعدادات النموذج
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="collapsible-section open" id="shipping-methods-section">
    <div class="collapsible-header">
        <h3><span class="subsection-icon dashicons dashicons-car"></span>طرق التوصيل</h3>
        <span class="collapsible-toggle dashicons dashicons-arrow-up-alt2"></span>
    </div>
    <h4 class="shipping-section-title">طرق الشحن</h4>
    <div class="collapsible-content">
        <!-- إعداد طريقة عرض طرق التوصيل -->
        <div class="shipping-display-settings">
            <h5 class="shipping-display-title">طريقة عرض طرق التوصيل</h5>
            <?php
            // تصحيح: عرض القيمة الحالية
            $current_mode = $form_settings['shipping_display_mode'] ?? 'detailed';
            echo '<!-- القيمة الحالية: ' . $current_mode . ' -->';
            ?>
            <div class="shipping-display-options">
                <div class="display-option">
                    <label class="display-option-label">
                        <input type="radio" name="form_settings[shipping_display_mode]" value="detailed"
                               <?php checked($current_mode, 'detailed'); ?>>
                        <div class="display-option-content">
                            <div class="display-option-icon">
                                <span class="dashicons dashicons-list-view"></span>
                            </div>
                            <div class="display-option-text">
                                <strong>العرض التفصيلي</strong>
                                <p>عرض طرق التوصيل بالشكل الحالي مع التفاصيل الكاملة</p>
                            </div>
                        </div>
                    </label>
                </div>
                <div class="display-option">
                    <label class="display-option-label">
                        <input type="radio" name="form_settings[shipping_display_mode]" value="simple"
                               <?php checked($current_mode, 'simple'); ?>>
                        <div class="display-option-content">
                            <div class="display-option-icon">
                                <span class="dashicons dashicons-editor-ul"></span>
                            </div>
                            <div class="display-option-text">
                                <strong>العرض المبسط</strong>
                                <p>عرض طرق التوصيل كأزرار راديو بسيطة في ملخص الطلب</p>
                            </div>
                        </div>
                    </label>
                </div>
            </div>
        </div>

        <div class="shipping-methods-container">
            <!-- طريقة الشحن الثانية -->
            <div class="shipping-method-card">
                <div class="shipping-method-header">
                    <div class="shipping-icon shipping-icon-secondary">
                        <span class="dashicons dashicons-performance"></span>
                    </div>
                    <h5 class="shipping-method-title">طريقة الشحن الثانية</h5>
                    <div class="shipping-method-toggle">
                        <label class="switch-toggle">
                            <input type="checkbox" id="default_shipping_2_enabled" name="form_settings[default_shipping_2_enabled]" value="1"
                                <?php checked(isset($form_settings['default_shipping_2_enabled']) ? intval($form_settings['default_shipping_2_enabled']) : 1, 1); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="shipping-method-content">
                    <div class="shipping-field">
                        <label for="default_shipping_2_title">اسم طريقة الشحن</label>
                        <input type="text" id="default_shipping_2_title" name="form_settings[default_shipping_2_title]"
                            value="<?php echo isset($form_settings['default_shipping_2_title']) ? esc_attr($form_settings['default_shipping_2_title']) : 'توصيل للمكتب'; ?>">
                    </div>

                    <div class="shipping-field">
                        <label for="default_shipping_2_cost">تكلفة الشحن (د.ج)</label>
                        <div class="shipping-cost-input">
                            <input type="number" id="default_shipping_2_cost" name="form_settings[default_shipping_2_cost]" min="0" step="50"
                                value="<?php echo isset($form_settings['default_shipping_2_cost']) ? intval($form_settings['default_shipping_2_cost']) : 600; ?>">
                            <span class="currency-symbol">د.ج</span>
                        </div>
                    </div>

                    <div class="shipping-field">
                        <label for="default_shipping_2_description">وصف طريقة الشحن</label>
                        <input type="text" id="default_shipping_2_description" name="form_settings[default_shipping_2_description]"
                            value="<?php echo isset($form_settings['default_shipping_2_description']) ? esc_attr($form_settings['default_shipping_2_description']) : ''; ?>" placeholder="اترك فارغاً إذا لم ترغب بإظهار وصف">
                    </div>
                </div>
            </div>

            <!-- طريقة الشحن الأولى -->
            <div class="shipping-method-card">
                <div class="shipping-method-header">
                    <div class="shipping-icon shipping-icon-primary">
                        <span class="dashicons dashicons-car"></span>
                    </div>
                    <h5 class="shipping-method-title">طريقة الشحن الأولى</h5>
                    <div class="shipping-method-toggle">
                        <label class="switch-toggle">
                            <input type="checkbox" id="default_shipping_1_enabled" name="form_settings[default_shipping_1_enabled]" value="1"
                                <?php checked(isset($form_settings['default_shipping_1_enabled']) ? intval($form_settings['default_shipping_1_enabled']) : 1, 1); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="shipping-method-content">
                    <div class="shipping-field">
                        <label for="default_shipping_1_title">اسم طريقة الشحن</label>
                        <input type="text" id="default_shipping_1_title" name="form_settings[default_shipping_1_title]"
                            value="<?php echo isset($form_settings['default_shipping_1_title']) ? esc_attr($form_settings['default_shipping_1_title']) : 'توصيل للمنزل'; ?>">
                    </div>

                    <div class="shipping-field">
                        <label for="default_shipping_1_cost">تكلفة الشحن (د.ج)</label>
                        <div class="shipping-cost-input">
                            <input type="number" id="default_shipping_1_cost" name="form_settings[default_shipping_1_cost]" min="0" step="50"
                                value="<?php echo isset($form_settings['default_shipping_1_cost']) ? intval($form_settings['default_shipping_1_cost']) : 800; ?>">
                            <span class="currency-symbol">د.ج</span>
                        </div>
                    </div>

                    <div class="shipping-field">
                        <label for="default_shipping_1_description">وصف طريقة الشحن</label>
                        <input type="text" id="default_shipping_1_description" name="form_settings[default_shipping_1_description]"
                            value="<?php echo isset($form_settings['default_shipping_1_description']) ? esc_attr($form_settings['default_shipping_1_description']) : ''; ?>" placeholder="اترك فارغاً إذا لم ترغب بإظهار وصف">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* تنسيق قسم طرق التوصيل */
.shipping-display-settings {
    background: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.shipping-display-title {
    margin: 0 0 15px;
    font-size: 14px;
    font-weight: 600;
    color: #1d2327;
}

.shipping-display-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.display-option {
    background: white;
    border: 2px solid #e2e4e7;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
    cursor: pointer;
}

.display-option:hover {
    border-color: #c3c4c7;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.display-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.display-option input[type="radio"]:checked + .display-option-content,
.display-option.selected .display-option-content {
    border-color: #2271b1;
    background-color: #f0f6fc;
}

.display-option.selected {
    border-color: #2271b1;
    box-shadow: 0 2px 8px rgba(34, 113, 177, 0.15);
}

.display-option-label {
    display: block;
    cursor: pointer;
    margin: 0;
}

.display-option-content {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.display-option-icon {
    width: 40px;
    height: 40px;
    background: #2271b1;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    flex-shrink: 0;
}

.display-option-icon .dashicons {
    color: white;
    font-size: 20px;
    width: 20px;
    height: 20px;
}

.display-option-text {
    flex: 1;
}

.display-option-text strong {
    display: block;
    margin-bottom: 5px;
    color: #1d2327;
    font-size: 14px;
}

.display-option-text p {
    margin: 0;
    font-size: 12px;
    color: #646970;
    line-height: 1.4;
}

.shipping-info-text {
    margin: 5px 0 15px;
    font-size: 13px;
    color: #646970;
    line-height: 1.5;
}

.shipping-info-description {
    margin: 5px 0 15px;
    font-size: 13px;
    color: #646970;
    line-height: 1.5;
}

.shipping-section-title {
    margin: 0 0 15px;
    font-size: 15px;
    color: #2271b1;
    border-bottom: 1px solid #e2e4e7;
    padding-bottom: 8px;
    font-weight: 500;
}

.shipping-methods-container {
    display: flex;
    flex-direction: row;
    gap: 20px;
    margin-top: 5px;
}

.shipping-method-card {
    flex: 1;
    min-width: 300px;
    background: #fff;
    border: 1px solid #e2e4e7;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.shipping-method-card:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    border-color: #c3c4c7;
}

.shipping-method-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: #f0f6fc;
    border-bottom: 1px solid #e2e4e7;
}

.shipping-method-toggle {
    margin-right: auto;
}

.shipping-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 10px;
}

.shipping-icon-primary {
    background-color: #2271b1;
    color: white;
}

.shipping-icon-secondary {
    background-color: #3582c4;
    color: white;
}

.shipping-icon .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
}

.shipping-method-title {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #1d2327;
}

.shipping-method-content {
    padding: 15px;
}

.shipping-field {
    margin-bottom: 15px;
}

.shipping-field:last-child {
    margin-bottom: 0;
}

.shipping-field label {
    display: block;
    margin-bottom: 6px;
    font-size: 13px;
    font-weight: 500;
    color: #1d2327;
}

.shipping-field input {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #dcdcde;
    border-radius: 4px;
    font-size: 13px;
    transition: all 0.2s ease;
}

.shipping-field input:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.shipping-cost-input {
    position: relative;
}

.shipping-cost-input input {
    padding-left: 40px;
}

.currency-symbol {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #646970;
    font-size: 13px;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 782px) {
    .shipping-methods-container {
        flex-direction: column;
    }

    .shipping-method-card {
        width: 100%;
        margin-bottom: 15px;
    }

    .shipping-section-title {
        margin-bottom: 10px;
    }
}
</style>

<!-- تم إزالة صندوق المعلومات -->

<style type="text/css">
.shipping-methods-info {
    margin: 20px 0;
}
.info-box {
    background-color: #f0f6fb;
    border-right: 4px solid #2271b1;
    padding: 15px;
    display: flex;
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.info-box .dashicons {
    font-size: 24px;
    color: #2271b1;
    margin-left: 15px;
    margin-right: 0;
}
.info-content {
    flex: 1;
}
.info-content h4 {
    margin: 0 0 10px;
    color: #1d2327;
}
.info-content p {
    margin: 0 0 10px;
}
.info-content ul {
    margin: 10px 20px;
    list-style-type: disc;
}
.info-content ul li {
    margin-bottom: 5px;
}

/* تنسيق قسم طرق الشحن الافتراضية */
.default-shipping-group {
    margin: 25px 0;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.settings-subtitle {
    margin-top: 0;
    margin-bottom: 10px;
    color: #2271b1;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.default-shipping-methods {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 15px;
}

.default-shipping-method {
    flex: 1;
    min-width: 300px;
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 15px;
}

.default-shipping-method h5 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #444;
    font-size: 14px;
    padding-bottom: 8px;
    border-bottom: 1px dashed #ddd;
}

.default-shipping-method .settings-field {
    margin-bottom: 15px;
}

.default-shipping-method input[type="text"],
.default-shipping-method input[type="number"] {
    width: 100%;
}

@media (max-width: 782px) {
    .default-shipping-methods {
        flex-direction: column;
    }

    .default-shipping-method {
        width: 100%;
    }
}

/* تنسيق زر التبديل */
.switch-toggle {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.switch-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #2271b1;
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px #2271b1;
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}
</style>

<script>
jQuery(document).ready(function($) {
    // تحديث حالة العرض عند تغيير الإعداد
    $('input[name="form_settings[shipping_display_mode]"]').on('change', function() {
        var selectedMode = $(this).val();

        // تحديث المظهر البصري للخيارات
        $('.display-option').removeClass('selected');
        $(this).closest('.display-option').addClass('selected');

        // إظهار رسالة توضيحية
        var message = selectedMode === 'simple' ?
            'سيتم عرض طرق التوصيل كأزرار راديو بسيطة في ملخص الطلب' :
            'سيتم عرض طرق التوصيل بالشكل التفصيلي الحالي';

        // إزالة الرسائل السابقة
        $('.shipping-display-message').remove();

        // إضافة رسالة جديدة
        $('.shipping-display-settings').append(
            '<div class="shipping-display-message" style="margin-top: 15px; padding: 10px; background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; color: #0c5460; font-size: 13px;">' +
            '<span class="dashicons dashicons-info" style="margin-left: 5px;"></span>' + message +
            '</div>'
        );

        // إزالة الرسالة بعد 3 ثوان
        setTimeout(function() {
            $('.shipping-display-message').fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    });

    // تطبيق الحالة المحددة عند تحميل الصفحة
    var selectedOption = $('input[name="form_settings[shipping_display_mode]"]:checked');
    if (selectedOption.length > 0) {
        selectedOption.closest('.display-option').addClass('selected');
    }
});
</script>