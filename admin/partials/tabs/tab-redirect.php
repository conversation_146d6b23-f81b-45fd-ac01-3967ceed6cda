<?php
/**
 * قالب إعدادات صفحة التوجيه
 *
 * @var array $form_settings إعدادات النموذج
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="collapsible-section open" id="redirect-section">
    <div class="collapsible-header">
        <h3><span class="subsection-icon dashicons dashicons-external"></span>توجيه المستخدم بعد الإرسال</h3>
        <span class="collapsible-toggle dashicons dashicons-arrow-up-alt2"></span>
    </div>
    <div class="collapsible-content">
        <div class="settings-group">
            <div class="settings-field">
                <label for="redirect-page-id">صفحة التوجيه بعد إتمام الطلب</label>
                <select name="settings[redirect_page_id]" id="redirect-page-id" class="regular-text">
                    <option value="">صفحة الشكر الافتراضية لووكومرس</option>
                    <?php
                    // الحصول على قائمة الصفحات
                    $pages = get_pages();
                    $selected_page = isset($form_settings['redirect_page_id']) ? intval($form_settings['redirect_page_id']) : 0;
                    // تسجيل قيمة الصفحة المختارة للتصحيح
                    error_log('قيمة صفحة التوجيه المختارة: ' . $selected_page);

                    foreach ($pages as $page) {
                        echo '<option value="' . $page->ID . '" ' . selected($selected_page, $page->ID, false) . '>' . esc_html($page->post_title) . '</option>';
                    }
                    ?>
                </select>
                <p class="description">اختر الصفحة التي تريد توجيه المستخدم إليها بعد إتمام الطلب بنجاح. إذا لم تختر صفحة، سيتم توجيه المستخدم إلى صفحة الشكر الافتراضية الخاصة بووكومرس.</p>
            </div>

            <div class="info-box">
                <i class="dashicons dashicons-info"></i>
                <div class="info-content">
                    <h4>ملاحظة هامة</h4>
                    <p>لعرض تفاصيل الطلب في الصفحة المخصصة، يجب عليك إضافة الكود القصير التالي في محتوى الصفحة:</p>
                    <code>[woocommerce_order_details]</code>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-box {
    background-color: #f0f6fb;
    border-right: 4px solid #2271b1;
    padding: 15px;
    display: flex;
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    margin-top: 20px;
}
.info-box .dashicons {
    font-size: 24px;
    color: #2271b1;
    margin-left: 15px;
    margin-right: 0;
}
.info-content {
    flex: 1;
}
.info-content h4 {
    margin: 0 0 10px;
    color: #1d2327;
}
.info-content p {
    margin: 0 0 10px;
}
.info-content code {
    background: #f6f7f7;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 14px;
    display: inline-block;
    margin-top: 5px;
    border: 1px solid #ddd;
}
</style>

<script>
jQuery(document).ready(function($) {
    // التأكد من حفظ قيمة صفحة التوجيه المخصصة
    $('#form-editor-form').on('submit', function() {
        // تخزين قيمة الصفحة المختارة في متغير
        var redirectPageId = $('#redirect-page-id').val();
        console.log('صفحة التوجيه المراد حفظها:', redirectPageId);

        // إضافة حقل مخفي إضافي للتأكد من إرسال القيمة
        $(this).append('<input type="hidden" name="settings[redirect_page_id_backup]" value="' + redirectPageId + '">');

        // تسجيل البيانات المرسلة للتصحيح
        console.log('تم إضافة حقل مخفي: settings[redirect_page_id_backup] = ' + redirectPageId);
    });

    // تحديث قيمة الحقل عند التغيير
    $('#redirect-page-id').on('change', function() {
        var selectedValue = $(this).val();
        console.log('تم تغيير صفحة التوجيه إلى: ' + selectedValue);
    });

    // تسجيل قيمة صفحة التوجيه في وحدة التحكم عند تحميل الصفحة
    console.log('قيمة صفحة التوجيه الحالية:', $('#redirect-page-id').val());
});