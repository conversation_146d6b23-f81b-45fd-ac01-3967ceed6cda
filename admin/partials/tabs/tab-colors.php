<?php
/**
 * تبويب الألوان والتصميم
 *
 * @var array $form_settings إعدادات النموذج
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// تضمين ملف مجموعات الألوان
require_once plugin_dir_path(__FILE__) . '../../../includes/color-schemes.php';

// الحصول على مجموعات الألوان المتاحة
$color_schemes = fe_get_color_schemes();
$current_scheme = $form_settings['color_scheme'] ?? 'blue_professional';
$unified_theme_enabled = $form_settings['unified_theme_enabled'] ?? 'yes';
?>

<!-- قسم النظام الموحد -->
<div class="unified-theme-section">
    <h3>النظام الموحد للألوان والتصميم</h3>

    <div class="settings-group">
        <div class="settings-field">
            <label for="unified-theme-toggle">
                <input type="checkbox" id="unified-theme-toggle" name="settings[unified_theme_enabled]"
                    value="yes" <?php checked($unified_theme_enabled, 'yes'); ?>>
                تفعيل النظام الموحد للألوان والتصميم
            </label>
            <p class="description">عند التفعيل، ستتحكم مجموعة الألوان المختارة في جميع عناصر النموذج.</p>
        </div>
    </div>

    <div class="unified-settings" style="<?php echo $unified_theme_enabled === 'yes' ? '' : 'display: none;'; ?>">
        <!-- تخطيط أفقي للعناصر الأساسية -->
        <div class="unified-controls-row">
            <!-- اختيار مجموعة الألوان -->
            <div class="color-scheme-section">
                <label for="color-scheme">اختر مجموعة الألوان</label>
                <div class="color-schemes-compact">
                    <?php foreach ($color_schemes as $scheme_id => $scheme): ?>
                        <div class="color-scheme-compact <?php echo $current_scheme === $scheme_id ? 'active' : ''; ?>"
                             data-scheme="<?php echo esc_attr($scheme_id); ?>" title="<?php echo esc_html($scheme['name']); ?>">
                            <input type="radio" name="settings[color_scheme]" value="<?php echo esc_attr($scheme_id); ?>"
                                <?php checked($current_scheme, $scheme_id); ?> style="display: none;">
                            <div class="scheme-preview-compact">
                                <div class="color-palette-compact">
                                    <div class="color-dot" style="background-color: <?php echo esc_attr($scheme['colors']['primary']); ?>"></div>
                                    <div class="color-dot" style="background-color: <?php echo esc_attr($scheme['colors']['card_background']); ?>"></div>
                                    <div class="color-dot" style="background-color: <?php echo esc_attr($scheme['colors']['border']); ?>"></div>
                                </div>
                                <span class="scheme-name"><?php echo esc_html($scheme['name']); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- قسم الألوان المخصصة -->
        <div class="custom-colors-section" style="<?php echo $current_scheme === 'custom' ? '' : 'display: none;'; ?>">
            <div class="settings-group">
                <h4>تخصيص الألوان</h4>
                <p class="description">قم بتخصيص كل لون حسب احتياجاتك</p>

                <?php
                $customizable_colors = fe_get_customizable_colors();

                // تجميع الألوان حسب المجموعة
                $color_groups = array();
                foreach ($customizable_colors as $color_key => $color_info) {
                    $group = $color_info['group'];
                    if (!isset($color_groups[$group])) {
                        $color_groups[$group] = array();
                    }
                    $color_groups[$group][$color_key] = $color_info;
                }

                // أسماء المجموعات مع الأيقونات
                $group_names = array(
                    'primary' => array(
                        'name' => 'الألوان الرئيسية',
                        'icon' => '🎨'
                    ),
                    'background' => array(
                        'name' => 'ألوان الخلفية',
                        'icon' => '🖼️'
                    ),
                    'text' => array(
                        'name' => 'ألوان النصوص',
                        'icon' => '📝'
                    ),
                    'input' => array(
                        'name' => 'ألوان الحقول',
                        'icon' => '📋'
                    ),
                    'border' => array(
                        'name' => 'ألوان الحدود',
                        'icon' => '🔲'
                    )
                );
                ?>

                <div class="custom-colors-horizontal-layout">
                    <?php foreach ($color_groups as $group_key => $group_colors): ?>
                        <div class="color-group-horizontal">
                            <h5 class="color-group-title">
                                <span class="group-icon"><?php echo $group_names[$group_key]['icon'] ?? '🎨'; ?></span>
                                <?php echo esc_html($group_names[$group_key]['name'] ?? $group_key); ?>
                            </h5>
                            <div class="color-fields-horizontal">
                                <?php foreach ($group_colors as $color_key => $color_info): ?>
                                    <div class="color-field-horizontal">
                                        <label for="<?php echo esc_attr($color_key); ?>" class="color-label"><?php echo esc_html($color_info['name']); ?></label>
                                        <div class="color-input-wrapper-horizontal">
                                            <input type="color"
                                                   name="settings[<?php echo esc_attr($color_key); ?>]"
                                                   id="<?php echo esc_attr($color_key); ?>"
                                                   value="<?php echo esc_attr($form_settings[$color_key] ?? $color_info['default']); ?>"
                                                   class="custom-color-picker-horizontal">
                                            <input type="text"
                                                   value="<?php echo esc_attr($form_settings[$color_key] ?? $color_info['default']); ?>"
                                                   class="color-text-input-horizontal"
                                                   placeholder="<?php echo esc_attr($color_info['default']); ?>">
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <div class="settings-group">
            <div class="settings-field border-radius-section">
                <label for="unified-border-radius">زوايا العناصر (موحدة)</label>
                <input type="range" name="settings[unified_border_radius]" id="unified-border-radius"
                    value="<?php echo esc_attr($form_settings['unified_border_radius'] ?? '8'); ?>"
                    min="0" max="30" step="1"
                    oninput="this.nextElementSibling.value = this.value + ' بكسل'">
                <output><?php echo esc_attr($form_settings['unified_border_radius'] ?? '8'); ?> بكسل</output>
                <p class="description">يتحكم في زوايا جميع العناصر: الحقول، الأزرار، الحاويات، وطرق الشحن.</p>
            </div>
        </div>
    </div>
</div>

<!-- حقول مخفية لضمان إرسال القيم -->
<input type="hidden" name="settings[unified_theme_enabled]" value="<?php echo $unified_theme_enabled === 'yes' ? 'yes' : 'no'; ?>" id="unified-theme-hidden">
<input type="hidden" name="settings[color_scheme]" value="<?php echo esc_attr($current_scheme); ?>" id="color-scheme-hidden">
<input type="hidden" name="settings[unified_border_radius]" value="<?php echo esc_attr($form_settings['unified_border_radius'] ?? '8'); ?>" id="border-radius-hidden">

<div class="manual-settings" style="<?php echo $unified_theme_enabled === 'yes' ? 'display: none;' : ''; ?>">
    <h3>الألوان الرئيسية (الإعداد اليدوي)</h3>

<div class="settings-group">
    <div class="settings-field">
        <label for="primary-color">اللون الرئيسي</label>
        <input type="text" name="settings[icons_color]" id="primary-color" class="color-picker" 
            value="<?php echo esc_attr($form_settings['icons_color'] ?? '#2563eb'); ?>">
        <p class="description">يتحكم في لون الأيقونات والعناصر التفاعلية وتأثيرات التحويم.</p>
    </div>
    
    <div class="settings-field">
        <label for="card-bg-color">خلفية النموذج</label>
        <input type="text" name="settings[card_bg_color]" id="card-bg-color" class="color-picker" 
            value="<?php echo esc_attr($form_settings['card_bg_color'] ?? '#ffffff'); ?>">
        <p class="description">لون خلفية النموذج بالكامل.</p>
    </div>
</div>

<h3>تصميم النموذج</h3>

<div class="settings-group">
    <div class="settings-field">
        <label for="card-border-radius">حواف النموذج</label>
        <input type="range" name="settings[card_border_radius]" id="card-border-radius" 
            value="<?php echo esc_attr($form_settings['card_border_radius'] ?? '8'); ?>" min="0" max="30" step="1"
            oninput="this.nextElementSibling.value = this.value + ' بكسل'">
        <o><?php echo esc_attr($form_settings['card_border_radius'] ?? '8'); ?> بكسل</o>
    </div>
    
    <div class="settings-field">
        <label for="card-shadow">ظل النموذج</label>
        <select name="settings[card_shadow]" id="card-shadow">
            <option value="none" <?php selected($form_settings['card_shadow'] ?? 'medium', 'none'); ?>>بدون ظل</option>
            <option value="small" <?php selected($form_settings['card_shadow'] ?? 'medium', 'small'); ?>>خفيف</option>
            <option value="medium" <?php selected($form_settings['card_shadow'] ?? 'medium', 'medium'); ?>>متوسط</option>
            <option value="large" <?php selected($form_settings['card_shadow'] ?? 'medium', 'large'); ?>>كثيف</option>
        </select>
    </div>
    
    <div class="settings-field">
        <label for="form-border-style">نوع حدود النموذج</label>
        <select name="settings[form_border_style]" id="form-border-style">
            <option value="none" <?php selected($form_settings['form_border_style'] ?? 'none', 'none'); ?>>بدون حدود</option>
            <option value="solid" <?php selected($form_settings['form_border_style'] ?? 'none', 'solid'); ?>>خط متصل</option>
            <option value="dashed" <?php selected($form_settings['form_border_style'] ?? 'none', 'dashed'); ?>>خط متقطع</option>
            <option value="dotted" <?php selected($form_settings['form_border_style'] ?? 'none', 'dotted'); ?>>نقاط</option>
        </select>
    </div>
    
    <div class="settings-field border-settings" style="<?php echo ($form_settings['form_border_style'] ?? 'none') == 'none' ? 'display: none;' : ''; ?>">
        <label for="form-border-color">لون الحدود</label>
        <input type="text" name="settings[form_border_color]" id="form-border-color" class="color-picker" 
            value="<?php echo esc_attr($form_settings['form_border_color'] ?? '#e2e8f0'); ?>">
    </div>
    
    <div class="settings-field border-settings" style="<?php echo ($form_settings['form_border_style'] ?? 'none') == 'none' ? 'display: none;' : ''; ?>">
        <label for="form-border-width">سمك الحدود</label>
        <input type="range" name="settings[form_border_width]" id="form-border-width" 
            value="<?php echo esc_attr($form_settings['form_border_width'] ?? '1'); ?>" min="1" max="10" step="1"
            oninput="this.nextElementSibling.value = this.value + ' بكسل'">
        <o><?php echo esc_attr($form_settings['form_border_width'] ?? '1'); ?> بكسل</o>
    </div>

    <div class="settings-field">
        <label for="text-color">لون النص</label>
        <input type="text" name="settings[text_color]" id="text-color" class="color-picker"
            value="<?php echo esc_attr($form_settings['text_color'] ?? '#1e293b'); ?>">
        <p class="description">لون النص الرئيسي في النموذج.</p>
    </div>
</div>

</div> <!-- إغلاق manual-settings -->

<style>
/* أنماط النظام الموحد */
.unified-theme-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.unified-theme-section h3 {
    margin-top: 0;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

/* تخطيط أفقي للعناصر الأساسية */
.unified-controls-row {
    display: flex;
    gap: 30px;
    margin: 20px 0;
    align-items: flex-start;
}

/* قسم اختيار مجموعة الألوان */
.color-scheme-section {
    flex: 2;
    min-width: 0;
}

.color-scheme-section label {
    display: block;
    font-weight: 600;
    margin-bottom: 12px;
    color: #2c3e50;
    font-size: 14px;
}

.color-schemes-compact {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;
}

.color-scheme-compact {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    text-align: center;
}

.color-scheme-compact:hover {
    border-color: #3498db;
    box-shadow: 0 2px 6px rgba(52, 152, 219, 0.1);
}

.color-scheme-compact.active {
    border-color: #3498db;
    background: #f8f9ff;
    box-shadow: 0 2px 6px rgba(52, 152, 219, 0.2);
}

.scheme-preview-compact {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.color-palette-compact {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin-bottom: 4px;
}

.color-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.scheme-name {
    font-size: 12px;
    font-weight: 500;
    color: #2c3e50;
    line-height: 1.3;
}

/* قسم زوايا العناصر */
.border-radius-section {
    flex: 1;
    min-width: 200px;
}

.border-radius-section label {
    display: block;
    font-weight: 600;
    margin-bottom: 12px;
    color: #2c3e50;
    font-size: 14px;
}

.range-control-compact {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.range-control-compact input[type="range"] {
    width: 100%;
    margin: 0;
}

.range-control-compact output {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
    font-weight: 500;
}

.description-compact {
    font-size: 12px;
    color: #7f8c8d;
    margin: 8px 0 0 0;
    line-height: 1.4;
}

.color-schemes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.color-scheme-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.color-scheme-option:hover {
    border-color: #3498db;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.color-scheme-option.active {
    border-color: #3498db;
    background: #f8f9ff;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
}

.scheme-preview {
    display: flex;
    align-items: center;
    gap: 15px;
}

.color-palette {
    display: flex;
    gap: 5px;
}

.color-swatch {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.scheme-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

.scheme-info p {
    margin: 0;
    font-size: 12px;
    color: #7f8c8d;
    line-height: 1.4;
}

.manual-settings {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.manual-settings h3 {
    color: #7f8c8d;
}

/* تحسين مظهر المفتاح */
#unified-theme-toggle {
    margin-left: 8px;
    transform: scale(1.2);
}

/* تحسين مظهر شريط التمرير */
#unified-border-radius {
    width: 100%;
    margin: 10px 0;
}

#unified-border-radius + output {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 10px;
}

/* أنماط قسم الألوان المخصصة */
.custom-colors-section {
    margin-top: 25px;
    padding: 25px;
    background: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #e1e1e1;
    width: 100%;
    clear: both;
    display: block;
}

.custom-colors-section h4 {
    margin-top: 0;
    color: #2c3e50;
    font-size: 18px;
}

/* التخطيط الأفقي الجديد للألوان المخصصة */
.custom-colors-horizontal-layout {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin: 20px 0;
}

.color-group-horizontal {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.color-group-horizontal:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.color-group-title {
    margin: 0 0 15px 0;
    color: #1e293b;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.group-icon {
    font-size: 18px;
    display: inline-block;
}

.color-fields-horizontal {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: flex-start;
}

.color-field-horizontal {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 200px;
    flex: 1;
    padding: 12px;
    background: #f8fafc;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.color-field-horizontal:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
}

.color-label {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
    margin-bottom: 5px;
}

.color-input-wrapper-horizontal {
    display: flex;
    gap: 10px;
    align-items: center;
}

.custom-color-picker-horizontal {
    width: 50px;
    height: 40px;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    cursor: pointer;
    padding: 0;
    transition: border-color 0.2s ease;
}

.custom-color-picker-horizontal:hover {
    border-color: #9ca3af;
}

.custom-color-picker-horizontal:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.color-text-input-horizontal {
    flex: 1;
    padding: 10px 12px;
    border: 2px solid #d1d5db;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    background-color: #f9fafb;
    transition: all 0.2s ease;
    min-width: 120px;
}

.color-text-input-horizontal:focus {
    outline: none;
    border-color: #3b82f6;
    background-color: #ffffff;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.color-text-input-horizontal:hover {
    border-color: #9ca3af;
    background-color: #ffffff;
}

/* تحسين التخطيط للشاشات الصغيرة */
@media (max-width: 768px) {
    .color-fields-horizontal {
        flex-direction: column;
        gap: 15px;
    }

    .color-field-horizontal {
        min-width: auto;
    }

    .color-input-wrapper-horizontal {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .custom-color-picker-horizontal {
        width: 100%;
        height: 45px;
    }
}

/* تحسين مظهر الشبكة على الشاشات الصغيرة */
@media (max-width: 768px) {
    .unified-controls-row {
        flex-direction: column;
        gap: 20px;
    }

    .color-schemes-compact {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
    }

    .color-scheme-compact {
        padding: 10px;
    }

    .scheme-name {
        font-size: 11px;
    }

    .border-radius-section {
        min-width: auto;
    }

    .custom-colors-grid {
        grid-template-columns: 1fr;
    }

    .color-input-wrapper {
        flex-direction: column;
        align-items: stretch;
    }

    .custom-color-picker {
        width: 100%;
        height: 40px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // تبديل النظام الموحد
    $('#unified-theme-toggle').on('change', function() {
        var isEnabled = $(this).is(':checked');

        console.log('تم تغيير حالة النظام الموحد إلى: ' + (isEnabled ? 'مفعل' : 'غير مفعل'));

        // تحديث الحقل المخفي
        $('#unified-theme-hidden').val(isEnabled ? 'yes' : 'no');

        if (isEnabled) {
            $('.unified-settings').slideDown(300);
            $('.manual-settings').slideUp(300);
        } else {
            $('.unified-settings').slideUp(300);
            $('.manual-settings').slideDown(300);
        }
    });

    // اختيار مجموعة الألوان (التخطيط الجديد المضغوط)
    $('.color-scheme-compact').on('click', function() {
        $('.color-scheme-compact').removeClass('active');
        $(this).addClass('active');
        $(this).find('input[type="radio"]').prop('checked', true);

        // تحديث الحقل المخفي
        var scheme = $(this).data('scheme');
        $('#color-scheme-hidden').val(scheme);

        console.log('تم اختيار مجموعة الألوان: ' + scheme);

        // إظهار أو إخفاء قسم الألوان المخصصة
        if (scheme === 'custom') {
            $('.custom-colors-section').slideDown(300);
        } else {
            $('.custom-colors-section').slideUp(300);
        }

        applyColorSchemePreview(scheme);
    });

    // دعم التخطيط القديم أيضاً (للتوافق)
    $('.color-scheme-option').on('click', function() {
        $('.color-scheme-option').removeClass('active');
        $(this).addClass('active');
        $(this).find('input[type="radio"]').prop('checked', true);

        var scheme = $(this).data('scheme');
        $('#color-scheme-hidden').val(scheme);

        if (scheme === 'custom') {
            $('.custom-colors-section').slideDown(300);
        } else {
            $('.custom-colors-section').slideUp(300);
        }

        applyColorSchemePreview(scheme);
    });

    // معاينة فورية لمجموعة الألوان (اختياري)
    function applyColorSchemePreview(scheme) {
        // يمكن إضافة معاينة فورية هنا
        console.log('تم اختيار مجموعة الألوان: ' + scheme);
    }

    // تحديث قيمة شريط الزوايا
    $('#unified-border-radius').on('input', function() {
        var value = $(this).val();
        $(this).next('output').text(value + ' بكسل');

        // تحديث الحقل المخفي
        $('#border-radius-hidden').val(value);

        console.log('تم تغيير الزوايا الموحدة إلى: ' + value);
    });

    // التعامل مع منتقي الألوان المخصص (التخطيط الأفقي الجديد)
    $('.custom-color-picker-horizontal').on('change', function() {
        var colorValue = $(this).val();
        $(this).siblings('.color-text-input-horizontal').val(colorValue);
    });

    // التعامل مع حقل النص للألوان (التخطيط الأفقي الجديد)
    $('.color-text-input-horizontal').on('input', function() {
        var colorValue = $(this).val();
        // التحقق من صحة اللون
        if (/^#[0-9A-F]{6}$/i.test(colorValue)) {
            $(this).siblings('.custom-color-picker-horizontal').val(colorValue);
            $(this).css('border-color', '#d1d5db');
        } else {
            $(this).css('border-color', '#ef4444');
        }
    });

    // دعم التخطيط القديم أيضاً (للتوافق)
    $('.custom-color-picker').on('change', function() {
        var colorValue = $(this).val();
        $(this).siblings('.color-text-input').val(colorValue);
    });

    $('.color-text-input').on('input', function() {
        var colorValue = $(this).val();
        if (/^#[0-9A-F]{6}$/i.test(colorValue)) {
            $(this).siblings('.custom-color-picker').val(colorValue);
            $(this).css('border-color', '#ddd');
        } else {
            $(this).css('border-color', '#e74c3c');
        }
    });

    // مراقبة إرسال النموذج
    $('form#form-editor-form').on('submit', function() {
        console.log('تم إرسال النموذج');

        // التحقق من إعدادات النظام الموحد
        var unifiedEnabled = $('#unified-theme-toggle').is(':checked');
        var colorScheme = $('input[name="settings[color_scheme]"]:checked').val();
        var borderRadius = $('#unified-border-radius').val();

        console.log('إعدادات النظام الموحد:', {
            'unified_theme_enabled': unifiedEnabled ? 'yes' : 'no',
            'color_scheme': colorScheme,
            'unified_border_radius': borderRadius
        });
    });
});
</script>