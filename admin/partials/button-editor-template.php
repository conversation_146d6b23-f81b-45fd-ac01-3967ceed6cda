<?php
/**
 * نموذج محرر زر الإرسال
 */
?>
<div class="button-editor-container">
    <!-- معاينة الزر -->
    <div class="button-preview-container">
        <h4>معاينة الزر</h4>
        <div class="button-preview">
            <button type="button" id="button-preview-element" class="preview-button">
                <span class="button-icon preview-icon-left"><i class="fas fa-check"></i></span>
                <span class="preview-text">اطلب الآن</span>
                <span class="button-icon preview-icon-right"><i class="fas fa-check"></i></span>
            </button>
        </div>
        <p class="description">سيتم تحديث المعاينة تلقائياً مع تغيير الإعدادات</p>
    </div>
    
    <!-- أقسام إعدادات الزر -->
    <div class="button-settings-container">
        <div class="button-settings-tabs">
            <ul class="button-tabs-nav">
                <li class="active"><a href="#button-tab-basic">إعدادات أساسية</a></li>
                <li><a href="#button-tab-colors">الألوان</a></li>
                <li><a href="#button-tab-icon">الأيقونة</a></li>
                <li><a href="#button-tab-effects">التأثيرات</a></li>
            </ul>
        </div>

        <!-- 1. الإعدادات الأساسية -->
        <div class="button-settings-content" id="button-tab-basic">
            <div class="button-setting-item button-text-setting">
                <label for="button-text">نص الزر</label>
                <input type="text" name="settings[button_text]" id="button-text" class="regular-text" 
                    value="<?php echo esc_attr($form_settings['button_text'] ?? 'اطلب الآن'); ?>">
            </div>
            
            <div class="button-setting-item button-size-setting">
                <label for="button-size">حجم الزر</label>
                <div class="button-size-options">
                    <label class="image-option">
                        <input type="radio" name="settings[button_size]" value="small" <?php checked($form_settings['button_size'] ?? 'medium', 'small'); ?>>
                        <span class="image-option-label">صغير</span>
                        <span class="image-option-preview small-button"></span>
                    </label>
                    <label class="image-option">
                        <input type="radio" name="settings[button_size]" value="medium" <?php checked($form_settings['button_size'] ?? 'medium', 'medium'); ?>>
                        <span class="image-option-label">متوسط</span>
                        <span class="image-option-preview medium-button"></span>
                    </label>
                    <label class="image-option">
                        <input type="radio" name="settings[button_size]" value="large" <?php checked($form_settings['button_size'] ?? 'medium', 'large'); ?>>
                        <span class="image-option-label">كبير</span>
                        <span class="image-option-preview large-button"></span>
                    </label>
                </div>
            </div>
            
            <div class="button-setting-item button-border-radius-setting">
                <label for="button-border-radius">حواف الزر</label>
                <div class="range-control">
                    <input type="range" name="settings[button_border_radius]" id="button-border-radius" 
                        value="<?php echo esc_attr($form_settings['button_border_radius'] ?? '4'); ?>" min="0" max="30" step="1"
                        oninput="this.nextElementSibling.value = this.value + ' بكسل'">
                    <output><?php echo esc_attr($form_settings['button_border_radius'] ?? '4'); ?> بكسل</output>
                </div>
                <div class="border-radius-previews">
                    <span class="radius-preview" data-value="0" title="بدون حواف"></span>
                    <span class="radius-preview" data-value="6" title="حواف صغيرة"></span>
                    <span class="radius-preview" data-value="12" title="حواف متوسطة"></span>
                    <span class="radius-preview" data-value="20" title="حواف كبيرة"></span>
                    <span class="radius-preview" data-value="30" title="حواف دائرية"></span>
                </div>
            </div>
        </div>
        
        <!-- 2. إعدادات الألوان -->
        <div class="button-settings-content" id="button-tab-colors" style="display: none;">
            <div class="button-setting-item">
                <div class="form-toggle-container">
                    <div class="toggle-label-container">
                        <label for="button-gradient-toggle">تفعيل تدرج لوني للزر</label>
                    </div>
                    <div class="toggle-switch-container">
                        <label class="toggle-switch">
                            <input type="checkbox" id="button-gradient-toggle" <?php checked(($form_settings['button_gradient'] ?? 'no'), 'yes'); ?>>
                            <span class="slider round"></span>
                        </label>
                        <input type="hidden" name="settings[button_gradient]" id="button-gradient" value="<?php echo esc_attr($form_settings['button_gradient'] ?? 'no'); ?>">
                    </div>
                </div>
            </div>
            
            <div class="button-setting-item">
                <label for="button-color">اللون الأساسي للزر</label>
                <div class="color-picker-with-presets">
                    <input type="text" name="settings[button_color]" id="button-color" class="color-picker" 
                        value="<?php echo esc_attr($form_settings['button_color'] ?? '#4CAF50'); ?>">
                    <div class="color-presets">
                        <span class="color-preset" style="background-color: #4CAF50;" data-color="#4CAF50" title="أخضر"></span>
                        <span class="color-preset" style="background-color: #2196F3;" data-color="#2196F3" title="أزرق"></span>
                        <span class="color-preset" style="background-color: #FF5722;" data-color="#FF5722" title="برتقالي"></span>
                        <span class="color-preset" style="background-color: #E91E63;" data-color="#E91E63" title="وردي"></span>
                        <span class="color-preset" style="background-color: #673AB7;" data-color="#673AB7" title="بنفسجي"></span>
                    </div>
                </div>
            </div>
            
            <div class="button-setting-item gradient-setting" <?php echo ($form_settings['button_gradient'] ?? 'no') === 'no' ? 'style="display:none;"' : ''; ?>>
                <label for="button-gradient-color">لون التدرج الثاني</label>
                <div class="color-picker-with-presets">
                    <input type="text" name="settings[button_gradient_color]" id="button-gradient-color" class="color-picker" 
                        value="<?php echo esc_attr($form_settings['button_gradient_color'] ?? '#38a169'); ?>">
                    <div class="color-presets">
                        <span class="color-preset" style="background-color: #38a169;" data-color="#38a169" title="أخضر داكن"></span>
                        <span class="color-preset" style="background-color: #1a73e8;" data-color="#1a73e8" title="أزرق داكن"></span>
                        <span class="color-preset" style="background-color: #bf360c;" data-color="#bf360c" title="برتقالي داكن"></span>
                        <span class="color-preset" style="background-color: #ad1457;" data-color="#ad1457" title="وردي داكن"></span>
                        <span class="color-preset" style="background-color: #4527a0;" data-color="#4527a0" title="بنفسجي داكن"></span>
                    </div>
                </div>
            </div>
            
            <div class="button-setting-item gradient-setting" <?php echo ($form_settings['button_gradient'] ?? 'no') === 'no' ? 'style="display:none;"' : ''; ?>>
                <label for="button-gradient-direction">اتجاه التدرج اللوني</label>
                <div class="gradient-direction-options">
                    <label class="direction-option">
                        <input type="radio" name="settings[button_gradient_direction]" value="to bottom" <?php checked($form_settings['button_gradient_direction'] ?? 'to bottom', 'to bottom'); ?>>
                        <span class="direction-preview to-bottom" title="من أعلى إلى أسفل"></span>
                    </label>
                    <label class="direction-option">
                        <input type="radio" name="settings[button_gradient_direction]" value="to right" <?php checked($form_settings['button_gradient_direction'] ?? 'to bottom', 'to right'); ?>>
                        <span class="direction-preview to-right" title="من اليمين إلى اليسار"></span>
                    </label>
                    <label class="direction-option">
                        <input type="radio" name="settings[button_gradient_direction]" value="to top" <?php checked($form_settings['button_gradient_direction'] ?? 'to bottom', 'to top'); ?>>
                        <span class="direction-preview to-top" title="من أسفل إلى أعلى"></span>
                    </label>
                    <label class="direction-option">
                        <input type="radio" name="settings[button_gradient_direction]" value="to left" <?php checked($form_settings['button_gradient_direction'] ?? 'to bottom', 'to left'); ?>>
                        <span class="direction-preview to-left" title="من اليسار إلى اليمين"></span>
                    </label>
                    <label class="direction-option">
                        <input type="radio" name="settings[button_gradient_direction]" value="to bottom right" <?php checked($form_settings['button_gradient_direction'] ?? 'to bottom', 'to bottom right'); ?>>
                        <span class="direction-preview to-bottom-right" title="قطري من أعلى اليسار"></span>
                    </label>
                    <label class="direction-option">
                        <input type="radio" name="settings[button_gradient_direction]" value="to bottom left" <?php checked($form_settings['button_gradient_direction'] ?? 'to bottom', 'to bottom left'); ?>>
                        <span class="direction-preview to-bottom-left" title="قطري من أعلى اليمين"></span>
                    </label>
                </div>
            </div>
            
            <div class="button-setting-item">
                <label for="button-text-color">لون نص الزر</label>
                <div class="color-picker-with-presets">
                    <input type="text" name="settings[button_text_color]" id="button-text-color" class="color-picker" 
                        value="<?php echo esc_attr($form_settings['button_text_color'] ?? '#ffffff'); ?>">
                    <div class="color-presets">
                        <span class="color-preset" style="background-color: #ffffff;" data-color="#ffffff" title="أبيض"></span>
                        <span class="color-preset" style="background-color: #f0f0f0;" data-color="#f0f0f0" title="رمادي فاتح"></span>
                        <span class="color-preset" style="background-color: #000000;" data-color="#000000" title="أسود"></span>
                        <span class="color-preset" style="background-color: #2d3748;" data-color="#2d3748" title="رمادي داكن"></span>
                        <span class="color-preset" style="background-color: #ffffbf;" data-color="#ffffbf" title="أصفر فاتح"></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 3. إعدادات الأيقونة -->
        <div class="button-settings-content" id="button-tab-icon" style="display: none;">
            <div class="button-setting-item button-icon-setting">
                <label>أيقونة الزر</label>
                <div class="icon-options">
                    <label class="icon-option">
                        <input type="radio" name="settings[button_icon]" value="none" <?php checked($form_settings['button_icon'] ?? 'check', 'none'); ?>>
                        <span class="icon-preview">بدون</span>
                    </label>
                    <label class="icon-option">
                        <input type="radio" name="settings[button_icon]" value="check" <?php checked($form_settings['button_icon'] ?? 'check', 'check'); ?>>
                        <span class="icon-preview"><i class="fas fa-check"></i></span>
                    </label>
                    <label class="icon-option">
                        <input type="radio" name="settings[button_icon]" value="cart" <?php checked($form_settings['button_icon'] ?? 'check', 'cart'); ?>>
                        <span class="icon-preview"><i class="fas fa-shopping-cart"></i></span>
                    </label>
                    <label class="icon-option">
                        <input type="radio" name="settings[button_icon]" value="arrow" <?php checked($form_settings['button_icon'] ?? 'check', 'arrow'); ?>>
                        <span class="icon-preview"><i class="fas fa-arrow-right"></i></span>
                    </label>
                    <label class="icon-option">
                        <input type="radio" name="settings[button_icon]" value="box" <?php checked($form_settings['button_icon'] ?? 'check', 'box'); ?>>
                        <span class="icon-preview"><i class="fas fa-box"></i></span>
                    </label>
                </div>
            </div>
            
            <div class="button-setting-item button-icon-position-setting" id="icon-position-setting" <?php echo ($form_settings['button_icon'] ?? 'check') === 'none' ? 'style="display:none;"' : ''; ?>>
                <label>موضع الأيقونة</label>
                <div class="icon-position-options">
                    <label class="position-option">
                        <input type="radio" name="settings[button_icon_position]" value="right" <?php checked($form_settings['button_icon_position'] ?? 'right', 'right'); ?>>
                        <span class="position-preview position-right">
                            <span class="text-placeholder">نص</span>
                            <i class="fas fa-check"></i>
                        </span>
                        <span class="position-label">يمين النص</span>
                    </label>
                    <label class="position-option">
                        <input type="radio" name="settings[button_icon_position]" value="left" <?php checked($form_settings['button_icon_position'] ?? 'right', 'left'); ?>>
                        <span class="position-preview position-left">
                            <i class="fas fa-check"></i>
                            <span class="text-placeholder">نص</span>
                        </span>
                        <span class="position-label">يسار النص</span>
                    </label>
                </div>
            </div>
        </div>
        
        <!-- 4. إعدادات التأثيرات -->
        <div class="button-settings-content" id="button-tab-effects" style="display: none;">
            <div class="button-setting-item button-hover-effect-setting">
                <label>تأثير عند المرور</label>
                <div class="effect-options">
                    <label class="effect-option">
                        <input type="radio" name="settings[button_hover_effect]" value="none" <?php checked($form_settings['button_hover_effect'] ?? 'shadow', 'none'); ?>>
                        <span class="effect-preview hover-none">بدون</span>
                    </label>
                    <label class="effect-option">
                        <input type="radio" name="settings[button_hover_effect]" value="shadow" <?php checked($form_settings['button_hover_effect'] ?? 'shadow', 'shadow'); ?>>
                        <span class="effect-preview hover-shadow">ظل</span>
                    </label>
                    <label class="effect-option">
                        <input type="radio" name="settings[button_hover_effect]" value="scale" <?php checked($form_settings['button_hover_effect'] ?? 'shadow', 'scale'); ?>>
                        <span class="effect-preview hover-scale">تكبير</span>
                    </label>
                    <label class="effect-option">
                        <input type="radio" name="settings[button_hover_effect]" value="glow" <?php checked($form_settings['button_hover_effect'] ?? 'shadow', 'glow'); ?>>
                        <span class="effect-preview hover-glow">توهج</span>
                    </label>
                </div>
            </div>
            
            <div class="button-setting-item button-animation-setting">
                <label>تأثير حركي</label>
                <div class="animation-options">
                    <label class="animation-option">
                        <input type="radio" name="settings[button_animation]" value="none" <?php checked($form_settings['button_animation'] ?? 'none', 'none'); ?>>
                        <span class="animation-preview">بدون</span>
                    </label>
                    <label class="animation-option">
                        <input type="radio" name="settings[button_animation]" value="pulse" <?php checked($form_settings['button_animation'] ?? 'none', 'pulse'); ?>>
                        <span class="animation-preview animation-pulse">نبضات</span>
                    </label>
                    <label class="animation-option">
                        <input type="radio" name="settings[button_animation]" value="bounce" <?php checked($form_settings['button_animation'] ?? 'none', 'bounce'); ?>>
                        <span class="animation-preview animation-bounce">قفز</span>
                    </label>
                    <label class="animation-option">
                        <input type="radio" name="settings[button_animation]" value="tada" <?php checked($form_settings['button_animation'] ?? 'none', 'tada'); ?>>
                        <span class="animation-preview animation-tada">تادا</span>
                    </label>
                    <label class="animation-option">
                        <input type="radio" name="settings[button_animation]" value="shake" <?php checked($form_settings['button_animation'] ?? 'none', 'shake'); ?>>
                        <span class="animation-preview animation-shake">اهتزاز</span>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>