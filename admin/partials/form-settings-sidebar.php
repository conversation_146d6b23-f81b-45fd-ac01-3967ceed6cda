<?php
/**
 * قالب إعدادات النموذج مع القائمة الجانبية
 *
 * @var array $form_settings إعدادات النموذج
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}
?>

<!-- إضافة Tailwind CSS -->
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
<style>
    /* تعريف الفئات المخصصة لـ Tailwind */
    .tw-flex { display: flex; }
    .tw-flex-row { flex-direction: row; }
    .tw-flex-col { flex-direction: column; }
    .tw-flex-1 { flex: 1 1 0%; }
    .tw-items-center { align-items: center; }
    .tw-justify-between { justify-content: space-between; }
    .tw-min-w-0 { min-width: 0; }
    .tw-truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
    .tw-text-sm { font-size: 0.875rem; }
    .tw-text-xs { font-size: 0.75rem; }
    .tw-font-medium { font-weight: 500; }
    .tw-text-gray-400 { color: rgba(156, 163, 175, 1); }
    .tw-text-gray-500 { color: rgba(107, 114, 128, 1); }
    .tw-mr-1 { margin-right: 0.25rem; }
    .tw-mr-2 { margin-right: 0.5rem; }
    .tw-mt-0\.5 { margin-top: 0.125rem; }
    .tw-mx-0\.5 { margin-left: 0.125rem; margin-right: 0.125rem; }
    .tw-gap-1 { gap: 0.25rem; }
    .tw-py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
    .tw-px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
    .tw-px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
    .tw-p-0\.5 { padding: 0.125rem; }
    .tw-rounded { border-radius: 0.25rem; }
    .tw-inline-block { display: inline-block; }
    .tw-italic { font-style: italic; }
    .tw-max-w-\[150px\] { max-width: 150px; }
    .tw-max-w-\[100px\] { max-width: 100px; }
    .tw-hidden { display: none; }
    .tw-bg-opacity-10 { background-opacity: 0.1; }
    .tw-bg-gray-100 { background-color: rgba(243, 244, 246, 0.3); }

    /* تنسيق الأزرار */
    .tw-text-blue-500 { color: rgba(59, 130, 246, 1); }
    .hover\:tw-text-blue-700:hover { color: rgba(29, 78, 216, 1); }
    .tw-bg-blue-50 { background-color: rgba(239, 246, 255, 1); }

    .tw-text-green-500 { color: rgba(16, 185, 129, 1); }
    .hover\:tw-text-green-700:hover { color: rgba(4, 120, 87, 1); }
    .tw-bg-green-50 { background-color: rgba(236, 253, 245, 1); }

    .tw-text-indigo-500 { color: rgba(99, 102, 241, 1); }
    .tw-bg-indigo-50 { background-color: rgba(238, 242, 255, 1); }

    .tw-text-red-500 { color: rgba(239, 68, 68, 1); }
    .hover\:tw-text-red-700:hover { color: rgba(185, 28, 28, 1); }
    .tw-bg-red-50 { background-color: rgba(254, 242, 242, 1); }

    .tw-bg-gray-50 { background-color: rgba(249, 250, 251, 1); }

    /* تصميم نظام التعديل المدمج الجديد */
    .form-field-item {
        background: #fff;
        border: 1px solid #e2e4e7;
        border-radius: 6px;
        margin-bottom: 8px;
        transition: all 0.2s ease;
    }

    .form-field-item:hover {
        border-color: #c3c4c7;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .form-field-item.editing {
        border-color: #2271b1;
        box-shadow: 0 0 0 1px #2271b1;
    }

    .field-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        cursor: pointer;
    }

    .field-header-main {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
    }

    .field-header .handle {
        color: #c3c4c7;
        margin-left: 8px;
        cursor: grab;
    }

    .field-info {
        flex: 1;
        min-width: 0;
    }

    .field-title {
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 4px 0;
        color: #1d2327;
    }

    .field-meta {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    .field-type {
        font-size: 11px;
        color: #646970;
        background: #f6f7f7;
        padding: 2px 6px;
        border-radius: 3px;
    }

    .field-status, .field-visibility {
        font-size: 11px;
        padding: 2px 6px;
        border-radius: 3px;
        font-weight: 500;
    }

    .field-status.required {
        background: #fef2f2;
        color: #dc2626;
    }

    .field-status.optional {
        background: #f0f9ff;
        color: #0369a1;
    }

    .field-visibility.visible {
        background: #f0fdf4;
        color: #16a34a;
    }

    .field-visibility.hidden {
        background: #fafafa;
        color: #6b7280;
    }

    .field-actions {
        display: flex;
        gap: 4px;
    }

    .field-actions button {
        background: none;
        border: none;
        padding: 6px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .field-actions button:hover:not(:disabled) {
        background: #f6f7f7;
    }

    .field-actions button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .field-toggle-edit .dashicons {
        color: #2271b1;
    }

    .field-clone .dashicons {
        color: #16a34a;
    }

    .field-toggle-visibility .dashicons {
        color: #7c3aed;
    }

    .field-delete .dashicons {
        color: #dc2626;
    }

    .field-permanent .dashicons {
        color: #9ca3af;
    }

    /* منطقة التعديل المدمجة */
    .field-edit-panel {
        border-top: 1px solid #e2e4e7;
        background: #f9fafb;
        animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            max-height: 0;
        }
        to {
            opacity: 1;
            max-height: 500px;
        }
    }

    .field-edit-content {
        padding: 16px;
    }

    .field-edit-row {
        margin-bottom: 16px;
    }

    .field-edit-row:last-child {
        margin-bottom: 0;
    }

    .field-edit-row label {
        display: block;
        font-weight: 600;
        margin-bottom: 6px;
        color: #1d2327;
        font-size: 13px;
    }

    .field-edit-row input,
    .field-edit-row select,
    .field-edit-row textarea {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 13px;
        transition: border-color 0.2s ease;
    }

    .field-edit-row input:focus,
    .field-edit-row select:focus,
    .field-edit-row textarea:focus {
        outline: none;
        border-color: #2271b1;
        box-shadow: 0 0 0 1px #2271b1;
    }

    .field-edit-row input:disabled,
    .field-edit-row select:disabled {
        background: #f6f7f7;
        color: #646970;
        cursor: not-allowed;
    }

    .field-toggles {
        display: flex;
        gap: 20px;
    }

    .field-toggle-option {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        font-size: 13px;
    }

    .field-toggle-option input[type="checkbox"] {
        width: auto;
        margin: 0;
    }

    .field-edit-actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #e2e4e7;
    }

    .field-save-changes {
        background: #2271b1 !important;
        color: white !important;
        border-color: #2271b1 !important;
        font-weight: 600;
    }

    .field-save-changes:hover {
        background: #135e96 !important;
        border-color: #135e96 !important;
    }

    .field-cancel-edit {
        background: #f6f7f7 !important;
        color: #646970 !important;
        border-color: #ddd !important;
    }

    .field-cancel-edit:hover {
        background: #e2e4e7 !important;
    }

    /* إخفاء البيانات المخفية */
    .field-hidden-data {
        display: none;
    }

    /* تحسين عرض خيارات القائمة */
    .field-options-row {
        display: none;
    }

    .field-options-row.show {
        display: block;
    }

    /* ملاحظة الحقول الأساسية */
    .field-edit-notice {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 4px;
        padding: 12px;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: #0073aa;
    }

    .field-edit-notice .dashicons {
        color: #0073aa;
        font-size: 16px;
        width: 16px;
        height: 16px;
    }
</style>

<div class="form-settings-container">
    <!-- القائمة الجانبية -->
    <div class="settings-sidebar">
        <!-- مجموعة الإعدادات الأساسية -->
        <div class="settings-nav-group">
            <h3 class="settings-nav-group-title">الإعدادات الأساسية</h3>
            <ul class="settings-navigation">
                <li><a href="#form-fields" class="active"><span class="nav-icon dashicons dashicons-forms"></span>النموذج والحقول</a></li>
                <li><a href="#colors-design"><span class="nav-icon dashicons dashicons-admin-appearance"></span>الألوان والتصميم</a></li>
            </ul>
        </div>

        <!-- مجموعة إعدادات التخطيط -->
        <div class="settings-nav-group">
            <h3 class="settings-nav-group-title">التخطيط والعرض</h3>
            <ul class="settings-navigation">
                <li><a href="#form-layout"><span class="nav-icon dashicons dashicons-layout"></span>تخطيط النموذج</a></li>
                <li><a href="#submit-button"><span class="nav-icon dashicons dashicons-button"></span>زر الإرسال</a></li>
            </ul>
        </div>

        <!-- مجموعة إعدادات متقدمة -->
        <div class="settings-nav-group">
            <h3 class="settings-nav-group-title">إعدادات متقدمة</h3>
            <ul class="settings-navigation">
                <li><a href="#shipping-methods"><span class="nav-icon dashicons dashicons-car"></span>طرق التوصيل</a></li>
                <li><a href="#payment-methods"><span class="nav-icon dashicons dashicons-money-alt"></span>طرق الدفع</a></li>
                <li><a href="#import-export"><span class="nav-icon dashicons dashicons-database-import"></span>تصدير/استيراد <small style="color: #666; font-size: 11px;">(تجريبي)</small></a></li>
                <li><a href="#redirect-page"><span class="nav-icon dashicons dashicons-external"></span>صفحة التوجيه</a></li>
            </ul>
        </div>



        <!-- زر حفظ الإعدادات -->
        <div class="save-settings-container">
            <button type="submit" class="button button-primary button-large save-form-settings">
                <span class="dashicons dashicons-saved"></span>
                حفظ الإعدادات
            </button>
        </div>
    </div>

    <!-- محتوى الإعدادات -->
    <div class="settings-content">
        <!-- قسم النموذج والحقول -->
        <section id="form-fields" class="settings-section" style="display: block;">
            <h2 class="settings-title"><span class="section-icon dashicons dashicons-forms"></span>النموذج والحقول</h2>

            <!-- إضافة قسم معلومات النموذج في الأعلى -->
            <?php include 'tabs/tab-form.php'; ?>

            <!-- قسم ترتيب عناصر النموذج -->
            <div class="form-elements-order-section">
                <h3><span class="subsection-icon dashicons dashicons-menu"></span>ترتيب عناصر النموذج</h3>
                <p class="description">اسحب وأفلت العناصر لتغيير ترتيبها في النموذج</p>

                <div class="form-elements-order-container">
                    <ul id="form-elements-order-list" class="form-elements-sortable">
                        <?php
                        // تحديد الترتيب الافتراضي للعناصر
                        $default_order = array(
                            'fields' => array('id' => 'fields', 'label' => 'حقول النموذج', 'icon' => 'dashicons-forms'),
                            'variations' => array('id' => 'variations', 'label' => 'متغيرات المنتج', 'icon' => 'dashicons-image-filter'),
                            'shipping' => array('id' => 'shipping', 'label' => 'طرق التوصيل', 'icon' => 'dashicons-car'),
                            'payment_methods' => array('id' => 'payment_methods', 'label' => 'طرق الدفع', 'icon' => 'dashicons-money-alt'),
                            'summary' => array('id' => 'summary', 'label' => 'ملخص الطلب', 'icon' => 'dashicons-clipboard'),
                            'button' => array('id' => 'button', 'label' => 'الأزرار وعنصر الكمية', 'icon' => 'dashicons-button')
                        );

                        // الحصول على الترتيب المخزن أو استخدام الترتيب الافتراضي
                        $elements_order = isset($form_settings['elements_order']) ? $form_settings['elements_order'] : array_keys($default_order);

                        // التأكد من أن جميع العناصر موجودة في الترتيب
                        foreach (array_keys($default_order) as $element_id) {
                            if (!in_array($element_id, $elements_order)) {
                                $elements_order[] = $element_id;
                            }
                        }

                        // عرض العناصر بالترتيب المحدد
                        foreach ($elements_order as $element_id) {
                            if (isset($default_order[$element_id])) {
                                $element = $default_order[$element_id];

                                // تحديد ما إذا كان هذا هو عنصر حقول النموذج أو ملخص الطلب
                                $is_fields_element = ($element['id'] === 'fields');
                                $is_summary_element = ($element['id'] === 'summary');
                                $has_children = $is_fields_element || $is_summary_element;
                                $toggle_class = $has_children ? 'form-element-item-with-children' : '';
                                $toggle_icon = $has_children ? '<span class="toggle-fields-icon dashicons dashicons-arrow-down-alt2"></span>' : '';
                                ?>
                                <li class="form-element-item <?php echo $toggle_class; ?>" data-element-id="<?php echo esc_attr($element['id']); ?>">
                                    <div class="element-handle"><span class="dashicons dashicons-menu"></span></div>
                                    <div class="element-icon"><span class="dashicons <?php echo esc_attr($element['icon']); ?>"></span></div>
                                    <div class="element-label"><?php echo esc_html($element['label']); ?></div>
                                    <?php echo $toggle_icon; ?>
                                    <input type="hidden" name="settings[elements_order][]" value="<?php echo esc_attr($element['id']); ?>">
                                </li>

                                <?php if ($is_fields_element): ?>
                                <!-- قسم حقول النموذج كفرع من عنصر حقول النموذج -->
                                <li class="form-fields-container">
                                    <div class="form-fields-list">
                                        <h4>إدارة حقول النموذج</h4>

                                        <!-- تخطيط جديد: الحقول على اليمين والإعدادات على اليسار -->
                                        <div class="form-fields-management-layout">
                                            <!-- إعدادات الحقول على اليسار -->
                                            <div class="fields-settings-sidebar">
                                                <h4>إعدادات حقول النموذج</h4>

                                                <!-- إعدادات حقول النموذج -->
                                                <div class="fields-settings-container">
                                                    <!-- تخطيط الحقول -->
                                                    <div class="form-layout-section tw-mb-2">
                                                        <h5 class="tw-text-xs tw-mb-1">تخطيط الحقول</h5>
                                                        <div class="form-layout-preview">
                                                            <div class="layout-option layout-option-vertical <?php echo ($form_settings['fields_layout'] ?? 'vertical') == 'vertical' ? 'active' : ''; ?>" data-layout="vertical">
                                                                <div class="layout-preview">
                                                                    <div class="layout-icon"><span class="dashicons dashicons-menu-alt"></span></div>
                                                                    <div class="layout-preview-items">
                                                                        <span class="preview-field"></span>
                                                                        <span class="preview-field"></span>
                                                                    </div>
                                                                </div>
                                                                <span class="layout-label">عمودي</span>
                                                                <input type="radio" name="settings[fields_layout]" value="vertical" <?php checked($form_settings['fields_layout'] ?? 'vertical', 'vertical'); ?> class="layout-radio">
                                                            </div>

                                                            <div class="layout-option layout-option-columns-2 <?php echo ($form_settings['fields_layout'] ?? 'vertical') == 'columns-2' ? 'active' : ''; ?>" data-layout="columns-2">
                                                                <div class="layout-preview">
                                                                    <div class="layout-icon"><span class="dashicons dashicons-grid-view"></span></div>
                                                                    <div class="layout-preview-items layout-columns">
                                                                        <span class="preview-field"></span>
                                                                        <span class="preview-field"></span>
                                                                        <span class="preview-field"></span>
                                                                        <span class="preview-field"></span>
                                                                    </div>
                                                                </div>
                                                                <span class="layout-label">أعمدة</span>
                                                                <input type="radio" name="settings[fields_layout]" value="columns-2" <?php checked($form_settings['fields_layout'] ?? 'vertical', 'columns-2'); ?> class="layout-radio">
                                                            </div>

                                                            <div class="layout-option layout-option-columns-desktop <?php echo ($form_settings['fields_layout'] ?? 'vertical') == 'columns-2-desktop' ? 'active' : ''; ?>" data-layout="columns-2-desktop">
                                                                <div class="layout-preview">
                                                                    <div class="layout-icon"><span class="dashicons dashicons-laptop"></span></div>
                                                                    <div class="layout-preview-items layout-responsive">
                                                                        <div class="desktop-view layout-columns">
                                                                            <span class="preview-field"></span>
                                                                            <span class="preview-field"></span>
                                                                        </div>
                                                                        <div class="mobile-view">
                                                                            <span class="preview-field"></span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <span class="layout-label">شاشات كبيرة</span>
                                                                <input type="radio" name="settings[fields_layout]" value="columns-2-desktop" <?php checked($form_settings['fields_layout'] ?? 'vertical', 'columns-2-desktop'); ?> class="layout-radio">
                                                            </div>
                                                        </div>



                                                        <!-- تم إزالة إعدادات المسافات بين الأعمدة لتبسيط الواجهة -->
                                                        <!-- يتم استخدام مسافة متوسطة افتراضياً -->
                                                        <input type="hidden" name="settings[fields_column_gap]" value="medium">
                                                    </div>

                                                    <!-- تنسيق الحقول -->
                                                    <div class="form-fields-styling-section">
                                                        <h5>تنسيق الحقول</h5>
                                                        <div class="settings-field-row">
                                                            <div class="settings-field">
                                                                <label for="fields-border-radius">حواف الحقول</label>
                                                                <input type="range" name="settings[fields_border_radius]" id="fields-border-radius"
                                                                    value="<?php echo esc_attr($form_settings['fields_border_radius']); ?>" min="0" max="20" step="1"
                                                                    oninput="this.nextElementSibling.value = this.value + ' بكسل'">
                                                                <output><?php echo esc_attr($form_settings['fields_border_radius']); ?> بكسل</output>
                                                            </div>
                                                        </div>

                                                        <!-- ارتفاع الحقول -->
                                                        <div class="settings-field full-width tw-mb-3">
                                                            <label class="tw-text-xs tw-mb-1">ارتفاع حقول النموذج</label>
                                                            <div class="radio-button-group field-height-options tw-grid tw-grid-cols-2 tw-gap-1">
                                                                <label class="tw-flex tw-items-center tw-p-1 tw-border tw-border-gray-300 tw-rounded tw-cursor-pointer tw-text-xs">
                                                                    <input type="radio" name="settings[fields_height]" value="small" <?php checked($form_settings['fields_height'] ?? 'medium', 'small'); ?> class="tw-mr-1">
                                                                    <span>منخفض (32px)</span>
                                                                </label>
                                                                <label class="tw-flex tw-items-center tw-p-1 tw-border tw-border-gray-300 tw-rounded tw-cursor-pointer tw-text-xs">
                                                                    <input type="radio" name="settings[fields_height]" value="medium" <?php checked($form_settings['fields_height'] ?? 'medium', 'medium'); ?> class="tw-mr-1">
                                                                    <span>متوسط (38px)</span>
                                                                </label>
                                                                <label class="tw-flex tw-items-center tw-p-1 tw-border tw-border-gray-300 tw-rounded tw-cursor-pointer tw-text-xs">
                                                                    <input type="radio" name="settings[fields_height]" value="large" <?php checked($form_settings['fields_height'] ?? 'medium', 'large'); ?> class="tw-mr-1">
                                                                    <span>مرتفع (46px)</span>
                                                                </label>
                                                                <label class="tw-flex tw-items-center tw-p-1 tw-border tw-border-gray-300 tw-rounded tw-cursor-pointer tw-text-xs">
                                                                    <input type="radio" name="settings[fields_height]" value="xlarge" <?php checked($form_settings['fields_height'] ?? 'medium', 'xlarge'); ?> class="tw-mr-1">
                                                                    <span>كبير (52px)</span>
                                                                </label>
                                                            </div>
                                                            <p class="tw-text-xs tw-text-gray-500 tw-mt-1">تعديل ارتفاع حقول النموذج ومربعات الأيقونات للحصول على تناسق في التصميم</p>
                                                        </div>



                                                    </div>

                                                    <!-- الأيقونات والعناوين -->
                                                    <div class="form-icons-labels-section">
                                                        <h5>الأيقونات والعناوين</h5>
                                                        <div class="settings-field-row">
                                                            <div class="settings-field">
                                                                <label for="fields-icons-display">أيقونات الحقول</label>
                                                                <div class="settings-control">
                                                                    <div class="toggle-switch-container">
                                                                        <label class="switch-toggle">
                                                                            <input type="checkbox" id="fields-icons-toggle" <?php checked(($form_settings['fields_icons_display'] ?? 'show'), 'show'); ?>>
                                                                            <span class="toggle-slider"></span>
                                                                        </label>
                                                                        <span class="toggle-label"><?php echo ($form_settings['fields_icons_display'] ?? 'show') === 'show' ? 'ظاهرة' : 'مخفية'; ?></span>
                                                                        <input type="hidden" name="settings[fields_icons_display]" id="fields-icons-display" value="<?php echo esc_attr($form_settings['fields_icons_display'] ?? 'show'); ?>">
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="settings-field">
                                                                <label for="icons-position">موضع الأيقونات</label>
                                                                <select name="settings[icons_position]" id="icons-position">
                                                                    <option value="right" <?php selected($form_settings['icons_position'] ?? 'right', 'right'); ?>>يمين الحقول</option>
                                                                    <option value="left" <?php selected($form_settings['icons_position'] ?? 'right', 'left'); ?>>يسار الحقول</option>
                                                                </select>
                                                            </div>



                                                            <div class="settings-field">
                                                                <label for="labels-font-weight">خط العناوين</label>
                                                                <select name="settings[labels_font_weight]" id="labels-font-weight">
                                                                    <option value="normal" <?php selected($form_settings['labels_font_weight'] ?? 'bold', 'normal'); ?>>عادي</option>
                                                                    <option value="bold" <?php selected($form_settings['labels_font_weight'] ?? 'bold', 'bold'); ?>>ثخين</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- قائمة الحقول على اليمين -->
                                            <div class="fields-list-main">
                                                <h4>إدارة حقول النموذج</h4>

                                                <div class="form-fields-management">
                                                    <div id="form-fields-container" class="full-width">
                                                        <ul id="form-elements-sortable-list">
                                                        <?php if (empty($form_fields)) : ?>
                                                            <div class="no-fields">
                                                                <p>لا توجد حقول. انقر على "إضافة حقل" لإضافة حقول جديدة.</p>
                                                            </div>
                                                        <?php else : ?>
                                                            <?php foreach ($form_fields as $index => $field) : ?>
                                                        <li class="form-field-item" data-field-id="<?php echo esc_attr($field['id']); ?>" data-field-index="<?php echo $index; ?>">
                                                            <!-- رأس الحقل -->
                                                            <div class="field-header">
                                                                <div class="field-header-main">
                                                                    <span class="handle dashicons dashicons-menu"></span>
                                                                    <div class="field-info">
                                                                        <h3 class="field-title"><?php echo esc_html($field['label']); ?></h3>
                                                                        <div class="field-meta">
                                                                            <span class="field-type"><?php echo esc_html($field['type']); ?></span>
                                                                            <span class="field-status <?php echo $field['required'] ? 'required' : 'optional'; ?>">
                                                                                <?php echo $field['required'] ? 'مطلوب' : 'اختياري'; ?>
                                                                            </span>
                                                                            <span class="field-visibility <?php echo $field['visible'] ? 'visible' : 'hidden'; ?>">
                                                                                <?php echo $field['visible'] ? 'مرئي' : 'مخفي'; ?>
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="field-actions">
                                                                    <button type="button" class="field-toggle-edit" title="تعديل الحقل">
                                                                        <span class="dashicons dashicons-edit"></span>
                                                                    </button>
                                                                    <button type="button" class="field-clone" title="نسخ الحقل">
                                                                        <span class="dashicons dashicons-admin-page"></span>
                                                                    </button>
                                                                    <button type="button" class="field-toggle-visibility" data-visible="<?php echo $field['visible'] ? '1' : '0'; ?>" title="<?php echo $field['visible'] ? 'إخفاء' : 'إظهار'; ?>">
                                                                        <span class="dashicons <?php echo $field['visible'] ? 'dashicons-visibility' : 'dashicons-hidden'; ?>"></span>
                                                                    </button>
                                                                    <?php if (!in_array($field['id'], array('state', 'municipality', 'address'))) : ?>
                                                                        <button type="button" class="field-delete" title="حذف الحقل">
                                                                            <span class="dashicons dashicons-trash"></span>
                                                                        </button>
                                                                    <?php else : ?>
                                                                        <button type="button" class="field-permanent" title="حقل أساسي" disabled>
                                                                            <span class="dashicons dashicons-lock"></span>
                                                                        </button>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>

                                                            <!-- منطقة التعديل المدمجة (مخفية افتراضياً) -->
                                                            <div class="field-edit-panel" style="display: none;">
                                                                <div class="field-edit-content">
                                                                    <?php if (in_array($field['id'], array('state', 'municipality', 'address'))) : ?>
                                                                    <div class="field-edit-notice">
                                                                        <span class="dashicons dashicons-info"></span>
                                                                        <span>هذا حقل أساسي في النظام. يمكنك تعديل جميع إعداداته حسب احتياجاتك.</span>
                                                                    </div>
                                                                    <?php endif; ?>

                                                                    <div class="field-edit-row">
                                                                        <label>عنوان الحقل</label>
                                                                        <input type="text" class="field-edit-label" value="<?php echo esc_attr($field['label']); ?>">
                                                                    </div>

                                                                    <div class="field-edit-row">
                                                                        <label>نوع الحقل</label>
                                                                        <select class="field-edit-type">
                                                                            <option value="text" <?php selected($field['type'], 'text'); ?>>نص قصير</option>
                                                                            <option value="textarea" <?php selected($field['type'], 'textarea'); ?>>نص متعدد الأسطر</option>
                                                                            <option value="email" <?php selected($field['type'], 'email'); ?>>بريد إلكتروني</option>
                                                                            <option value="tel" <?php selected($field['type'], 'tel'); ?>>رقم هاتف</option>
                                                                            <option value="number" <?php selected($field['type'], 'number'); ?>>رقم</option>
                                                                            <option value="select" <?php selected($field['type'], 'select'); ?>>قائمة منسدلة</option>
                                                                            <option value="radio" <?php selected($field['type'], 'radio'); ?>>اختيار واحد</option>
                                                                            <option value="checkbox" <?php selected($field['type'], 'checkbox'); ?>>اختيار متعدد</option>
                                                                        </select>
                                                                    </div>

                                                                    <div class="field-edit-row">
                                                                        <label>النص التوضيحي</label>
                                                                        <input type="text" class="field-edit-placeholder" value="<?php echo esc_attr($field['placeholder'] ?? ''); ?>" placeholder="النص الذي يظهر داخل الحقل">
                                                                    </div>

                                                                    <div class="field-edit-row">
                                                                        <label>القيمة الافتراضية</label>
                                                                        <input type="text" class="field-edit-default" value="<?php echo esc_attr($field['default_value'] ?? ''); ?>">
                                                                    </div>

                                                                    <div class="field-edit-row">
                                                                        <label>وصف الحقل</label>
                                                                        <textarea class="field-edit-description" rows="2" placeholder="وصف يظهر أسفل الحقل"><?php echo esc_textarea($field['description'] ?? ''); ?></textarea>
                                                                    </div>

                                                                    <?php if (in_array($field['type'], array('select', 'radio', 'checkbox'))) : ?>
                                                                    <div class="field-edit-row field-options-row">
                                                                        <label>خيارات القائمة</label>
                                                                        <textarea class="field-edit-options" rows="3" placeholder="أدخل كل خيار في سطر منفصل"><?php echo !empty($field['options']) ? implode("\n", $field['options']) : ''; ?></textarea>
                                                                    </div>
                                                                    <?php endif; ?>

                                                                    <div class="field-edit-row field-toggles">
                                                                        <label class="field-toggle-option">
                                                                            <input type="checkbox" class="field-edit-required" <?php checked($field['required']); ?>>
                                                                            <span>حقل إلزامي</span>
                                                                        </label>
                                                                        <label class="field-toggle-option">
                                                                            <input type="checkbox" class="field-edit-visible" <?php checked($field['visible']); ?>>
                                                                            <span>ظاهر في النموذج</span>
                                                                        </label>
                                                                    </div>

                                                                    <div class="field-edit-actions">
                                                                        <button type="button" class="button field-save-changes">حفظ التغييرات</button>
                                                                        <button type="button" class="button field-cancel-edit">إلغاء</button>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <!-- Hidden field data for storage -->
                                                            <div class="field-hidden-data">
                                                                <input type="hidden" name="fields[<?php echo $index; ?>][id]" value="<?php echo esc_attr($field['id']); ?>">
                                                                <input type="hidden" name="fields[<?php echo $index; ?>][label]" value="<?php echo esc_attr($field['label']); ?>">
                                                                <input type="hidden" name="fields[<?php echo $index; ?>][type]" value="<?php echo esc_attr($field['type']); ?>">
                                                                <input type="hidden" name="fields[<?php echo $index; ?>][required]" value="<?php echo $field['required'] ? '1' : '0'; ?>">
                                                                <input type="hidden" name="fields[<?php echo $index; ?>][visible]" value="<?php echo $field['visible'] ? '1' : '0'; ?>">
                                                                <input type="hidden" name="fields[<?php echo $index; ?>][placeholder]" value="<?php echo esc_attr($field['placeholder'] ?? ''); ?>">
                                                                <input type="hidden" name="fields[<?php echo $index; ?>][default_value]" value="<?php echo esc_attr($field['default_value'] ?? ''); ?>">
                                                                <input type="hidden" name="fields[<?php echo $index; ?>][description]" value="<?php echo esc_attr($field['description'] ?? ''); ?>">
                                                                <?php if (!empty($field['options']) && is_array($field['options'])) : ?>
                                                                    <?php foreach ($field['options'] as $option_index => $option) : ?>
                                                                        <input type="hidden" name="fields[<?php echo $index; ?>][options][]" value="<?php echo esc_attr($option); ?>">
                                                                    <?php endforeach; ?>
                                                                <?php endif; ?>
                                                            </div>
                                                        </li>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                                </ul>
                                            </div>

                                                    <div class="field-buttons">
                                                        <button type="button" id="add-field-button" class="button">
                                                            <span class="dashicons dashicons-plus-alt" style="vertical-align: middle;"></span> إضافة حقل
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <?php endif; ?>

                                <?php if ($is_summary_element): ?>
                                <!-- قسم إعدادات ملخص الطلب كفرع من عنصر ملخص الطلب -->
                                <li class="form-fields-container">
                                    <div class="form-fields-list">
                                        <h4>إعدادات ملخص الطلب</h4>

                                        <div class="summary-settings-container">
                                            <div class="settings-field">
                                                <div class="toggle-switch-container">
                                                    <label class="switch-toggle">
                                                        <input type="checkbox" id="hide-order-summary" name="settings[hide_order_summary]" value="1"
                                                            <?php checked(isset($form_settings['hide_order_summary']) && $form_settings['hide_order_summary'] == 1 ? 1 : 0, 1); ?>>
                                                        <span class="toggle-slider"></span>
                                                    </label>
                                                    <span>إخفاء ملخص الطلب</span>
                                                </div>
                                                <p class="description">عند تفعيل هذا الخيار، سيتم إخفاء ملخص الطلب بالكامل من النموذج</p>
                                            </div>

                                            <div class="settings-field summary-collapse-setting" style="<?php echo isset($form_settings['hide_order_summary']) && $form_settings['hide_order_summary'] == 1 ? 'display: none;' : ''; ?>">
                                                <div class="toggle-switch-container">
                                                    <label class="switch-toggle">
                                                        <input type="checkbox" id="summary-collapsed-default" name="settings[summary_collapsed_default]" value="1"
                                                            <?php checked(isset($form_settings['summary_collapsed_default']) && $form_settings['summary_collapsed_default'] == 1 ? 1 : 0, 1); ?>>
                                                        <span class="toggle-slider"></span>
                                                    </label>
                                                    <span>جعل ملخص الطلب مطوي افتراضياً</span>
                                                </div>
                                                <p class="description">عند تفعيل هذا الخيار، سيظهر ملخص الطلب مطوي عند تحميل الصفحة</p>
                                            </div>

                                            <!-- حقول مخفية للتأكد من إرسال القيم -->
                                            <input type="hidden" name="settings[hide_order_summary_submitted]" value="1">
                                            <input type="hidden" name="settings[summary_collapsed_default_submitted]" value="1">
                                        </div>
                                    </div>
                                </li>
                                <?php endif; ?>

                                <?php
                            }
                        }
                        ?>
                    </ul>
                </div>
            </div>
        </section>

        <!-- قسم الألوان والتصميم -->
        <section id="colors-design" class="settings-section">
            <h2 class="settings-title"><span class="section-icon dashicons dashicons-admin-appearance"></span>الألوان والتصميم</h2>
            <?php include 'tabs/tab-colors.php'; ?>
        </section>

        <!-- قسم زر الإرسال -->
        <section id="submit-button" class="settings-section">
            <h2 class="settings-title"><span class="section-icon dashicons dashicons-button"></span>زر الإرسال</h2>
            <?php include 'tabs/tab-submit-button.php'; ?>
        </section>

        <!-- قسم تخطيط النموذج -->
        <section id="form-layout" class="settings-section">
            <h2 class="settings-title"><span class="section-icon dashicons dashicons-layout"></span>تخطيط النموذج</h2>
            <?php include 'tabs/tab-layout.php'; ?>
        </section>

        <!-- قسم طرق التوصيل -->
        <section id="shipping-methods" class="settings-section">
            <h2 class="settings-title"><span class="section-icon dashicons dashicons-car"></span>طرق التوصيل</h2>
            <?php include 'tabs/tab-shipping.php'; ?>
        </section>

        <!-- قسم طرق الدفع -->
        <section id="payment-methods" class="settings-section">
            <h2 class="settings-title"><span class="section-icon dashicons dashicons-money-alt"></span>طرق الدفع</h2>
            <?php include 'tabs/tab-payment-methods.php'; ?>
        </section>

        <!-- قسم تصدير/استيراد الإعدادات -->
        <section id="import-export" class="settings-section">
            <h2 class="settings-title"><span class="section-icon dashicons dashicons-database-import"></span>تصدير/استيراد الإعدادات <small style="color: #666; font-size: 14px;">(تجريبي)</small></h2>
            <?php include 'tabs/tab-import-export.php'; ?>
        </section>

        <!-- قسم صفحة التوجيه -->
        <section id="redirect-page" class="settings-section">
            <h2 class="settings-title"><span class="section-icon dashicons dashicons-external"></span>صفحة التوجيه</h2>
            <?php include 'tabs/tab-redirect.php'; ?>
        </section>


    </div>
</div>

<!-- زر العودة للأعلى -->
<div class="back-to-top">
    <span class="dashicons dashicons-arrow-up-alt2"></span>
</div>

<style>
/* تنسيق زر حفظ الإعدادات */
.save-settings-container {
    position: sticky;
    bottom: 0;
    background: #fff;
    padding: 15px;
    border-top: 1px solid #e2e4e7;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.05);
    margin-top: auto;
}

.save-form-settings {
    width: 100%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 20px !important;
}

.save-form-settings .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-top: 2px;
}
/* تنسيق الحاوية الرئيسية */
.form-settings-container {
    display: flex;
    gap: 20px;
    margin: 10px 0;
    padding: 15px;
    background: #f0f0f1;
    border-radius: 10px;
}

/* تنسيق القائمة الجانبية */
.settings-sidebar {
    flex: 1;
    max-width: 48%;
    background: #fff;
    border-radius: 8px;
    padding: 15px 0 0 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 32px;
    height: calc(100vh - 80px);
    display: flex;
    flex-direction: column;
}

.settings-navigation {
    margin: 0;
    padding: 0;
    list-style: none;
}

.settings-navigation li {
    margin: 0;
    padding: 0;
}

.settings-navigation a {
    display: block;
    padding: 12px 20px;
    color: #1d2327;
    text-decoration: none;
    border-right: 4px solid transparent;
    transition: all 0.3s ease;
}

.settings-navigation a:hover {
    background: #f6f7f7;
    border-right-color: #cdcdcd;
}

.settings-navigation a.active {
    background: #f0f6fc;
    border-right-color: #2271b1;
    color: #2271b1;
    font-weight: 500;
}

/* تنسيق محتوى الإعدادات */
.settings-content {
    flex: 1;
    max-width: 48%;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.settings-section {
    margin-bottom: 40px;
    padding-bottom: 40px;
    border-bottom: 1px solid #eee;
}

.settings-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.settings-title {
    margin: 0 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #2271b1;
    color: #1d2327;
    font-size: 1.3em;
}

/* تحسين عرض الحقول */
.settings-field {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e2e4e7;
}

.settings-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #1d2327;
}

.settings-field .description {
    margin-top: 5px;
    color: #646970;
    font-size: 13px;
}

/* تحسين أزرار الراديو والتشيك بوكس */
.settings-field input[type="radio"],
.settings-field input[type="checkbox"] {
    margin-right: 8px;
}

/* تحسين حقول النص والقوائم المنسدلة */
.settings-field input[type="text"],
.settings-field select {
    width: 100%;
    max-width: 400px;
}

/* دعم عرض الحقول بعرض كامل */
.settings-field.full-width {
    grid-column: 1 / -1;
}

/* تنسيق التنقل السلس */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 32px;
}

/* تنسيقات قسم ترتيب العناصر */
.form-elements-order-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

/* تنسيقات قسم إعدادات ملخص الطلب */
.summary-settings-container {
    padding: 15px;
    background: #f9f9f9;
    border-radius: 8px;
    margin-top: 10px;
}

.summary-settings-container .settings-field {
    margin-bottom: 15px;
}

.summary-settings-container .settings-field:last-child {
    margin-bottom: 0;
}

/* تحسين استغلال المساحة للشاشات المتوسطة والصغيرة */
@media (max-width: 1400px) {
    .form-settings-container {
        gap: 15px;
        padding: 12px;
    }

    .settings-sidebar {
        padding: 12px 0 0 0;
    }

    .settings-content {
        padding: 15px;
    }
}

@media (max-width: 1200px) {
    .form-settings-container {
        flex-direction: column;
        gap: 20px;
    }

    .settings-sidebar,
    .settings-content {
        max-width: 100%;
        flex: none;
    }

    .settings-sidebar {
        height: auto;
        position: relative;
        order: 2;
    }

    .settings-content {
        order: 1;
    }
}

/* تحسين عرض العناوين */
.fields-list-main h4,
.form-fields-list h4 {
    margin: 0 0 15px 0;
    padding: 0 15px;
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
    border-bottom: 2px solid #f0f0f1;
    padding-bottom: 10px;
}

/* تحسين عرض قائمة الحقول */
.fields-list-main {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.form-fields-management {
    flex: 1;
    overflow-y: auto;
    padding: 0 15px;
}

#form-fields-container {
    height: 100%;
}

#form-elements-sortable-list {
    min-height: 200px;
    padding: 10px 0;
}

/* تحسين عرض عناصر الحقول */
.form-field-item {
    margin-bottom: 8px;
    padding: 10px 12px;
}

.form-field-item:last-child {
    margin-bottom: 0;
}

.form-elements-order-container {
    margin-top: 20px;
    background-color: #f8f8f8;
    border: 1px solid #e2e2e2;
    border-radius: 8px;
    padding: 15px;
}

.form-elements-sortable {
    list-style: none;
    padding: 0;
    margin: 0;
}

.form-element-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px 15px;
    margin-bottom: 10px;
    cursor: move;
    transition: all 0.2s ease;
    position: relative;
}

.form-element-item:hover {
    border-color: #2271b1;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.form-element-item.ui-sortable-helper {
    box-shadow: 0 5px 10px rgba(0,0,0,0.15);
    border-color: #2271b1;
    background-color: #f0f6fc;
}

.element-handle {
    margin-right: 10px;
    color: #999;
    cursor: move;
}

.element-icon {
    margin-right: 10px;
    color: #2271b1;
}

.element-label {
    flex: 1;
    font-weight: 500;
}

/* تنسيقات عنصر حقول النموذج القابل للطي */
.form-element-item-with-children {
    background-color: #f0f6fc;
    border-color: #2271b1;
}

.toggle-fields-icon {
    margin-right: auto;
    color: #2271b1;
    cursor: pointer;
    transition: transform 0.3s ease;
    display: inline-block;
}

.toggle-fields-icon.open {
    transform: rotate(180deg);
}

.form-fields-container {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    margin: -5px 0 15px 0;
    padding: 15px;
    list-style: none;
}

.form-fields-list h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #2271b1;
    font-size: 14px;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 8px;
}

.fields-settings-container {
    margin-top: 20px;
    border-top: 1px solid #e2e8f0;
    padding-top: 15px;
}

.fields-settings-container h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #2271b1;
    font-size: 14px;
}

.fields-settings-container h5 {
    margin-top: 15px;
    margin-bottom: 10px;
    color: #4b5563;
    font-size: 13px;
    border-bottom: 1px dashed #e2e8f0;
    padding-bottom: 5px;
}

.form-layout-section,
.form-padding-section,
.form-fields-styling-section,
.form-icons-labels-section {
    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 5px;
    padding: 12px;
}

.form-layout-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
}

.layout-option {
    width: calc(33.33% - 10px);
    min-width: 120px;
}

.spacing-options {
    display: flex;
    gap: 10px;
}

.spacing-option {
    width: calc(33.33% - 10px);
    min-width: 80px;
}

.form-fields-sortable {
    list-style: none;
    padding: 0;
    margin: 0;
}

.form-field-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.form-field-item:hover {
    border-color: #93c5fd;
    background-color: #f8fafc;
}

.field-item-handle {
    margin-right: 8px;
    color: #94a3b8;
}

.field-item-icon {
    margin-right: 8px;
    color: #2271b1;
}

.field-item-label {
    font-weight: 500;
    flex: 1;
}

.field-item-type {
    color: #94a3b8;
    font-size: 12px;
    margin-right: 8px;
}

.field-item-visibility {
    color: #64748b;
}

.no-fields-message {
    color: #94a3b8;
    font-style: italic;
    text-align: center;
    padding: 15px;
    background-color: #f1f5f9;
    border-radius: 4px;
    border: 1px dashed #cbd5e1;
}

/* تنسيقات قسم إدارة الحقول */
.form-fields-management {
    margin-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 15px;
}

.form-field-row {
    background-color: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    margin-bottom: 4px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.03);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.field-header {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    background-color: #f8fafc;
    border-bottom: none;
}

.field-header h3 {
    margin: 0;
    flex: 1;
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.field-actions {
    display: flex;
    gap: 4px;
}

.field-actions span {
    cursor: pointer;
    color: #64748b;
    transition: color 0.2s ease;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.field-actions span:hover {
    color: #2271b1;
}

.field-details {
    padding: 2px 8px 6px;
    font-size: 11px;
    color: #64748b;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
}

.required-field, .visible-field {
    font-weight: 500;
}

.required-field {
    color: #e11d48;
}

.visible-field {
    color: #0891b2;
}

.hidden-field {
    color: #94a3b8;
}

.optional-field {
    color: #6b7280;
}

.handle {
    cursor: move;
    margin-right: 6px;
    color: #94a3b8;
    font-size: 14px;
}

.field-buttons {
    margin-top: 15px;
    text-align: center;
}

#add-field-button {
    background-color: #2271b1;
    color: white;
    border-color: #2271b1;
    padding: 5px 15px;
    font-weight: 500;
}

#add-field-button:hover {
    background-color: #135e96;
    border-color: #135e96;
}
</style>

<script>
jQuery(document).ready(function($) {
    // تحديث القائمة الجانبية عند التمرير
    $(window).on('scroll', function() {
        $('.settings-section').each(function() {
            if ($(window).scrollTop() >= $(this).offset().top - 100) {
                var id = $(this).attr('id');
                $('.settings-navigation a').removeClass('active');
                $('.settings-navigation a[href="#' + id + '"]').addClass('active');
            }
        });
    });

    // التنقل السلس عند النقر على روابط القائمة
    $('.settings-navigation a').on('click', function(e) {
        e.preventDefault();
        var target = $(this).attr('href');
        $('html, body').animate({
            scrollTop: $(target).offset().top - 50
        }, 500);
    });

    // تفعيل السحب والإفلات لقائمة ترتيب العناصر
    $('#form-elements-order-list').sortable({
        handle: '.element-handle',
        placeholder: 'form-element-item ui-state-highlight',
        items: '> .form-element-item', // فقط العناصر الرئيسية قابلة للسحب
        cancel: '.toggle-fields-icon' // منع السحب عند النقر على أيقونة التبديل
    });

    // تفعيل وظيفة طي وفتح قسم حقول النموذج
    $('.toggle-fields-icon').on('click', function(e) {
        e.stopPropagation(); // منع انتشار الحدث للعنصر الأب

        var $icon = $(this);
        var $fieldsContainer = $icon.closest('.form-element-item').next('.form-fields-container');

        // تبديل حالة العرض
        $fieldsContainer.slideToggle(300, function() {
            // تبديل فئة الأيقونة بعد انتهاء الحركة
            $icon.toggleClass('open', $fieldsContainer.is(':visible'));
        });
    });

    // فتح قسم حقول النموذج وملخص الطلب افتراضيًا عند تحميل الصفحة
    setTimeout(function() {
        $('.form-element-item-with-children').each(function() {
            var $item = $(this);
            var $icon = $item.find('.toggle-fields-icon');
            var $fieldsContainer = $item.next('.form-fields-container');

            // عرض القسم وتحديث الأيقونة
            $fieldsContainer.show();
            $icon.addClass('open');
        });
    }, 100);

    // إدارة خيارات ملخص الطلب
    $('#hide-order-summary').on('change', function() {
        var isHidden = $(this).is(':checked');
        $('.summary-collapse-setting').toggle(!isHidden);
        console.log('تم تغيير حالة إخفاء ملخص الطلب:', isHidden ? 'مخفي' : 'ظاهر');
    });

    $('#summary-collapsed-default').on('change', function() {
        var isCollapsed = $(this).is(':checked');
        console.log('تم تغيير حالة طي ملخص الطلب افتراضياً:', isCollapsed ? 'مطوي' : 'مفتوح');
    });

    // تفعيل السحب والإفلات لقائمة الحقول
    $('#form-elements-sortable-list').sortable({
        handle: '.handle',
        placeholder: 'form-field-row ui-state-highlight',
        update: function() {
            // تحديث ترتيب الحقول
            updateFieldIndices();
        }
    });

    // وظيفة تحديث مؤشرات الحقول
    function updateFieldIndices() {
        $('#form-elements-sortable-list .form-field-row').each(function(index) {
            var $row = $(this);
            $row.find('input[name^="fields["]').each(function() {
                var name = $(this).attr('name');
                var newName = name.replace(/fields\[\d+\]/, 'fields[' + index + ']');
                $(this).attr('name', newName);
            });
        });
    }

    // تفعيل أزرار تحرير الحقول
    $(document).on('click', '.edit-field', function() {
        var fieldId = $(this).data('field-id');
        // هنا يمكن إضافة كود لفتح نافذة تحرير الحقل
        console.log('تحرير الحقل: ' + fieldId);
    });

    // تفعيل أزرار نسخ الحقول
    $(document).on('click', '.clone-field', function() {
        var fieldId = $(this).data('field-id');
        // هنا يمكن إضافة كود لنسخ الحقل
        console.log('نسخ الحقل: ' + fieldId);
    });

    // تفعيل أزرار إظهار/إخفاء الحقول
    $(document).on('click', '.toggle-field', function() {
        var $this = $(this);
        var fieldId = $this.data('field-id');
        var isVisible = $this.data('visible') === 1;

        // تبديل حالة الرؤية
        isVisible = !isVisible;
        $this.data('visible', isVisible ? 1 : 0);

        // تحديث الأيقونة
        $this.removeClass('dashicons-visibility dashicons-hidden')
             .addClass(isVisible ? 'dashicons-visibility' : 'dashicons-hidden')
             .attr('title', isVisible ? 'إخفاء الحقل' : 'إظهار الحقل');

        // تحديث النص والقيمة المخفية
        var $row = $this.closest('.form-field-row');
        $row.find('.visible-field, .hidden-field').text(isVisible ? 'مرئي' : 'مخفي');
        $row.find('input[name$="[visible]"]').val(isVisible ? '1' : '0');

        console.log('تبديل حالة الحقل: ' + fieldId + ' إلى ' + (isVisible ? 'مرئي' : 'مخفي'));
    });

    // تفعيل أزرار حذف الحقول
    $(document).on('click', '.delete-field', function() {
        var fieldId = $(this).data('field-id');
        var $row = $(this).closest('.form-field-row');

        if (confirm('هل أنت متأكد من حذف هذا الحقل؟')) {
            $row.fadeOut(300, function() {
                $(this).remove();
                updateFieldIndices();
            });
        }

        console.log('حذف الحقل: ' + fieldId);
    });

    // تفعيل زر إضافة حقل جديد
    $('#add-field-button').on('click', function() {
        // هنا يمكن إضافة كود لفتح نافذة إضافة حقل جديد
        console.log('إضافة حقل جديد');
    });

    // تفعيل الأقسام القابلة للطي
    $('.collapsible-header').on('click', function() {
        var $section = $(this).closest('.collapsible-section');
        $section.toggleClass('open');

        // تبديل أيقونة السهم
        var $toggle = $(this).find('.collapsible-toggle');
        $toggle.toggleClass('dashicons-arrow-up-alt2 dashicons-arrow-down-alt2');

        // عرض أو إخفاء المحتوى
        $section.find('.collapsible-content').slideToggle(300);
    });

    // مفتاح تبديل أيقونات الحقول
    $('#fields-icons-toggle').on('change', function() {
        var isChecked = $(this).is(':checked');
        $('#fields-icons-display').val(isChecked ? 'show' : 'hide');
        $(this).closest('.toggle-switch-container').find('.toggle-label').text(isChecked ? 'ظاهرة' : 'مخفية');
        console.log('تم تغيير حالة أيقونات الحقول إلى:', isChecked ? 'ظاهرة' : 'مخفية');
    });

    // مراقبة تغيير موضع عنصر الكمية
    $('#quantity_position').on('change', function() {
        var selectedPosition = $(this).val();
        console.log('تم تغيير موضع الكمية إلى:', selectedPosition);

        // تطبيق التغيير فوراً على المعاينة إذا كانت متاحة
        updateQuantityPositionPreview(selectedPosition);
    });

    // دالة تحديث معاينة موضع الكمية
    function updateQuantityPositionPreview(position) {
        // البحث عن النموذج في المعاينة
        var $previewForm = $('.pexlat-form-form');
        var $formActions = $previewForm.find('.pexlat-form-actions');
        var $quantityContainer = $previewForm.find('.quantity-controls-container');

        if ($previewForm.length > 0) {
            // تحديث data attribute للنموذج
            $previewForm.attr('data-quantity-position', position);

            // إزالة الكلاسات السابقة
            $formActions.removeClass('inline-quantity');

            if (position === 'inline') {
                // إضافة كلاس الوضع المضمن
                $formActions.addClass('inline-quantity');

                // إخفاء عنصر الكمية المنفصل
                $quantityContainer.hide();

                // التأكد من ظهور عنصر الكمية بجانب الزر
                var $quantityInActions = $formActions.find('.quantity-controls');
                if ($quantityInActions.length === 0) {
                    // إضافة عنصر الكمية إذا لم يكن موجوداً
                    var quantityHtml = '<div class="quantity-controls">' +
                        '<button type="button" class="quantity-btn minus">-</button>' +
                        '<input type="number" name="quantity" value="1" min="1" class="quantity-input" />' +
                        '<button type="button" class="quantity-btn plus">+</button>' +
                        '</div>';
                    $formActions.prepend(quantityHtml);
                }

                // تحديث ترتيب أزرار السلة
                var $cartButtons = $previewForm.find('.add-to-cart-button').parent();
                if ($cartButtons.length > 0) {
                    $cartButtons.css('order', '12');
                }
            } else {
                // إظهار عنصر الكمية المنفصل
                $quantityContainer.show();

                // إزالة عنصر الكمية من بجانب الزر إذا كان موجوداً
                $formActions.find('.quantity-controls').remove();
            }

            console.log('تم تحديث معاينة موضع الكمية:', position);
        }
    }

    // تطبيق موضع الكمية عند تحميل الصفحة
    $(document).ready(function() {
        var currentPosition = $('#quantity_position').val();
        if (currentPosition) {
            updateQuantityPositionPreview(currentPosition);
        }
    });

    // التأكد من إرسال قيمة موضع الكمية عند حفظ النموذج
    $('form#form-editor-form').on('submit', function() {
        var quantityPosition = $('#quantity_position').val();


        // التأكد من وجود الحقل في النموذج
        if (!$('input[name="settings[quantity_position]"]').length) {
            // إضافة حقل مخفي إذا لم يكن موجوداً
            $(this).append('<input type="hidden" name="settings[quantity_position]" value="' + quantityPosition + '">');

        }
    });


});
</script>