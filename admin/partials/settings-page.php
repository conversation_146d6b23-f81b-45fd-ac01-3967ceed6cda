<?php
/**
 * Settings page template - تصميم محسن
 */
?>

<div class="wrap">
    <h1>إعدادات Pexlat Form</h1>
    <?php settings_errors('pexlat_form_settings'); ?>

    <!-- شريط الشعار -->
    <?php include(plugin_dir_path(dirname(__FILE__)) . 'partials/header-bar.php'); ?>

    <div id="poststuff">
        <div class="postbox bg-white rounded-lg shadow-md overflow-hidden">
            <h2 class="hndle px-4 py-3 border-b border-gray-200 text-lg font-semibold bg-gray-50 flex justify-between items-center cursor-move">
                <span>إعدادات Pexlat Form</span>
                <span class="dashicons dashicons-admin-generic"></span>
            </h2>
            <div class="inside design-settings-container p-0">
                <form method="post" action="">
                    <?php wp_nonce_field('pexlat_form_settings', 'pexlat_form_settings_nonce'); ?>

                    <div class="form-settings-container">
                        <!-- القائمة الجانبية -->
                        <div class="settings-sidebar">
                            <div class="settings-nav-group">
                                <h3 class="settings-nav-group-title">الإعدادات الأساسية</h3>
                                <ul class="settings-navigation">
                                    <li><a href="#general-settings" class="active">
                                        <span class="nav-icon dashicons dashicons-admin-generic"></span>إعدادات عامة</a>
                                    </li>
                                    <li><a href="#cart-settings">
                                        <span class="nav-icon dashicons dashicons-cart"></span>إعدادات السلة <small style="color: #666; font-size: 11px;">(تجريبي)</small></a>
                                    </li>
                                    <li><a href="#language-settings">
                                        <span class="nav-icon dashicons dashicons-translation"></span>إعدادات اللغة</a>
                                    </li>
                                </ul>
                            </div>



                            <div class="settings-nav-group">
                                <h3 class="settings-nav-group-title">إعدادات متقدمة</h3>
                                <ul class="settings-navigation">
                                    <li><a href="#woocommerce">
                                        <span class="nav-icon dashicons dashicons-cart"></span>تكامل ووكومرس</a>
                                    </li>
                                    <li><a href="#uninstall-settings">
                                        <span class="nav-icon dashicons dashicons-trash"></span>إعدادات إلغاء التثبيت</a>
                                    </li>
                                </ul>
                            </div>

                            <div class="settings-nav-group">
                                <h3 class="settings-nav-group-title">معلومات</h3>
                                <ul class="settings-navigation">
                                    <li><a href="#plugin-info">
                                        <span class="nav-icon dashicons dashicons-info"></span>معلومات الإضافة</a>
                                    </li>
                                    <li><a href="#help-support">
                                        <span class="nav-icon dashicons dashicons-editor-help"></span>المساعدة والدعم</a>
                                    </li>
                                </ul>
                            </div>

                            <div class="save-settings-container">
                                <button type="submit" class="button button-primary button-large save-form-settings">
                                    <span class="dashicons dashicons-saved"></span>
                                    حفظ الإعدادات
                                </button>
                            </div>
                        </div>

                        <!-- محتوى الإعدادات -->
                        <div class="settings-content">


                            <!-- قسم الإعدادات العامة -->
                            <section id="general-settings" class="settings-section" style="display: none;">
                                <h2 class="settings-title">
                                    <span class="section-icon dashicons dashicons-admin-generic"></span>إعدادات عامة
                                </h2>

                                <div class="settings-field">
                                    <label for="notification-email">البريد الإلكتروني للإشعارات</label>
                                    <input type="email" id="notification-email" name="notification_email"
                                           value="<?php echo esc_attr($notification_email); ?>" class="regular-text">
                                    <p class="description">سيتم إرسال إشعارات بالطلبات الجديدة إلى هذا البريد</p>
                                </div>

                                <div class="settings-field">
                                    <label for="form_container_style">نمط حاوية النموذج</label>
                                    <select id="form_container_style" name="form_container_style" class="regular-text">
                                        <option value="default" <?php selected(get_option('pexlat_form_container_style', 'default'), 'default'); ?>>افتراضي</option>
                                        <option value="card" <?php selected(get_option('pexlat_form_container_style', 'default'), 'card'); ?>>بطاقة</option>
                                        <option value="flat" <?php selected(get_option('pexlat_form_container_style', 'default'), 'flat'); ?>>مسطح</option>
                                    </select>
                                    <p class="description">اختر نمط عرض حاوية النموذج</p>
                                </div>

                                <div class="settings-field">
                                    <h4>إعدادات الحماية من الطلبات الوهمية</h4>
                                    <p class="description">قم بتخصيص إعدادات الحماية لمنع الطلبات الوهمية والاحتيالية</p>

                                    <!-- التحقق من صحة رقم الهاتف -->
                                    <div class="collapsible-section open">
                                        <div class="collapsible-header">
                                            <h5>التحقق من صحة رقم الهاتف</h5>
                                            <span class="dashicons dashicons-arrow-up-alt2"></span>
                                        </div>
                                        <div class="collapsible-content" style="display: block;">
                                            <div class="settings-group">
                                                <div>
                                                    <label for="phone_validation_enabled">تفعيل التحقق من رقم الهاتف</label>
                                                    <div class="toggle-switch-container">
                                                        <label class="switch-toggle">
                                                            <input type="checkbox" id="phone_validation_enabled" name="phone_validation_enabled" value="1"
                                                                <?php checked(get_option('pexlat_form_phone_validation_enabled', 1), 1); ?>>
                                                            <span class="toggle-slider"></span>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="phone-validation-options" style="<?php echo get_option('pexlat_form_phone_validation_enabled', 1) ? '' : 'display: none;'; ?>">
                                                <div class="settings-field">
                                                    <label for="phone_prefixes">البادئات المسموح بها</label>
                                                    <input type="text" id="phone_prefixes" name="phone_prefixes"
                                                           value="<?php echo esc_attr(get_option('pexlat_form_phone_prefixes', '05,06,07')); ?>" class="regular-text">
                                                    <p class="description">أدخل البادئات المسموح بها مفصولة بفواصل (مثال: 05,06,07)</p>
                                                </div>

                                                <div class="settings-field">
                                                    <label for="phone_length">طول رقم الهاتف</label>
                                                    <input type="number" id="phone_length" name="phone_length" min="8" max="15"
                                                           value="<?php echo esc_attr(get_option('pexlat_form_phone_length', '10')); ?>" class="small-text">
                                                    <p class="description">أدخل العدد الإجمالي للأرقام في رقم الهاتف</p>
                                                </div>

                                                <div class="settings-field">
                                                    <label for="custom_phone_validation">تخصيص التحقق من رقم الهاتف</label>
                                                    <div class="toggle-switch-container">
                                                        <label class="switch-toggle">
                                                            <input type="checkbox" id="custom_phone_validation" name="custom_phone_validation" value="1"
                                                                <?php checked(get_option('pexlat_form_custom_phone_validation', 0), 1); ?>>
                                                            <span class="toggle-slider"></span>
                                                        </label>
                                                    </div>
                                                    <p class="description">تفعيل تخصيص بنية رقم الهاتف</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- منع الطلبات المتكررة -->
                                    <div class="collapsible-section open">
                                        <div class="collapsible-header">
                                            <h5>منع الطلبات المتكررة</h5>
                                            <span class="dashicons dashicons-arrow-up-alt2"></span>
                                        </div>
                                        <div class="collapsible-content" style="display: block;">
                                            <div class="settings-group">
                                                <div>
                                                    <label for="limit_orders_enabled">تفعيل تقييد عدد الطلبات</label>
                                                    <div class="toggle-switch-container">
                                                        <label class="switch-toggle">
                                                            <input type="checkbox" id="limit_orders_enabled" name="limit_orders_enabled" value="1"
                                                                <?php checked(get_option('pexlat_form_limit_orders_enabled', 1), 1); ?>>
                                                            <span class="toggle-slider"></span>
                                                        </label>
                                                    </div>
                                                    <p class="description">عند تفعيل هذا الخيار، سيتم تقييد عدد الطلبات التي يمكن للعميل إرسالها خلال فترة زمنية محددة</p>
                                                </div>
                                            </div>

                                            <div class="limit-orders-options" style="<?php echo get_option('pexlat_form_limit_orders_enabled', 1) ? '' : 'display: none;'; ?>">
                                                <div class="settings-field">
                                                    <label for="max_orders">الحد الأقصى للطلبات</label>
                                                    <input type="number" id="max_orders" name="max_orders" min="1" max="10"
                                                           value="<?php echo esc_attr(get_option('pexlat_form_max_orders', '3')); ?>" class="small-text">
                                                    <p class="description">أقصى عدد من الطلبات المسموح بها خلال الفترة المحددة (الافتراضي: 3)</p>
                                                </div>

                                                <div class="settings-field">
                                                    <label for="time_period">فترة المنع (بالساعات)</label>
                                                    <input type="number" id="time_period" name="time_period" min="1" max="72"
                                                           value="<?php echo esc_attr(get_option('pexlat_form_time_period', '24')); ?>" class="small-text">
                                                    <p class="description">المدة التي يتم خلالها تطبيق الحد الأقصى للطلبات (الافتراضي: 24 ساعة)</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- منع الإكمال التلقائي -->
                                    <div class="collapsible-section open">
                                        <div class="collapsible-header">
                                            <h5>منع الإكمال التلقائي</h5>
                                            <span class="dashicons dashicons-arrow-up-alt2"></span>
                                        </div>
                                        <div class="collapsible-content" style="display: block;">
                                            <div class="settings-group">
                                                <div>
                                                    <label for="disable_autocomplete">تعطيل الإكمال التلقائي للحقول</label>
                                                    <div class="toggle-switch-container">
                                                        <label class="switch-toggle">
                                                            <input type="checkbox" id="disable_autocomplete" name="disable_autocomplete" value="1"
                                                                <?php checked(get_option('pexlat_form_disable_autocomplete', 0), 1); ?>>
                                                            <span class="toggle-slider"></span>
                                                        </label>
                                                    </div>
                                                    <p class="description">تعطيل خاصية الإكمال التلقائي في متصفح المستخدم</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- منع نسخ ولصق النصوص -->
                                    <div class="collapsible-section open">
                                        <div class="collapsible-header">
                                            <h5>منع نسخ ولصق النصوص</h5>
                                            <span class="dashicons dashicons-arrow-up-alt2"></span>
                                        </div>
                                        <div class="collapsible-content" style="display: block;">
                                            <div class="settings-group">
                                                <div>
                                                    <label for="disable_copy_paste">تعطيل النسخ واللصق في الحقول</label>
                                                    <div class="toggle-switch-container">
                                                        <label class="switch-toggle">
                                                            <input type="checkbox" id="disable_copy_paste" name="disable_copy_paste" value="1"
                                                                <?php checked(get_option('pexlat_form_disable_copy_paste', 0), 1); ?>>
                                                            <span class="toggle-slider"></span>
                                                        </label>
                                                    </div>
                                                    <p class="description">منع المستخدمين من نسخ ولصق النصوص في حقول النموذج</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                </div>
                            </section>

                            <!-- قسم إعدادات السلة -->
                            <section id="cart-settings" class="settings-section" style="display: none;">
                                <h2 class="settings-title">
                                    <span class="section-icon dashicons dashicons-cart"></span>إعدادات السلة <small style="color: #666; font-size: 14px;">(تجريبي)</small>
                                </h2>

                                <div class="settings-field">
                                    <h4>إعدادات السلة العامة</h4>
                                    <p class="description">تحكم في إعدادات نظام السلة المخصص في الموقع</p>

                                    <!-- تفعيل نظام السلة -->
                                    <div class="collapsible-section open">
                                        <div class="collapsible-header">
                                            <h5>تفعيل نظام السلة</h5>
                                            <span class="dashicons dashicons-arrow-up-alt2"></span>
                                        </div>
                                        <div class="collapsible-content" style="display: block;">
                                            <div class="settings-group">
                                                <div>
                                                    <label for="cart_system_enabled">تفعيل نظام السلة المخصص</label>
                                                    <div class="toggle-switch-container">
                                                        <label class="switch-toggle">
                                                            <input type="checkbox" id="cart_system_enabled" name="cart_system_enabled" value="1"
                                                                <?php checked(get_option('pexlat_form_cart_system_enabled', 0), 1); ?>>
                                                            <span class="toggle-slider"></span>
                                                        </label>
                                                    </div>
                                                    <p class="description">عند تفعيل هذا الخيار، سيتم تشغيل نظام السلة المخصص في الموقع</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- إعدادات زر أضف إلى السلة -->
                                    <div class="collapsible-section open">
                                        <div class="collapsible-header">
                                            <h5>إعدادات زر "أضف إلى السلة"</h5>
                                            <span class="dashicons dashicons-arrow-up-alt2"></span>
                                        </div>
                                        <div class="collapsible-content" style="display: block;">
                                            <div class="cart-button-options" style="<?php echo get_option('pexlat_form_cart_system_enabled', 0) ? '' : 'display: none;'; ?>">
                                                <div class="settings-field">
                                                    <label for="cart_button_default_text">النص الافتراضي للزر</label>
                                                    <input type="text" id="cart_button_default_text" name="cart_button_default_text"
                                                           value="<?php echo esc_attr(get_option('pexlat_form_cart_button_default_text', 'أضف إلى السلة')); ?>" class="regular-text">
                                                    <p class="description">النص الافتراضي الذي سيظهر على زر "أضف إلى السلة" (يمكن تخصيصه لكل منتج)</p>
                                                </div>

                                                <div class="settings-field">
                                                    <label for="cart_button_default_color">اللون الافتراضي للزر</label>
                                                    <input type="text" id="cart_button_default_color" name="cart_button_default_color" class="color-picker"
                                                           value="<?php echo esc_attr(get_option('pexlat_form_cart_button_default_color', '#28a745')); ?>">
                                                    <p class="description">اللون الافتراضي لزر "أضف إلى السلة" (يمكن تخصيصه لكل منتج)</p>
                                                </div>

                                                <div class="settings-field">
                                                    <label for="cart_icon_enabled">إظهار أيقونة السلة العائمة</label>
                                                    <div class="toggle-switch-container">
                                                        <label class="switch-toggle">
                                                            <input type="checkbox" id="cart_icon_enabled" name="cart_icon_enabled" value="1"
                                                                <?php checked(get_option('pexlat_form_cart_icon_enabled', 1), 1); ?>>
                                                            <span class="toggle-slider"></span>
                                                        </label>
                                                    </div>
                                                    <p class="description">عرض أيقونة السلة العائمة في الموقع</p>
                                                </div>

                                                <div class="settings-field">
                                                    <label for="cart_auto_open">فتح السلة تلقائياً عند الإضافة</label>
                                                    <div class="toggle-switch-container">
                                                        <label class="switch-toggle">
                                                            <input type="checkbox" id="cart_auto_open" name="cart_auto_open" value="1"
                                                                <?php checked(get_option('pexlat_form_cart_auto_open', 1), 1); ?>>
                                                            <span class="toggle-slider"></span>
                                                        </label>
                                                    </div>
                                                    <p class="description">فتح السلة الجانبية تلقائياً عند إضافة منتج جديد</p>
                                                </div>

                                                <div class="settings-field">
                                                    <label for="cart_default_enabled">تفعيل زر السلة افتراضياً للمنتجات الجديدة</label>
                                                    <div class="toggle-switch-container">
                                                        <label class="switch-toggle">
                                                            <input type="checkbox" id="cart_default_enabled" name="cart_default_enabled" value="1"
                                                                <?php checked(get_option('pexlat_form_cart_default_enabled', 1), 1); ?>>
                                                            <span class="toggle-slider"></span>
                                                        </label>
                                                    </div>
                                                    <p class="description">عند تفعيل هذا الخيار، سيتم تفعيل زر "أضف إلى السلة" تلقائياً لجميع المنتجات الجديدة</p>
                                                </div>

                                                <div class="settings-field">
                                                    <label for="cart_button_style">نمط عرض زر السلة</label>
                                                    <select id="cart_button_style" name="cart_button_style" class="regular-text">
                                                        <option value="separate" <?php selected(get_option('pexlat_form_cart_button_style', 'separate'), 'separate'); ?>>زر منفصل (الشكل الحالي)</option>
                                                        <option value="icon_only" <?php selected(get_option('pexlat_form_cart_button_style', 'separate'), 'icon_only'); ?>>أيقونة فقط بجانب زر الطلب</option>
                                                    </select>
                                                    <p class="description">اختر كيف تريد عرض زر السلة في النموذج</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- إعدادات الأيقونة العائمة -->
                                    <div class="collapsible-section open">
                                        <div class="collapsible-header">
                                            <h5>إعدادات الأيقونة العائمة</h5>
                                            <span class="dashicons dashicons-arrow-up-alt2"></span>
                                        </div>
                                        <div class="collapsible-content" style="display: block;">
                                            <div class="cart-floating-options" style="<?php echo get_option('pexlat_form_cart_system_enabled', 0) ? '' : 'display: none;'; ?>">
                                                <div class="settings-field">
                                                    <label for="floating_cart_position">موضع الأيقونة العائمة</label>
                                                    <select id="floating_cart_position" name="floating_cart_position" class="regular-text">
                                                        <option value="top-left" <?php selected(get_option('pexlat_form_floating_cart_position', 'top-left'), 'top-left'); ?>>أعلى اليسار</option>
                                                        <option value="top-right" <?php selected(get_option('pexlat_form_floating_cart_position', 'top-left'), 'top-right'); ?>>أعلى اليمين</option>
                                                        <option value="bottom-left" <?php selected(get_option('pexlat_form_floating_cart_position', 'top-left'), 'bottom-left'); ?>>أسفل اليسار</option>
                                                        <option value="bottom-right" <?php selected(get_option('pexlat_form_floating_cart_position', 'top-left'), 'bottom-right'); ?>>أسفل اليمين</option>
                                                    </select>
                                                    <p class="description">اختر موضع الأيقونة العائمة على الشاشة</p>
                                                </div>

                                                <div class="settings-field">
                                                    <label for="floating_cart_color">لون الأيقونة العائمة</label>
                                                    <input type="color" id="floating_cart_color" name="floating_cart_color"
                                                           value="<?php echo esc_attr(get_option('pexlat_form_floating_cart_color', '#28a745')); ?>" class="color-picker">
                                                    <p class="description">اختر لون خلفية الأيقونة العائمة</p>
                                                </div>

                                                <div class="settings-field">
                                                    <label for="floating_cart_size">حجم الأيقونة العائمة</label>
                                                    <select id="floating_cart_size" name="floating_cart_size" class="regular-text">
                                                        <option value="small" <?php selected(get_option('pexlat_form_floating_cart_size', 'medium'), 'small'); ?>>صغير (50px)</option>
                                                        <option value="medium" <?php selected(get_option('pexlat_form_floating_cart_size', 'medium'), 'medium'); ?>>متوسط (60px)</option>
                                                        <option value="large" <?php selected(get_option('pexlat_form_floating_cart_size', 'medium'), 'large'); ?>>كبير (70px)</option>
                                                    </select>
                                                    <p class="description">اختر حجم الأيقونة العائمة</p>
                                                </div>

                                                <div class="settings-field">
                                                    <label for="floating_cart_shape">شكل الأيقونة العائمة</label>
                                                    <select id="floating_cart_shape" name="floating_cart_shape" class="regular-text">
                                                        <option value="circle" <?php selected(get_option('pexlat_form_floating_cart_shape', 'circle'), 'circle'); ?>>دائري</option>
                                                        <option value="square" <?php selected(get_option('pexlat_form_floating_cart_shape', 'circle'), 'square'); ?>>مربع</option>
                                                        <option value="rounded" <?php selected(get_option('pexlat_form_floating_cart_shape', 'circle'), 'rounded'); ?>>مربع بزوايا مدورة</option>
                                                    </select>
                                                    <p class="description">اختر شكل الأيقونة العائمة</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- إعدادات السلة المتقدمة -->
                                    <div class="collapsible-section open">
                                        <div class="collapsible-header">
                                            <h5>إعدادات متقدمة</h5>
                                            <span class="dashicons dashicons-arrow-up-alt2"></span>
                                        </div>
                                        <div class="collapsible-content" style="display: block;">
                                            <div class="cart-advanced-options" style="<?php echo get_option('pexlat_form_cart_system_enabled', 0) ? '' : 'display: none;'; ?>">
                                                <div class="settings-field">
                                                    <label for="cart_save_data">حفظ بيانات السلة في المتصفح</label>
                                                    <div class="toggle-switch-container">
                                                        <label class="switch-toggle">
                                                            <input type="checkbox" id="cart_save_data" name="cart_save_data" value="1"
                                                                <?php checked(get_option('pexlat_form_cart_save_data', 1), 1); ?>>
                                                            <span class="toggle-slider"></span>
                                                        </label>
                                                    </div>
                                                    <p class="description">حفظ محتويات السلة في المتصفح للاحتفاظ بها عند إعادة تحميل الصفحة</p>
                                                </div>

                                                <div class="settings-field">
                                                    <label for="cart_clear_after_order">مسح السلة بعد إتمام الطلب</label>
                                                    <div class="toggle-switch-container">
                                                        <label class="switch-toggle">
                                                            <input type="checkbox" id="cart_clear_after_order" name="cart_clear_after_order" value="1"
                                                                <?php checked(get_option('pexlat_form_cart_clear_after_order', 1), 1); ?>>
                                                            <span class="toggle-slider"></span>
                                                        </label>
                                                    </div>
                                                    <p class="description">مسح محتويات السلة تلقائياً بعد إتمام الطلب بنجاح</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- قسم إعدادات اللغة -->
                            <section id="language-settings" class="settings-section" style="display: none;">
                                <h2 class="settings-title">
                                    <span class="section-icon dashicons dashicons-translation"></span>إعدادات اللغة
                                </h2>

                                <?php include(plugin_dir_path(dirname(__FILE__)) . 'partials/tabs/tab-language.php'); ?>
                            </section>

                            <!-- قسم تكامل ووكومرس -->
                            <section id="woocommerce" class="settings-section" style="display: none;">
                                <h2 class="settings-title">
                                    <span class="section-icon dashicons dashicons-cart"></span>تكامل ووكومرس
                                </h2>

                                <div class="settings-field">
                                    <p class="description">النموذج سيظهر دائما في مكان وصف المنتج القصير.</p>
                                    <input type="hidden" name="woo_integration_location" value="short_description">
                                </div>

                                <!-- إعدادات الطلبات المتروكة -->
                                <div class="collapsible-section open">
                                    <div class="collapsible-header">
                                        <h5>إعدادات الطلبات المتروكة</h5>
                                        <span class="dashicons dashicons-arrow-up-alt2"></span>
                                    </div>
                                    <div class="collapsible-content" style="display: block;">
                                        <div class="settings-group">
                                            <div>
                                                <label for="save_abandoned_orders">حفظ الطلبات المتروكة</label>
                                                <div class="toggle-switch-container">
                                                    <label class="switch-toggle">
                                                        <input type="checkbox" id="save_abandoned_orders" name="save_abandoned_orders" value="1"
                                                            <?php checked(get_option('pexlat_form_save_abandoned_orders', 1), 1); ?>>
                                                        <span class="toggle-slider"></span>
                                                    </label>
                                                </div>
                                                <p class="description">حفظ الطلبات التي يتركها العملاء دون إتمامها في قائمة طلبات ووكومرس</p>
                                            </div>
                                        </div>

                                        <div class="abandoned-orders-options" style="<?php echo get_option('pexlat_form_save_abandoned_orders', 1) ? '' : 'display: none;'; ?>">
                                            <div class="settings-field">
                                                <label for="abandoned_order_status">حالة الطلب المتروك</label>
                                                <select id="abandoned_order_status" name="abandoned_order_status" class="regular-text">
                                                    <option value="pending" <?php selected(get_option('pexlat_form_abandoned_order_status', 'pending'), 'pending'); ?>>قيد الانتظار</option>
                                                    <option value="on-hold" <?php selected(get_option('pexlat_form_abandoned_order_status', 'pending'), 'on-hold'); ?>>معلق</option>
                                                </select>
                                                <p class="description">اختر الحالة التي سيتم حفظ الطلبات المتروكة بها (الافتراضي: قيد الانتظار)</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </section>

                            <!-- قسم إعدادات إلغاء التثبيت -->
                            <section id="uninstall-settings" class="settings-section" style="display: none;">
                                <h2 class="settings-title">
                                    <span class="section-icon dashicons dashicons-trash"></span>إعدادات إلغاء التثبيت
                                </h2>

                                <div class="settings-field">
                                    <div class="toggle-switch-container">
                                        <label class="switch-toggle">
                                            <input type="checkbox" name="pexlat_form_delete_data" value="1" <?php checked(get_option('pexlat_form_delete_data'), 1); ?>>
                                            <span class="toggle-slider"></span>
                                        </label>
                                        <span>حذف جميع بيانات الإضافة عند إلغاء تثبيتها</span>
                                    </div>
                                    <p class="description">عند تفعيل هذا الخيار، سيتم حذف جميع النماذج والطلبات والإعدادات عند إلغاء تثبيت الإضافة</p>
                                </div>
                            </section>

                            <!-- قسم معلومات الإضافة -->
                            <section id="plugin-info" class="settings-section" style="display: none;">
                                <h2 class="settings-title">
                                    <span class="section-icon dashicons dashicons-info"></span>معلومات الإضافة
                                </h2>

                                <div class="settings-field">
                                    <p>إصدار الإضافة: <strong><?php echo PEXLAT_FORM_VERSION; ?></strong></p>
                                    <p>عدد النماذج: <strong><?php global $wpdb; echo $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}pexlat_form_forms"); ?></strong></p>
                                </div>
                            </section>

                            <!-- قسم المساعدة والدعم -->
                            <section id="help-support" class="settings-section" style="display: none;">
                                <h2 class="settings-title">
                                    <span class="section-icon dashicons dashicons-editor-help"></span>المساعدة والدعم
                                </h2>

                                <!-- قسم مقاطع الفيديو التعليمية -->
                                <div class="settings-field">
                                    <h3 style="margin-bottom: 15px; color: #1d2327; font-size: 16px;">
                                        <span class="dashicons dashicons-video-alt3" style="margin-left: 5px; color: #d63638;"></span>
                                        مقاطع فيديو تعليمية
                                    </h3>
                                    <p style="margin-bottom: 15px; color: #646970;">شاهد مقاطع الفيديو التعليمية لتعلم كيفية استخدام جميع ميزات الإضافة:</p>

                                    <!-- قائمة التشغيل من يوتيوب -->
                                    <div class="youtube-playlist-container" style="margin-bottom: 25px;">
                                        <!-- عرض قائمة التشغيل مع إمكانية التنقل بين الفيديوهات -->
                                        <div style="position: relative; width: 100%; height: 0; padding-bottom: 56.25%;">
                                            <iframe
                                                style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
                                                src="https://www.youtube.com/embed?listType=playlist&list=PLvf1YfvIvrh11LGyhCcQhbubI89U61bHP&showinfo=1&rel=0&modestbranding=1"
                                                title="مقاطع تعليمية لإضافة Pexlat Form"
                                                frameborder="0"
                                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                                allowfullscreen>
                                            </iframe>
                                        </div>

                                        <!-- معلومات إضافية وروابط -->
                                        <div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-right: 4px solid #d63638;">
                                            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 10px;">
                                                <div>
                                                    <p style="margin: 0; font-size: 14px; color: #1d2327; font-weight: 500;">
                                                        <span class="dashicons dashicons-playlist-video" style="margin-left: 5px; color: #d63638;"></span>
                                                        قائمة تشغيل شاملة لشرح جميع ميزات الإضافة
                                                    </p>
                                                    <p style="margin: 5px 0 0 0; font-size: 12px; color: #646970;">
                                                        استخدم أزرار التنقل في المشغل للانتقال بين الفيديوهات
                                                    </p>
                                                </div>
                                                <a href="https://www.youtube.com/playlist?list=PLvf1YfvIvrh11LGyhCcQhbubI89U61bHP" target="_blank"
                                                   style="display: inline-flex; align-items: center; padding: 8px 15px; background: #d63638; color: white; text-decoration: none; border-radius: 5px; font-size: 13px; transition: all 0.2s ease;">
                                                    <span class="dashicons dashicons-external" style="font-size: 14px; margin-left: 5px;"></span>
                                                    فتح في يوتيوب
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- قسم التواصل والدعم -->
                                <div class="settings-field">
                                    <h3 style="margin-bottom: 15px; color: #1d2327; font-size: 16px;">
                                        <span class="dashicons dashicons-email-alt" style="margin-left: 5px; color: #0073aa;"></span>
                                        التواصل والدعم الفني
                                    </h3>
                                    <p style="margin-bottom: 15px; color: #646970;">إذا واجهتك أي مشكلة في استخدام الإضافة أو كانت لديك أي استفسارات، يمكنك التواصل معنا عبر:</p>
                                    <ul style="list-style: none; padding: 0;">
                                        <li style="margin-bottom: 10px; padding: 10px; background: #f6f7f7; border-radius: 5px; border-right: 3px solid #0073aa;">
                                            <span class="dashicons dashicons-email" style="margin-left: 8px; color: #0073aa;"></span>
                                            البريد الإلكتروني: <a href="mailto:<EMAIL>" style="color: #0073aa; text-decoration: none;"><EMAIL></a>
                                        </li>
                                        <li style="margin-bottom: 10px; padding: 10px; background: #f6f7f7; border-radius: 5px; border-right: 3px solid #0073aa;">
                                            <span class="dashicons dashicons-admin-site-alt3" style="margin-left: 8px; color: #0073aa;"></span>
                                            زيارة موقعنا: <a href="https://pexlat.com/support" target="_blank" style="color: #0073aa; text-decoration: none;">pexlat.com/support</a>
                                        </li>
                                    </ul>
                                </div>
                            </section>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>