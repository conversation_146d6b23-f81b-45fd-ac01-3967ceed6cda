/**
 * نظام التعديل المدمج للحقول - Inline Field Editor
 * نظام جديد تماماً لتعديل الحقول داخل الحقل نفسه بدلاً من النافذة المنبثقة
 */

jQuery(document).ready(function($) {
    'use strict';

    // متغيرات عامة
    let currentEditingField = null;
    let formId = $('#form_id').val() || $('input[name="form_id"]').val();

    console.log('تم تحميل نظام التعديل المدمج للحقول');
    console.log('معرف النموذج:', formId);

    // ======= الأحداث الرئيسية =======

    // إضافة حقل جديد
    $(document).on('click', '#add-field-button', function(e) {
        e.preventDefault();
        addNewField();
    });

    // فتح/إغلاق منطقة التعديل
    $(document).on('click', '.field-toggle-edit', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $fieldItem = $(this).closest('.form-field-item');
        const $editPanel = $fieldItem.find('.field-edit-panel');

        if (currentEditingField && currentEditingField[0] !== $fieldItem[0]) {
            // إغلاق الحقل المفتوح حالياً
            closeCurrentEdit();
        }

        if ($editPanel.is(':visible')) {
            // إغلاق التعديل
            closeFieldEdit($fieldItem);
        } else {
            // فتح التعديل
            openFieldEdit($fieldItem);
        }
    });

    // حفظ التغييرات
    $(document).on('click', '.field-save-changes', function(e) {
        e.preventDefault();
        const $fieldItem = $(this).closest('.form-field-item');
        saveFieldChanges($fieldItem);
    });

    // إلغاء التعديل
    $(document).on('click', '.field-cancel-edit', function(e) {
        e.preventDefault();
        const $fieldItem = $(this).closest('.form-field-item');
        closeFieldEdit($fieldItem);
    });

    // تبديل الرؤية السريع
    $(document).on('click', '.field-toggle-visibility', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $fieldItem = $(this).closest('.form-field-item');
        const fieldId = $fieldItem.data('field-id');
        const currentVisible = $(this).data('visible') === 1;
        const newVisible = !currentVisible;

        toggleFieldVisibility($fieldItem, fieldId, newVisible);
    });

    // نسخ الحقل
    $(document).on('click', '.field-clone', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $fieldItem = $(this).closest('.form-field-item');
        cloneField($fieldItem);
    });

    // حذف الحقل
    $(document).on('click', '.field-delete', function(e) {
        e.preventDefault();
        e.stopPropagation();

        if (confirm('هل أنت متأكد من حذف هذا الحقل؟')) {
            const $fieldItem = $(this).closest('.form-field-item');
            deleteField($fieldItem);
        }
    });

    // تغيير نوع الحقل
    $(document).on('change', '.field-edit-type', function() {
        const $fieldItem = $(this).closest('.form-field-item');
        const fieldType = $(this).val();
        updateFieldTypeOptions($fieldItem, fieldType);
    });

    // ======= الوظائف الرئيسية =======

    /**
     * فتح منطقة تعديل الحقل
     */
    function openFieldEdit($fieldItem) {
        const $editPanel = $fieldItem.find('.field-edit-panel');

        // إضافة فئة التعديل
        $fieldItem.addClass('editing');

        // عرض منطقة التعديل مع تأثير انزلاق
        $editPanel.slideDown(300);

        // تحديث المتغير العام
        currentEditingField = $fieldItem;

        // التركيز على أول حقل
        setTimeout(() => {
            $editPanel.find('.field-edit-label').focus();
        }, 350);

        // تحديث خيارات نوع الحقل
        const fieldType = $fieldItem.find('.field-edit-type').val();
        updateFieldTypeOptions($fieldItem, fieldType);

        console.log('تم فتح تعديل الحقل:', $fieldItem.data('field-id'));
    }

    /**
     * إغلاق منطقة تعديل الحقل
     */
    function closeFieldEdit($fieldItem) {
        const $editPanel = $fieldItem.find('.field-edit-panel');

        // إزالة فئة التعديل
        $fieldItem.removeClass('editing');

        // إخفاء منطقة التعديل
        $editPanel.slideUp(300);

        // تنظيف المتغير العام
        if (currentEditingField && currentEditingField[0] === $fieldItem[0]) {
            currentEditingField = null;
        }

        console.log('تم إغلاق تعديل الحقل:', $fieldItem.data('field-id'));
    }

    /**
     * إغلاق التعديل الحالي
     */
    function closeCurrentEdit() {
        if (currentEditingField) {
            closeFieldEdit(currentEditingField);
        }
    }

    /**
     * حفظ تغييرات الحقل
     */
    function saveFieldChanges($fieldItem) {
        const fieldId = $fieldItem.data('field-id');
        const fieldIndex = $fieldItem.data('field-index');

        // جمع البيانات من منطقة التعديل
        const fieldData = {
            id: fieldId,
            label: $fieldItem.find('.field-edit-label').val(),
            type: $fieldItem.find('.field-edit-type').val(),
            placeholder: $fieldItem.find('.field-edit-placeholder').val(),
            default_value: $fieldItem.find('.field-edit-default').val(),
            description: $fieldItem.find('.field-edit-description').val(),
            required: $fieldItem.find('.field-edit-required').is(':checked'),
            visible: $fieldItem.find('.field-edit-visible').is(':checked'),
            options: []
        };

        // جمع الخيارات إن وجدت
        const optionsText = $fieldItem.find('.field-edit-options').val();
        if (optionsText) {
            fieldData.options = optionsText.split('\n')
                .map(option => option.trim())
                .filter(option => option !== '');
        }

        console.log('حفظ بيانات الحقل:', fieldData);

        // تحديث واجهة المستخدم
        updateFieldDisplay($fieldItem, fieldData);

        // تحديث الحقول المخفية
        updateHiddenFields($fieldItem, fieldData, fieldIndex);

        // حفظ في قاعدة البيانات عبر AJAX
        saveFieldToDatabase(fieldData);

        // إغلاق منطقة التعديل
        closeFieldEdit($fieldItem);
    }

    /**
     * تحديث عرض الحقل في الواجهة
     */
    function updateFieldDisplay($fieldItem, fieldData) {
        // تحديث العنوان
        $fieldItem.find('.field-title').text(fieldData.label);

        // تحديث نوع الحقل
        $fieldItem.find('.field-type').text(fieldData.type);

        // تحديث حالة الإلزامية
        const $statusSpan = $fieldItem.find('.field-status');
        $statusSpan.removeClass('required optional')
                  .addClass(fieldData.required ? 'required' : 'optional')
                  .text(fieldData.required ? 'مطلوب' : 'اختياري');

        // تحديث حالة الرؤية
        const $visibilitySpan = $fieldItem.find('.field-visibility');
        $visibilitySpan.removeClass('visible hidden')
                      .addClass(fieldData.visible ? 'visible' : 'hidden')
                      .text(fieldData.visible ? 'مرئي' : 'مخفي');

        // تحديث زر الرؤية
        const $visibilityBtn = $fieldItem.find('.field-toggle-visibility');
        $visibilityBtn.data('visible', fieldData.visible ? 1 : 0)
                     .attr('title', fieldData.visible ? 'إخفاء' : 'إظهار');

        const $visibilityIcon = $visibilityBtn.find('.dashicons');
        $visibilityIcon.removeClass('dashicons-visibility dashicons-hidden')
                      .addClass(fieldData.visible ? 'dashicons-visibility' : 'dashicons-hidden');
    }

    /**
     * تحديث الحقول المخفية
     */
    function updateHiddenFields($fieldItem, fieldData, fieldIndex) {
        const $hiddenData = $fieldItem.find('.field-hidden-data');

        // تحديث الحقول الأساسية
        $hiddenData.find('input[name$="[label]"]').val(fieldData.label);
        $hiddenData.find('input[name$="[type]"]').val(fieldData.type);
        $hiddenData.find('input[name$="[required]"]').val(fieldData.required ? '1' : '0');
        $hiddenData.find('input[name$="[visible]"]').val(fieldData.visible ? '1' : '0');
        $hiddenData.find('input[name$="[placeholder]"]').val(fieldData.placeholder);
        $hiddenData.find('input[name$="[default_value]"]').val(fieldData.default_value);
        $hiddenData.find('input[name$="[description]"]').val(fieldData.description);

        // تحديث الخيارات
        $hiddenData.find('input[name$="[options][]"]').remove();
        if (fieldData.options && fieldData.options.length > 0) {
            fieldData.options.forEach(option => {
                $hiddenData.append(`<input type="hidden" name="fields[${fieldIndex}][options][]" value="${option}">`);
            });
        }
    }

    /**
     * تحديث خيارات نوع الحقل
     */
    function updateFieldTypeOptions($fieldItem, fieldType) {
        const $optionsRow = $fieldItem.find('.field-options-row');

        if (['select', 'radio', 'checkbox'].includes(fieldType)) {
            $optionsRow.addClass('show');
        } else {
            $optionsRow.removeClass('show');
        }
    }

    /**
     * تبديل رؤية الحقل
     */
    function toggleFieldVisibility($fieldItem, fieldId, newVisible) {
        // تحديث واجهة المستخدم
        const $visibilitySpan = $fieldItem.find('.field-visibility');
        $visibilitySpan.removeClass('visible hidden')
                      .addClass(newVisible ? 'visible' : 'hidden')
                      .text(newVisible ? 'مرئي' : 'مخفي');

        const $visibilityBtn = $fieldItem.find('.field-toggle-visibility');
        $visibilityBtn.data('visible', newVisible ? 1 : 0)
                     .attr('title', newVisible ? 'إخفاء' : 'إظهار');

        const $visibilityIcon = $visibilityBtn.find('.dashicons');
        $visibilityIcon.removeClass('dashicons-visibility dashicons-hidden')
                      .addClass(newVisible ? 'dashicons-visibility' : 'dashicons-hidden');

        // تحديث الحقل المخفي
        $fieldItem.find('input[name$="[visible]"]').val(newVisible ? '1' : '0');

        // تحديث checkbox في منطقة التعديل
        $fieldItem.find('.field-edit-visible').prop('checked', newVisible);

        // حفظ في قاعدة البيانات
        saveFieldVisibilityToDatabase(fieldId, newVisible);

        console.log('تم تبديل رؤية الحقل:', fieldId, 'إلى:', newVisible ? 'مرئي' : 'مخفي');
    }

    // ======= وظائف AJAX =======

    /**
     * حفظ الحقل في قاعدة البيانات
     */
    function saveFieldToDatabase(fieldData) {
        if (!formId) {
            console.error('معرف النموذج غير متوفر');
            return;
        }



        $.ajax({
            url: formElrakamiAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_update_field_settings',
                field_id: fieldData.id,
                form_id: formId,
                field_data: JSON.stringify(fieldData),
                nonce: formElrakamiAdmin.nonce
            },
            success: function(response) {

                if (response.success) {
                    showNotification('تم حفظ التغييرات بنجاح', 'success');
                } else {
                    showNotification('خطأ في حفظ التغييرات: ' + (response.data || 'خطأ غير معروف'), 'error');

                }
            },
            error: function(xhr, status, error) {

                showNotification('خطأ في الاتصال بالخادم', 'error');
            }
        });
    }

    /**
     * حفظ رؤية الحقل في قاعدة البيانات
     */
    function saveFieldVisibilityToDatabase(fieldId, visible) {
        if (!formId) {

            return;
        }

        $.ajax({
            url: formElrakamiAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_update_field_visibility',
                field_id: fieldId,
                form_id: formId,
                visible: visible ? 1 : 0,
                nonce: formElrakamiAdmin.nonce
            },
            success: function(response) {

            },
            error: function(xhr, status, error) {

            }
        });
    }

    // ======= وظائف مساعدة =======

    /**
     * عرض إشعار للمستخدم
     */
    function showNotification(message, type = 'info') {
        // إنشاء عنصر الإشعار
        const $notification = $(`
            <div class="inline-field-notification ${type}">
                <span class="dashicons ${type === 'success' ? 'dashicons-yes-alt' : 'dashicons-warning'}"></span>
                ${message}
            </div>
        `);

        // إضافة الإشعار للصفحة
        $('body').append($notification);

        // عرض الإشعار
        $notification.fadeIn(300);

        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    /**
     * نسخ الحقل
     */
    function cloneField($fieldItem) {
        const fieldId = $fieldItem.data('field-id');

        console.log('نسخ الحقل:', fieldId);

        // جمع بيانات الحقل الأصلي
        const originalData = {
            id: generateFieldId(),
            label: $fieldItem.find('.field-edit-label').val() + ' (نسخة)',
            type: $fieldItem.find('.field-edit-type').val(),
            placeholder: $fieldItem.find('.field-edit-placeholder').val(),
            default_value: $fieldItem.find('.field-edit-default').val(),
            description: $fieldItem.find('.field-edit-description').val(),
            required: $fieldItem.find('.field-edit-required').is(':checked'),
            visible: $fieldItem.find('.field-edit-visible').is(':checked'),
            options: []
        };

        // جمع الخيارات إن وجدت
        const optionsText = $fieldItem.find('.field-edit-options').val();
        if (optionsText) {
            originalData.options = optionsText.split('\n')
                .map(option => option.trim())
                .filter(option => option !== '');
        }

        // إنشاء حقل جديد بالبيانات المنسوخة
        const $newField = createNewFieldElement(originalData);

        // إدراج الحقل الجديد بعد الحقل الأصلي
        $fieldItem.after($newField);

        // تحديث فهارس الحقول
        updateAllFieldIndices();

        // فتح الحقل الجديد للتعديل
        setTimeout(() => {
            openFieldEdit($newField);
        }, 100);

        showNotification('تم نسخ الحقل بنجاح', 'success');
    }

    /**
     * حذف الحقل
     */
    function deleteField($fieldItem) {
        const fieldId = $fieldItem.data('field-id');

        console.log('حذف الحقل:', fieldId);

        // حذف من قاعدة البيانات أولاً
        deleteFieldFromDatabase(fieldId);

        // حذف من الواجهة
        $fieldItem.fadeOut(300, function() {
            $(this).remove();
            updateAllFieldIndices();

            // إظهار رسالة "لا توجد حقول" إذا لم تعد هناك حقول
            if ($('#form-elements-sortable-list .form-field-item').length === 0) {
                $('#form-elements-sortable-list').html('<div class="no-fields"><p>لا توجد حقول. انقر على "إضافة حقل" لإضافة حقول جديدة.</p></div>');
            }
        });

        showNotification('تم حذف الحقل بنجاح', 'success');
    }

    // ======= تهيئة النظام =======

    // إضافة CSS للإشعارات
    $('head').append(`
        <style>
        .inline-field-notification {
            position: fixed;
            top: 32px;
            right: 20px;
            background: #fff;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            padding: 12px 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            z-index: 999999;
            display: none;
            font-size: 13px;
            max-width: 300px;
        }

        .inline-field-notification.success {
            border-color: #16a34a;
            background: #f0fdf4;
            color: #16a34a;
        }

        .inline-field-notification.error {
            border-color: #dc2626;
            background: #fef2f2;
            color: #dc2626;
        }

        .inline-field-notification .dashicons {
            margin-left: 8px;
            font-size: 16px;
        }

        .no-fields {
            text-align: center;
            padding: 40px 20px;
            color: #64748b;
            font-style: italic;
        }
        </style>
    `);

    // ======= وظائف إضافية =======

    /**
     * إضافة حقل جديد
     */
    function addNewField() {


        if (!formId || formId === 0 || formId === '0') {

            showNotification('خطأ: معرف النموذج غير متوفر', 'error');
            return;
        }

        // إنشاء بيانات الحقل الجديد
        const newFieldData = {
            id: generateFieldId(),
            label: 'حقل جديد',
            type: 'text',
            placeholder: '',
            default_value: '',
            description: '',
            required: false,
            visible: true,
            options: []
        };

        // إرسال البيانات عبر AJAX لحفظها في قاعدة البيانات
        $.ajax({
            url: formElrakamiAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_add_field',
                form_id: formId,
                field_data: JSON.stringify(newFieldData),
                nonce: formElrakamiAdmin.nonce
            },
            beforeSend: function() {
                $('#add-field-button').prop('disabled', true).text('جاري الإضافة...');
            },
            success: function(response) {

                if (response.success) {
                    // إنشاء عنصر الحقل الجديد
                    const $newField = createNewFieldElement(newFieldData);

                    // إزالة رسالة "لا توجد حقول" إن وجدت
                    $('#form-elements-sortable-list .no-fields').remove();

                    // إضافة الحقل الجديد إلى القائمة
                    $('#form-elements-sortable-list').append($newField);

                    // تحديث فهارس الحقول
                    updateAllFieldIndices();

                    // فتح الحقل الجديد للتعديل مباشرة
                    setTimeout(() => {
                        openFieldEdit($newField);
                    }, 100);

                    showNotification('تم إضافة حقل جديد بنجاح', 'success');
                } else {

                    showNotification('خطأ: ' + (response.data || 'فشل في إضافة الحقل'), 'error');
                }
            },
            error: function(xhr, status, error) {

                showNotification('خطأ في الاتصال بالخادم', 'error');
            },
            complete: function() {
                $('#add-field-button').prop('disabled', false).text('إضافة حقل');
            }
        });
    }

    /**
     * إنشاء معرف فريد للحقل
     */
    function generateFieldId() {
        return 'field_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * إنشاء عنصر حقل جديد
     */
    function createNewFieldElement(fieldData) {
        const fieldIndex = $('#form-elements-sortable-list .form-field-item').length;

        const fieldHtml = `
            <li class="form-field-item" data-field-id="${fieldData.id}" data-field-index="${fieldIndex}">
                <!-- رأس الحقل -->
                <div class="field-header">
                    <div class="field-header-main">
                        <span class="handle dashicons dashicons-menu"></span>
                        <div class="field-info">
                            <h3 class="field-title">${fieldData.label}</h3>
                            <div class="field-meta">
                                <span class="field-type">${fieldData.type}</span>
                                <span class="field-status ${fieldData.required ? 'required' : 'optional'}">
                                    ${fieldData.required ? 'مطلوب' : 'اختياري'}
                                </span>
                                <span class="field-visibility ${fieldData.visible ? 'visible' : 'hidden'}">
                                    ${fieldData.visible ? 'مرئي' : 'مخفي'}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="field-actions">
                        <button type="button" class="field-toggle-edit" title="تعديل الحقل">
                            <span class="dashicons dashicons-edit"></span>
                        </button>
                        <button type="button" class="field-clone" title="نسخ الحقل">
                            <span class="dashicons dashicons-admin-page"></span>
                        </button>
                        <button type="button" class="field-toggle-visibility" data-visible="${fieldData.visible ? '1' : '0'}" title="${fieldData.visible ? 'إخفاء' : 'إظهار'}">
                            <span class="dashicons ${fieldData.visible ? 'dashicons-visibility' : 'dashicons-hidden'}"></span>
                        </button>
                        <button type="button" class="field-delete" title="حذف الحقل">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </div>
                </div>

                <!-- منطقة التعديل المدمجة (مخفية افتراضياً) -->
                <div class="field-edit-panel" style="display: none;">
                    <div class="field-edit-content">
                        <div class="field-edit-row">
                            <label>عنوان الحقل</label>
                            <input type="text" class="field-edit-label" value="${fieldData.label}">
                        </div>

                        <div class="field-edit-row">
                            <label>نوع الحقل</label>
                            <select class="field-edit-type">
                                <option value="text" ${fieldData.type === 'text' ? 'selected' : ''}>نص قصير</option>
                                <option value="textarea" ${fieldData.type === 'textarea' ? 'selected' : ''}>نص متعدد الأسطر</option>
                                <option value="email" ${fieldData.type === 'email' ? 'selected' : ''}>بريد إلكتروني</option>
                                <option value="tel" ${fieldData.type === 'tel' ? 'selected' : ''}>رقم هاتف</option>
                                <option value="number" ${fieldData.type === 'number' ? 'selected' : ''}>رقم</option>
                                <option value="select" ${fieldData.type === 'select' ? 'selected' : ''}>قائمة منسدلة</option>
                                <option value="radio" ${fieldData.type === 'radio' ? 'selected' : ''}>اختيار واحد</option>
                                <option value="checkbox" ${fieldData.type === 'checkbox' ? 'selected' : ''}>اختيار متعدد</option>
                            </select>
                        </div>

                        <div class="field-edit-row">
                            <label>النص التوضيحي</label>
                            <input type="text" class="field-edit-placeholder" value="${fieldData.placeholder}" placeholder="النص الذي يظهر داخل الحقل">
                        </div>

                        <div class="field-edit-row">
                            <label>القيمة الافتراضية</label>
                            <input type="text" class="field-edit-default" value="${fieldData.default_value}">
                        </div>

                        <div class="field-edit-row">
                            <label>وصف الحقل</label>
                            <textarea class="field-edit-description" rows="2" placeholder="وصف يظهر أسفل الحقل">${fieldData.description}</textarea>
                        </div>

                        <div class="field-edit-row field-options-row" style="display: none;">
                            <label>خيارات القائمة</label>
                            <textarea class="field-edit-options" rows="3" placeholder="أدخل كل خيار في سطر منفصل">${fieldData.options ? fieldData.options.join('\n') : ''}</textarea>
                        </div>

                        <div class="field-edit-row field-toggles">
                            <label class="field-toggle-option">
                                <input type="checkbox" class="field-edit-required" ${fieldData.required ? 'checked' : ''}>
                                <span>حقل إلزامي</span>
                            </label>
                            <label class="field-toggle-option">
                                <input type="checkbox" class="field-edit-visible" ${fieldData.visible ? 'checked' : ''}>
                                <span>ظاهر في النموذج</span>
                            </label>
                        </div>

                        <div class="field-edit-actions">
                            <button type="button" class="button field-save-changes">حفظ التغييرات</button>
                            <button type="button" class="button field-cancel-edit">إلغاء</button>
                        </div>
                    </div>
                </div>

                <!-- Hidden field data for storage -->
                <div class="field-hidden-data">
                    <input type="hidden" name="fields[${fieldIndex}][id]" value="${fieldData.id}">
                    <input type="hidden" name="fields[${fieldIndex}][label]" value="${fieldData.label}">
                    <input type="hidden" name="fields[${fieldIndex}][type]" value="${fieldData.type}">
                    <input type="hidden" name="fields[${fieldIndex}][required]" value="${fieldData.required ? '1' : '0'}">
                    <input type="hidden" name="fields[${fieldIndex}][visible]" value="${fieldData.visible ? '1' : '0'}">
                    <input type="hidden" name="fields[${fieldIndex}][placeholder]" value="${fieldData.placeholder}">
                    <input type="hidden" name="fields[${fieldIndex}][default_value]" value="${fieldData.default_value}">
                    <input type="hidden" name="fields[${fieldIndex}][description]" value="${fieldData.description}">
                </div>
            </li>
        `;

        return $(fieldHtml);
    }

    /**
     * تحديث فهارس جميع الحقول
     */
    function updateAllFieldIndices() {
        $('#form-elements-sortable-list .form-field-item').each(function(index) {
            const $fieldItem = $(this);

            // تحديث data-field-index
            $fieldItem.attr('data-field-index', index);

            // تحديث أسماء الحقول المخفية
            $fieldItem.find('input[name^="fields["]').each(function() {
                const name = $(this).attr('name');
                const newName = name.replace(/fields\[\d+\]/, 'fields[' + index + ']');
                $(this).attr('name', newName);
            });
        });
    }

    /**
     * حذف الحقل من قاعدة البيانات
     */
    function deleteFieldFromDatabase(fieldId) {
        if (!formId) {
            console.error('معرف النموذج غير متوفر');
            return;
        }

        $.ajax({
            url: formElrakamiAdmin.ajaxurl,
            type: 'POST',
            data: {
                action: 'pexlat_form_delete_field',
                field_id: fieldId,
                form_id: formId,
                nonce: formElrakamiAdmin.nonce
            },
            success: function(response) {
                console.log('تم حذف الحقل من قاعدة البيانات:', response);
            },
            error: function(xhr, status, error) {
                console.error('خطأ في حذف الحقل من قاعدة البيانات:', error);
            }
        });
    }

    console.log('تم تهيئة نظام التعديل المدمج بنجاح');
});
