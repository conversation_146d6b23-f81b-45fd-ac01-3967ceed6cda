/**
 * مخصص لمحرر زر الإرسال في الإعدادات
 */
jQuery(document).ready(function($) {
    
    // تهيئة تبويبات محرر الزر
    function initButtonEditor() {
        if (!$('.button-editor-container').length) {
            return;
        }
        
        // تحويل اختيارات الزر إلى أزرار تبديل حديثة
        // تم نقل هذه الوظيفة إلى معالج الحدث الموحد أدناه
        
        // ضبط حالة التبديل في البداية
        const gradientEnabled = $('#button-gradient').val() === 'yes';
        $('#button-gradient-toggle').prop('checked', gradientEnabled);
        $('.button-gradient-settings').toggle(gradientEnabled);
        
        // تحديث قيمة الحقل المخفي حسب الحالة الأولية
        $('input[name="settings[button_gradient]"]').val(gradientEnabled ? 'yes' : 'no');
        
        // تبديل بين التبويبات
        $('.button-tabs-nav a').on('click', function(e) {
            e.preventDefault();
            
            // تنشيط الرابط المختار
            $('.button-tabs-nav li').removeClass('active');
            $(this).parent().addClass('active');
            
            // إظهار المحتوى المناسب
            var targetTab = $(this).attr('href');
            $('.button-settings-content').hide();
            $(targetTab).show();
        });
        
        // تحديث خيارات التدرج اللوني
        $('#button-gradient-toggle').on('change', function() {
            const isChecked = $(this).is(':checked');
            console.log("تم تغيير حالة التدرج اللوني:", isChecked ? "مفعل" : "غير مفعل");
            
            // تحديث قيمة الزر المخفي
            $('#button-gradient').val(isChecked ? 'yes' : 'no');
            
            // تحديث القيم المخفية - نستخدم فقط الحقل المخفي بدون محاولة تحديث select غير موجود
            $('input[name="settings[button_gradient]"]').val(isChecked ? 'yes' : 'no');
            
            // تبديل ظهور إعدادات التدرج
            $('.button-gradient-settings').toggle(isChecked);
            $('.gradient-setting').toggle(isChecked);

        });

        // معالج تغيير خيار عرض السعر في الزر
        $('#show-total-price-in-button').on('change', function() {
            const isChecked = $(this).is(':checked');
            console.log("تم تغيير حالة عرض السعر في الزر:", isChecked ? "مفعل" : "غير مفعل");

            // تحديث النص في واجهة المستخدم
            $('.toggle-status').text(isChecked ? 'مفعل' : 'غير مفعل');

            // تحديث معاينة الزر إذا كانت موجودة
            updateButtonPreview();
        });
        
        // تحديث ظهور إعدادات الأيقونة
        $('input[name="settings[button_icon]"]').on('change', function() {
            var iconValue = $('input[name="settings[button_icon]"]:checked').val();
            $('#icon-position-setting').toggle(iconValue !== 'none');
            updateButtonPreview();
        });
        
        // اختيار حجم الزر
        $('.button-size-options input').on('change', function() {
            const selectedSize = $(this).val();
            console.log("تم اختيار حجم الزر:", selectedSize);
            
            // تحديث حقول الإدخال المخفية (إذا كانت موجودة)
            // تغيير من select إلى input لتتوافق مع نوع الحقل الفعلي
            $('input[name="settings[button_size]"][value="' + selectedSize + '"]').prop('checked', true);
            
            updateButtonPreview();
        });
        
        // تغيير خيارات دائرية الحواف
        $('#button-border-radius').on('input', function() {
            const radius = $(this).val();
            console.log("تم تحديث دائرية الحواف:", radius);
            
            // تحديث حقل الإدخال المخفي مباشرة
            $('input[name="settings[button_border_radius]"]').val(radius);
            
            updateButtonPreview();
        });
        
        // النقر على نماذج دائرية الحواف
        $('.radius-preview').on('click', function() {
            var value = $(this).data('value');
            $('#button-border-radius').val(value).trigger('input');
            $(this).siblings().removeClass('active');
            $(this).addClass('active');
        });
        
        // اختيار ألوان مسبقة
        $('.color-preset').on('click', function() {
            var color = $(this).data('color');
            var colorField = $(this).closest('.color-picker-with-presets').find('.color-picker');
            
            // تحديث حقل اللون
            colorField.wpColorPicker('color', color);
            
            // تحديث المعاينة
            setTimeout(updateButtonPreview, 100);
        });
        
        // اختيار اتجاه التدرج
        $('.gradient-direction-options input').on('change', function() {
            const direction = $(this).val();
            console.log("تم اختيار اتجاه التدرج:", direction);
            
            // تحديث الحقل المخفي بطريقة صحيحة لحقول radio
            $('input[name="settings[button_gradient_direction]"][value="' + direction + '"]').prop('checked', true);
            
            updateButtonPreview();
        });
        
        // اختيار الأيقونة
        $('.icon-options input').on('change', function() {
            const icon = $(this).val();
            console.log("تم اختيار الأيقونة:", icon);
            
            // تحديث الحقل المخفي مباشرة - حقول الإدخال المخفية فقط
            // لا تحاول تحديث حقول select غير موجودة
            $('input[name="settings[button_icon]"][value="' + icon + '"]').prop('checked', true);
            
            // إظهار/إخفاء إعدادات موضع الأيقونة
            $('#icon-position-setting').toggle(icon !== 'none');
            
            updateButtonPreview();
        });
        
        // اختيار موضع الأيقونة
        $('.icon-position-options input').on('change', function() {
            const position = $(this).val();
            console.log("تم اختيار موضع الأيقونة:", position);
            
            // تحديث الحقل المخفي مباشرة - استخدام طريقة prop للحقول من نوع radio
            $('input[name="settings[button_icon_position]"][value="' + position + '"]').prop('checked', true);
            
            updateButtonPreview();
        });
        
        // اختيار تأثير التحويم
        $('.effect-options input').on('change', function() {
            const effect = $(this).val();
            console.log("تم اختيار تأثير التحويم:", effect);
            
            // تحديث حقول الإدخال المخفية - نستخدم الطريقة الصحيحة لحقول radio
            $('input[name="settings[button_hover_effect]"][value="' + effect + '"]').prop('checked', true);
            
            updateButtonPreview();
        });
        
        // اختيار التأثير الحركي
        $('.animation-options input').on('change', function() {
            const animation = $(this).val();
            console.log("تم اختيار التأثير الحركي:", animation);
            
            // تحديث حقول الإدخال المخفية - نستخدم الطريقة الصحيحة لحقول radio
            $('input[name="settings[button_animation]"][value="' + animation + '"]').prop('checked', true);
            
            updateButtonPreview();
        });
        
        // تحديث المعاينة عند تغيير اللون
        $('.color-picker').wpColorPicker({
            change: function(event, ui) {
                // Set color value directly to input
                const value = ui.color.toString();
                $(this).val(value);
                
                // للحصول على معرف الحقل
                const fieldId = $(this).attr('id');
                const targetInputName = `input[name="settings[${fieldId}]"]`;
                
                // تحديث حقل الإدخال المخفي إذا كان موجودًا
                if ($(targetInputName).length) {
                    $(targetInputName).val(value);
                }
                
                // تطبيق التحديث على المعاينة
                setTimeout(updateButtonPreview, 100);
            }
        });
        
        // تحديث نص الزر
        $('#button-text').on('input', function() {
            const text = $(this).val();

            
            // تحديث حقل الإدخال المخفي مباشرة
            $('input[name="settings[button_text]"]').val(text);
            
            updateButtonPreview();
        });
        
        // عرض المعاينة الأولية
        updateButtonPreview();


    }
    
    // وظيفة تحديث معاينة الزر
    function updateButtonPreview() {
        var $preview = $('#button-preview-element');
        if (!$preview.length) return;
        
        // تحديث الحقول المخفية لحفظ التغييرات
        $('input[name="settings[button_color]"]').val($('#button-color').val());
        $('input[name="settings[button_text_color]"]').val($('#button-text-color').val());
        $('input[name="settings[button_gradient_color]"]').val($('#button-gradient-color').val());
        $('input[name="settings[button_text]"]').val($('#button-text').val());
        $('input[name="settings[button_border_radius]"]').val($('#button-border-radius').val());
        
        // تحديث الخيارات المختارة
        var selectedSize = $('input[name="settings[button_size]"]:checked').val();
        $('#button-size').val(selectedSize);
        
        var selectedHoverEffect = $('input[name="settings[button_hover_effect]"]:checked').val();
        $('#button-hover-effect').val(selectedHoverEffect);
        
        var selectedAnimation = $('input[name="settings[button_animation]"]:checked').val();
        $('#button-animation').val(selectedAnimation);
        
        var selectedIcon = $('input[name="settings[button_icon]"]:checked').val();
        $('#button-icon').val(selectedIcon);
        
        var selectedIconPosition = $('input[name="settings[button_icon_position]"]:checked').val();
        $('#button-icon-position').val(selectedIconPosition);
        
        // الحصول على القيم الحالية
        var buttonText = $('#button-text').val() || 'اطلب الآن';
        var buttonSize = $('input[name="settings[button_size]"]:checked').val() || 'medium';
        var borderRadius = $('#button-border-radius').val() || '4';
        
        var primaryColor = $('#button-color').val() || '#4CAF50';
        var gradientColor = $('#button-gradient-color').val() || '#38a169';
        var textColor = $('#button-text-color').val() || '#ffffff';
        
        var useGradient = $('#button-gradient').val() === 'yes';
        var gradientDirection = $('input[name="settings[button_gradient_direction]"]:checked').val() || 'to bottom';
        
        var iconType = $('input[name="settings[button_icon]"]:checked').val() || 'none';
        var iconPosition = $('input[name="settings[button_icon_position]"]:checked').val() || 'right';
        
        var hoverEffect = $('input[name="settings[button_hover_effect]"]:checked').val() || 'none';
        var animation = $('input[name="settings[button_animation]"]:checked').val() || 'none';
        
        // حجم الزر
        $preview.removeClass('small-size medium-size large-size').addClass(buttonSize + '-size');
        
        // تحديث النص
        var showTotalPrice = $('#show-total-price-in-button').is(':checked');
        var displayText = buttonText;
        if (showTotalPrice) {
            displayText += ' (150.00 د.ج)'; // سعر تجريبي للمعاينة
        }
        $('.preview-text').text(displayText);
        
        // إعداد الأيقونة
        var iconClass = '';
        switch(iconType) {
            case 'check': iconClass = 'fa-check'; break;
            case 'cart': iconClass = 'fa-shopping-cart'; break;
            case 'arrow': iconClass = 'fa-arrow-right'; break;
            case 'box': iconClass = 'fa-box'; break;
            default: iconClass = '';
        }
        
        // عرض/إخفاء الأيقونات
        $('.preview-icon-left, .preview-icon-right').hide();
        if (iconType !== 'none') {
            $('.preview-icon-' + iconPosition + ' i').attr('class', 'fas ' + iconClass);
            $('.preview-icon-' + iconPosition).show();
        }
        
        // تحديث الألوان والتدرج
        if (useGradient) {
            $preview.css('background', 'linear-gradient(' + gradientDirection + ', ' + primaryColor + ', ' + gradientColor + ')');
        } else {
            $preview.css('background', primaryColor);
        }
        $preview.css('color', textColor);
        
        // تحديث حواف الزر
        $preview.css('border-radius', borderRadius + 'px');
        
        // إزالة جميع تأثيرات التحويم السابقة
        $preview.removeClass('hover-shadow hover-scale hover-glow');
        
        // إضافة تأثير التحويم المحدد
        if (hoverEffect !== 'none') {
            $preview.addClass('hover-' + hoverEffect);
        }
        
        // إزالة جميع التأثيرات الحركية السابقة
        $preview.removeClass('animated-pulse animated-bounce animated-tada animated-shake');
        
        // إضافة التأثير الحركي المحدد
        if (animation !== 'none') {
            $preview.addClass('animated-' + animation);
        }
    }
    
    // تهيئة محرر الزر
    initButtonEditor();
    
});