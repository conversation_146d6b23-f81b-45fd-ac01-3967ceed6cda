/**
 * Admin JavaScript for Form Elrakami
 */
jQuery(document).ready(function($) {

    // Initialize color pickers
    if ($.fn.wpColorPicker) {
        $('.color-picker').wpColorPicker({
            change: function(event, ui) {
                // When a color is changed, store the value directly to the input
                const value = ui.color.toString();
                $(this).val(value);
            }
        });
    }

    // Form editor page
    if ($('#form-editor-form').length) {
        initFormEditor();
    }

    // Forms list page
    if ($('.wp-list-table.forms-table').length) {
        initFormsList();
    }

    // Registrations list page
    if ($('.registrations-table').length) {
        initRegistrationsList();
    }

    /**
     * Initialize form editor functionality
     */
    function initFormEditor() {
        var $fieldsContainer = $('#form-fields-container');
        // Flag to prevent double event handling
        var isProcessing = false;
        // الحصول على معرف النموذج من النموذج
        var formId = $('#form_id').val();

        // =========== إعدادات تخطيط النموذج ===========

        // إعدادات خيارات التخطيط - تم نقلها إلى settings-redesign.js لتجنب التداخل
        // هذا الكود محجوز للتوافق مع الإصدارات القديمة

        // تحديث عرض العمود عند تغيير القيمة
        $('#column-gap').on('input change', function() {
            var gap = $(this).val();
            $('.column-gap-value').text(gap + 'px');
            $('#field-preview-container').attr('data-column-gap', gap);
        });

        // تحديث إعدادات الأعمدة عند تغيير القيمة
        $('#columns-count').on('input change', function() {
            var columns = $(this).val();
            $('.columns-count-value').text(columns);
            $('#field-preview-container').attr('data-columns', columns);
        });

        // تحديث ارتفاع الحقول
        $('#field-height').on('change', function() {
            var height = $(this).val();
            $('#field-preview-container').attr('data-field-height', height);
        });

        // تبديل عرض الأيقونات
        $('#show-icons').on('change', function() {
            var showIcons = $(this).is(':checked');
            $('#field-preview-container').toggleClass('hide-icons', !showIcons);
        });

        // تبديل أسلوب الحدود
        $('#border-style').on('change', function() {
            var borderStyle = $(this).val();
            $('#field-preview-container').attr('data-border-style', borderStyle);
        });

        // زر إضافة حقل جديد
        $('#add-field').on('click', function() {
            // هذه الوظيفة موجودة في field-editor.js
        });

        // تهيئة النموذج عند تحميل الصفحة
        window.initializeFormView = function() {
            // تطبيق التخطيط الحالي على معاينة الحقول
            var layout = $('input[name="settings[layout]"]:checked').val() || 'vertical';
            $('.layout-option[data-layout="' + layout + '"]').addClass('active');
            $('#field-preview-container').attr('data-layout', layout);

            // عرض أو إخفاء إعدادات الأعمدة بناءً على التخطيط
            if (layout === 'vertical') {
                $('.column-settings-container').hide();
            } else {
                $('.column-settings-container').show();
            }

            // تعيين قيم العناصر الأخرى
            var gap = $('#column-gap').val() || 15;
            $('.column-gap-value').text(gap + 'px');
            $('#field-preview-container').attr('data-column-gap', gap);

            var columns = $('#columns-count').val() || 2;
            $('.columns-count-value').text(columns);
            $('#field-preview-container').attr('data-columns', columns);

            var height = $('#field-height').val() || 'medium';
            $('#field-preview-container').attr('data-field-height', height);

            var showIcons = $('#show-icons').is(':checked');
            $('#field-preview-container').toggleClass('hide-icons', !showIcons);

            var borderStyle = $('#border-style').val() || 'standard';
            $('#field-preview-container').attr('data-border-style', borderStyle);
        }

        // تهيئة النظام الموحد
        function initializeUnifiedTheme() {
            // تبديل النظام الموحد
            $('#unified-theme-toggle').on('change', function() {
                var isEnabled = $(this).is(':checked');

                if (isEnabled) {
                    $('.unified-settings').slideDown(300);
                    $('.manual-settings').slideUp(300);
                } else {
                    $('.unified-settings').slideUp(300);
                    $('.manual-settings').slideDown(300);
                }
            });

            // اختيار مجموعة الألوان
            $('.color-scheme-option').on('click', function() {
                $('.color-scheme-option').removeClass('active');
                $(this).addClass('active');
                $(this).find('input[type="radio"]').prop('checked', true);

                // تطبيق المعاينة الفورية (اختياري)
                var scheme = $(this).data('scheme');
                applyColorSchemePreview(scheme);
            });

            // تحديث قيمة شريط الزوايا
            $('#unified-border-radius').on('input', function() {
                var value = $(this).val();
                $(this).next('output').text(value + ' بكسل');
            });
        }

        // معاينة فورية لمجموعة الألوان (اختياري)
        function applyColorSchemePreview(scheme) {
            // يمكن إضافة معاينة فورية هنا
            console.log('تم اختيار مجموعة الألوان: ' + scheme);
        }

        // الانتقال بين التبويبات
        $('.tab-link').on('click', function() {
            var tabId = $(this).attr('href').substring(1);

            // إزالة الفئة النشطة من جميع الأزرار والتبويبات
            $('.tab-link').removeClass('active');
            $('.tab-content').removeClass('active');

            // إضافة الفئة النشطة للزر والتبويب المختارين
            $(this).addClass('active');
            $('#' + tabId).addClass('active');

            return false;
        });

        // تهيئة النموذج عند التحميل
        window.initializeFormView();

        // السماح بسحب وإفلات الحقول لتغيير الترتيب
        $fieldsContainer.sortable({
            handle: '.handle',
            update: function() {
                updateFieldIndices();
            }
        });

        // تحديث فهارس الحقول
        function updateFieldIndices() {
            $fieldsContainer.find('.form-field-row').each(function(index) {
                var $row = $(this);

                // تحديث رقم الترتيب المعروض
                $row.find('.field-order-number').text(index + 1);

                // تحديث أسماء الحقول المخفية
                $row.find('input[name^="fields["]').each(function() {
                    var name = $(this).attr('name');
                    var newName = name.replace(/fields\[\d+\]/, 'fields[' + index + ']');
                    $(this).attr('name', newName);
                });
            });

            // عرض رسالة "لا توجد حقول" إذا لم تكن هناك حقول
            if ($fieldsContainer.find('.form-field-row').length === 0) {
                $fieldsContainer.html('<p class="no-fields">لا توجد حقول في هذا النموذج حتى الآن. انقر على "إضافة حقل" لإضافة حقول.</p>');
            }
        }

        // تحديث الشريط المثبت المصاحب للنموذج
        /**
         * تحديث معاينة وإعدادات الشريط المثبت
         */
        function updateStickyButtonStyle() {
            var showStickyBar = $('#show-sticky-bar-toggle').is(':checked');
            $('#show-sticky-bar').val(showStickyBar ? 'yes' : 'no');

            if (showStickyBar) {
                $('.sticky-bar-settings').slideDown();
            } else {
                $('.sticky-bar-settings').slideUp();
            }

            // تحديث معاينة زر الشريط المثبت
            var buttonColor = $('#sticky-bar-button-color-input').val();
            var textColor = $('#sticky-bar-button-text-color-input').val();
            var borderRadius = $('#sticky-bar-button-border-radius').val();
            var buttonText = $('#sticky-bar-button-text-input').val();
            $('#sticky-bar-button-text').val(buttonText);

            // تحديث الأيقونة
            var buttonIcon = $('#sticky-bar-button-icon').val();
            var $iconPreview = $('.sticky-icon-preview');

            if (buttonIcon === 'none') {
                $iconPreview.hide();
            } else {
                $iconPreview.show();
                var iconClass = 'fa-shopping-cart';

                if (buttonIcon === 'check') {
                    iconClass = 'fa-check';
                } else if (buttonIcon === 'arrow') {
                    iconClass = 'fa-arrow-right';
                } else if (buttonIcon === 'box') {
                    iconClass = 'fa-box';
                }

                $iconPreview.find('i').attr('class', 'fas ' + iconClass);
            }

            // تحديث عرض معلومات المنتج
            var showProduct = $('#sticky-bar-show-product-toggle').is(':checked');
            $('#sticky-bar-show-product').val(showProduct ? 'yes' : 'no');

            if (showProduct) {
                $('#sticky-preview-product').show();
            } else {
                $('#sticky-preview-product').hide();
            }

            // تحديث نص الزر في المعاينة
            $('.sticky-text-preview').text(buttonText);

            // تحديث التأثير الحركي
            var animation = $('#sticky-bar-button-animation').val();
            var $buttonPreview = $('#sticky-button-preview');

            // إزالة كل فئات التأثيرات الحركية
            $buttonPreview.removeClass('animation-pulse animation-bounce animation-tada animation-swing');

            // إضافة الفئة الجديدة إذا كانت غير "none"
            if (animation !== 'none') {
                $buttonPreview.addClass('animation-' + animation);
            }

            // تحديث التدرج اللوني
            var useGradient = $('#sticky-bar-button-gradient-toggle').is(':checked');
            $('#sticky-bar-button-gradient').val(useGradient ? 'yes' : 'no');

            if (useGradient) {
                $('.sticky-gradient-options').slideDown();
                var gradientColor = $('#sticky-bar-button-gradient-color-input').val();
                var gradientDirection = $('#sticky-bar-button-gradient-direction').val();

                // تطبيق التدرج في المعاينة
                $buttonPreview.addClass('gradient');
                $buttonPreview.css({
                    'background-image': 'linear-gradient(' + gradientDirection + ', ' + buttonColor + ', ' + gradientColor + ')',
                    'background-color': 'transparent'
                });
            } else {
                $('.sticky-gradient-options').slideUp();
                $buttonPreview.removeClass('gradient');
                $buttonPreview.css({
                    'background-image': 'none',
                    'background-color': buttonColor
                });
            }

            // تحديث نمط الزر الأساسي
            $buttonPreview.css({
                'color': textColor,
                'border-radius': borderRadius + 'px',
                '--sticky-button-color': buttonColor,
                '--sticky-button-text-color': textColor,
                '--sticky-button-gradient-color': useGradient ? $('#sticky-bar-button-gradient-color-input').val() : '',
                '--sticky-button-gradient-direction': useGradient ? gradientDirection : '',
                '--sticky-button-border-radius': borderRadius + 'px'
            });
        }

        // تفعيل أحداث تغيير إعدادات الشريط المثبت
        $('#show-sticky-bar-toggle').on('change', function() {
            $('#show-sticky-bar').val($(this).is(':checked') ? 'yes' : 'no');
            updateStickyButtonStyle();
        });

        $('#sticky-bar-show-product-toggle').on('change', function() {
            $('#sticky-bar-show-product').val($(this).is(':checked') ? 'yes' : 'no');
            updateStickyButtonStyle();
        });

        $('#sticky-bar-button-gradient-toggle').on('change', function() {
            $('#sticky-bar-button-gradient').val($(this).is(':checked') ? 'yes' : 'no');
            updateStickyButtonStyle();
        });

        // تفعيل أحداث تغيير الخيارات الجديدة للشريط المثبت
        $('#sticky-bar-always-visible-toggle').on('change', function() {
            $('#sticky-bar-always-visible').val($(this).is(':checked') ? 'yes' : 'no');
        });

        $('#sticky-bar-button-submit-toggle').on('change', function() {
            $('#sticky-bar-button-submit').val($(this).is(':checked') ? 'yes' : 'no');
        });

        // ربط أحداث التغيير الأخرى
        $('#sticky-bar-button-color-input, #sticky-bar-button-text-color-input, #sticky-bar-button-border-radius, #sticky-bar-button-text-input, #sticky-bar-button-icon, #sticky-bar-button-animation, #sticky-bar-button-gradient-color-input, #sticky-bar-button-gradient-direction').on('change input', updateStickyButtonStyle);

        // تطبيق نص زر الطلب الرئيسي على زر الشريط عند النقر على زر النسخ
        $('#copy-main-button-text').on('click', function() {
            var mainButtonText = $('#button-text').val() || 'اطلب الآن';
            $('#sticky-bar-button-text-input').val(mainButtonText);
            $('#sticky-bar-button-text').val(mainButtonText);
            updateStickyButtonStyle();
        });

        // تهيئة معاينة إعدادات الشريط المثبت
        updateStickyButtonStyle();
    }

    /**
     * Initialize forms list page functionality
     */
    function initFormsList() {
        // جدولة إعادة تحميل الصفحة بعد عملية ناجحة
        if (window.location.search.indexOf('message=') !== -1) {
            // إزالة معلمة الرسالة من عنوان URL
            setTimeout(function() {
                var url = window.location.href.replace(/&?message=\d+/, '');
                window.history.replaceState({}, document.title, url);
            }, 3000);
        }
    }

    /**
     * Initialize registrations list page functionality
     */
    function initRegistrationsList() {
        // عرض التفاصيل الكاملة عند النقر على "عرض التفاصيل"
        $('.view-details').on('click', function(e) {
            e.preventDefault();

            var registrationId = $(this).data('registration-id');
            var $detailsModal = $('#registration-details-' + registrationId);

            // عرض النافذة المنبثقة
            $detailsModal.fadeIn();

            // إضافة حدث للنقر خارج النافذة لإغلاقها
            $(document).on('click', function(e) {
                if ($(e.target).is($detailsModal)) {
                    $detailsModal.fadeOut();
                }
            });

            // إضافة حدث لزر الإغلاق
            $detailsModal.find('.close').on('click', function() {
                $detailsModal.fadeOut();
            });
        });

        // تفعيل تصدير البيانات
        $('#export-registrations').on('click', function(e) {
            e.preventDefault();

            var form = $('#export-form');
            var data = {
                action: 'pexlat_form_export_registrations',
                nonce: form.data('nonce'),
                format: $('#export-format').val(),
                form_id: $('#export-form-id').val(),
                date_from: $('#date-from').val(),
                date_to: $('#date-to').val()
            };

            // إنشاء نموذج مؤقت وإرساله
            var $tempForm = $('<form></form>').attr({
                method: 'post',
                action: form.attr('action')
            }).css('display', 'none');

            $.each(data, function(key, value) {
                $tempForm.append($('<input/>').attr({
                    type: 'hidden',
                    name: key,
                    value: value
                }));
            });

            $('body').append($tempForm);
            $tempForm.submit();

            // إزالة النموذج المؤقت بعد الإرسال
            setTimeout(function() {
                $tempForm.remove();
            }, 500);
        });
    }

    // إعدادات السلة - إظهار/إخفاء الخيارات حسب حالة التفعيل
    $('#cart_system_enabled').on('change', function() {
        if ($(this).is(':checked')) {
            $('.cart-button-options, .cart-advanced-options').slideDown(300);
        } else {
            $('.cart-button-options, .cart-advanced-options').slideUp(300);
        }
    });

    // تهيئة حالة إعدادات السلة عند تحميل الصفحة
    if (!$('#cart_system_enabled').is(':checked')) {
        $('.cart-button-options, .cart-advanced-options').hide();
    }

    // إضافة تأثيرات بصرية لإعدادات السلة
    $('#cart-settings .collapsible-header').on('click', function() {
        var $section = $(this).closest('.collapsible-section');
        var $content = $section.find('.collapsible-content');
        var $icon = $(this).find('.dashicons');

        if ($section.hasClass('open')) {
            $content.slideUp(300);
            $section.removeClass('open');
            $icon.removeClass('dashicons-arrow-up-alt2').addClass('dashicons-arrow-down-alt2');
        } else {
            $content.slideDown(300);
            $section.addClass('open');
            $icon.removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-up-alt2');
        }
    });

    // تحديث معاينة لون زر السلة
    $('#cart_button_default_color').on('change', function() {
        var color = $(this).val();
        $('.nav-icon.dashicons-cart').css('color', color);
    });

    // تهيئة النموذج عند تحميل الصفحة
    if (typeof window.initializeFormView === 'function') {
        window.initializeFormView();
    }

    // تهيئة النظام الموحد
    initializeUnifiedTheme();
});