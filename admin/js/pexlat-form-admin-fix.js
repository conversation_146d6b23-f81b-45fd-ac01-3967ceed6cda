/**
 * Fix for JavaScript errors in Form Elrakami admin
 */
jQuery(document).ready(function($) {
    'use strict';



    // Fix for WhatsApp button status
    if ($('#enable-whatsapp-button').length) {
        var whatsappEnabled = $('#enable-whatsapp-button').is(':checked');

    }

    // Fix for show total price in button status
    if ($('#show-total-price-in-button').length) {
        var showTotalPriceEnabled = $('#show-total-price-in-button').is(':checked');


        // إضافة معالج للتغيير
        $('#show-total-price-in-button').on('change', function() {
            var isChecked = $(this).is(':checked');

        });
    }



    // Fix for quantity element status
    if ($('#quantity-controls-toggle').length) {
        var quantityStatus = $('#quantity-controls-toggle').is(':checked') ? 'show' : 'hide';

    }

    // مراقبة تغيير إعداد موضع الكمية
    $('#quantity_position').on('change', function() {
        var selectedPosition = $(this).val();

    });

    // التحقق من قيمة موضع الكمية عند التحميل
    if ($('#quantity_position').length) {
        var currentPosition = $('#quantity_position').val();

    }

    // إضافة مراقب لإرسال النموذج
    $('form#form-editor-form').on('submit', function() {


        // التحقق من قيمة موضع الكمية قبل الإرسال
        var quantityPosition = $('#quantity_position').val();
        var showQuantityControls = $('#show_quantity_controls').val();



        // جمع بيانات النموذج
        var formData = $(this).serializeArray();

        // البحث عن إعدادات الكمية في بيانات النموذج
        var quantitySettings = formData.filter(function(item) {
            return item.name.includes('quantity');
        });



        // التحقق من إعداد عرض السعر في الزر
        var showTotalPriceInButton = $('#show-total-price-in-button').is(':checked');


        // البحث عن إعدادات الزر في بيانات النموذج
        var buttonSettings = formData.filter(function(item) {
            return item.name.includes('button') || item.name.includes('show_total_price');
        });


        // البحث عن إعدادات ملخص الطلب في بيانات النموذج
        var summarySettings = formData.filter(function(item) {
            return item.name.includes('summary') || item.name.includes('hide_order_summary');
        });

    });
});
