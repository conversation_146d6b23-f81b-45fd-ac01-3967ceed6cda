/**
 * JavaScript لتحسين تجربة المستخدم في واجهة إعدادات النموذج
 */
jQuery(document).ready(function($) {
    // --- التنقل في القائمة الجانبية ---
    const navLinks = $('.settings-navigation a');
    const sections = $('.settings-section');

    // إضافة معرفات للأقسام
    sections.each(function() {
        const id = $(this).attr('id');
        $(this).attr('data-section-id', id);
    });

    // إخفاء جميع الأقسام ما عدا القسم الأول
    sections.hide();

    // التحقق من وجود hash في URL أولاً
    var hash = window.location.hash;
    if (hash && $(hash).length) {
        $(hash).show();
        $('.settings-navigation a[href="' + hash + '"]').addClass('active');
    } else {
        // إظهار القسم الافتراضي حسب الصفحة
        if ($('#form-fields').length) {
            // صفحة إعدادات النموذج - إظهار النموذج والحقول
            $('#form-fields').show();
            $('a[href="#form-fields"]').addClass('active');
        } else if ($('#general-settings').length) {
            // صفحة الإعدادات العامة - إظهار الإعدادات العامة
            $('#general-settings').show();
            $('a[href="#general-settings"]').addClass('active');
        }
    }

    // معالجة النقر على روابط التنقل
    navLinks.on('click', function(e) {
        e.preventDefault();

        // إزالة التحديد من جميع الروابط
        navLinks.removeClass('active');

        // إضافة التحديد للرابط الحالي
        $(this).addClass('active');

        // الحصول على القسم المستهدف
        const targetId = $(this).attr('href');
        const $targetSection = $(targetId);

        // إخفاء جميع الأقسام وإظهار القسم المستهدف فقط
        sections.hide();
        $targetSection.show();

        // تحديث عنوان URL
        history.pushState(null, null, targetId);
    });

    // --- الأقسام القابلة للطي ---
    $('.collapsible-header').on('click', function() {
        const section = $(this).parent();
        section.toggleClass('open');

        // تبديل حالة العرض للمحتوى
        const content = section.find('.collapsible-content');
        content.slideToggle(300);

        // تبديل أيقونة السهم
        const icon = $(this).find('.dashicons');
        if (section.hasClass('open')) {
            icon.removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-up-alt2');
        } else {
            icon.removeClass('dashicons-arrow-up-alt2').addClass('dashicons-arrow-down-alt2');
        }

        // حفظ حالة القسم في localStorage
        const sectionId = section.attr('id');
        if (sectionId) {
            localStorage.setItem('section_' + sectionId, section.hasClass('open') ? 'open' : 'closed');
        }
    });

    // استعادة حالة الأقسام من localStorage
    $('.collapsible-section').each(function() {
        const sectionId = $(this).attr('id');

        // إذا كان القسم ضمن إعدادات الحماية من الطلبات الوهمية، اجعله مفتوحًا دائمًا
        if ($(this).closest('.settings-field').find('h4:contains("إعدادات الحماية من الطلبات الوهمية")').length > 0) {
            $(this).addClass('open');
            $(this).find('.collapsible-content').show();
            $(this).find('.collapsible-header .dashicons').removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-up-alt2');
        }
        // بالنسبة للأقسام الأخرى، استعد حالتها من localStorage
        else if (sectionId) {
            const state = localStorage.getItem('section_' + sectionId);
            if (state === 'open' || $(this).hasClass('open')) {
                $(this).addClass('open');
                $(this).find('.collapsible-content').show();
                $(this).find('.collapsible-header .dashicons').removeClass('dashicons-arrow-down-alt2').addClass('dashicons-arrow-up-alt2');
            }
        }
    });

    // --- إعدادات السلة ---
    // تفعيل نظام السلة
    $('#cart_system_enabled').on('change', function() {
        if ($(this).is(':checked')) {
            $('.cart-button-options').slideDown(300);
            $('.cart-floating-options').slideDown(300);
            $('.cart-advanced-options').slideDown(300);
        } else {
            $('.cart-button-options').slideUp(300);
            $('.cart-floating-options').slideUp(300);
            $('.cart-advanced-options').slideUp(300);
        }
    });

    // --- إعدادات الحماية من الطلبات الوهمية ---
    // التحقق من صحة رقم الهاتف
    $('#phone_validation_enabled').on('change', function() {
        if ($(this).is(':checked')) {
            $('.phone-validation-options').slideDown(300);
        } else {
            $('.phone-validation-options').slideUp(300);
        }
    });

    // تخصيص التحقق من رقم الهاتف
    $('#custom_phone_validation').on('change', function() {
        if ($(this).is(':checked')) {
            // يمكن إضافة سلوك إضافي هنا إذا لزم الأمر
            console.log('تم تفعيل تخصيص التحقق من رقم الهاتف');
        } else {
            console.log('تم تعطيل تخصيص التحقق من رقم الهاتف');
        }
    });

    // منع الطلبات المتكررة
    $('#limit_orders_enabled').on('change', function() {
        if ($(this).is(':checked')) {
            $('.limit-orders-options').slideDown(300);
        } else {
            $('.limit-orders-options').slideUp(300);
        }
    });

    // إعدادات الطلبات المتروكة
    $('#save_abandoned_orders').on('change', function() {
        if ($(this).is(':checked')) {
            $('.abandoned-orders-options').slideDown(300);
        } else {
            $('.abandoned-orders-options').slideUp(300);
        }
    });

    // --- إدارة فتح/إغلاق حقول النموذج ---
    $('.toggle-fields-icon').on('click', function(e) {
        e.stopPropagation(); // منع انتشار الحدث للعنصر الأب

        const $icon = $(this);
        const $fieldsContainer = $icon.closest('.form-element-item').next('.form-fields-container');

        // تبديل حالة العرض
        $fieldsContainer.slideToggle(300, function() {
            // تبديل فئة الأيقونة بعد انتهاء الحركة
            $icon.toggleClass('open', $fieldsContainer.is(':visible'));
        });
    });

    // فتح قسم حقول النموذج افتراضيًا عند تحميل الصفحة
    setTimeout(function() {
        $('.form-element-item-with-children').each(function() {
            const $item = $(this);
            const $icon = $item.find('.toggle-fields-icon');
            const $fieldsContainer = $item.next('.form-fields-container');

            // عرض القسم وتحديث الأيقونة
            $fieldsContainer.show();
            $icon.addClass('open');
        });
    }, 100);

    // --- إدارة خيارات التخطيط والتباعد ---
    $(document).on('click', '.layout-option', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // إزالة التحديد من جميع خيارات التخطيط
        $('.layout-option').removeClass('active border-indigo-600 shadow-outline-indigo');

        // إضافة التحديد لهذا الخيار فقط
        $(this).addClass('active border-indigo-600 shadow-outline-indigo');

        // تحديد زر الراديو المقابل وإلغاء تحديد الباقي
        $('.layout-option input[type="radio"]').prop('checked', false);
        $(this).find('input[type="radio"]').prop('checked', true);

        // إظهار/إخفاء إعدادات الأعمدة بناءً على تخطيط الحقول
        const selectedLayout = $(this).data('layout');
        if (selectedLayout && selectedLayout.includes('columns')) {
            $('.column-settings-container').slideDown(300);
        } else {
            $('.column-settings-container').slideUp(300);
        }

        console.log('تم اختيار تخطيط:', selectedLayout);
    });

    $(document).on('click', '.spacing-option', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // إزالة التحديد من جميع خيارات المسافات
        $('.spacing-option').removeClass('active border-indigo-600 shadow-outline-indigo');

        // إضافة التحديد لهذا الخيار فقط
        $(this).addClass('active border-indigo-600 shadow-outline-indigo');

        // تحديد زر الراديو المقابل وإلغاء تحديد الباقي
        $('.spacing-option input[type="radio"]').prop('checked', false);
        $(this).find('input[type="radio"]').prop('checked', true);

        console.log('تم اختيار مسافة:', $(this).data('gap'));
    });

    // تحقق من الحالة الأولية عند التحميل
    function initializeLayoutOptions() {
        // إزالة جميع الفئات النشطة أولاً
        $('.layout-option').removeClass('active border-indigo-600 shadow-outline-indigo');

        // البحث عن الخيار المحدد
        const checkedRadio = $('.layout-option input[type="radio"]:checked');
        if (checkedRadio.length) {
            const activeOption = checkedRadio.closest('.layout-option');
            activeOption.addClass('active border-indigo-600 shadow-outline-indigo');

            // إظهار/إخفاء إعدادات الأعمدة
            const selectedLayout = activeOption.data('layout');
            if (selectedLayout && selectedLayout.includes('columns')) {
                $('.column-settings-container').show();
            } else {
                $('.column-settings-container').hide();
            }

            console.log('تم تهيئة التخطيط:', selectedLayout);
        } else {
            // إذا لم يكن هناك خيار محدد، اختر العمودي كافتراضي
            const defaultOption = $('.layout-option[data-layout="vertical"]');
            if (defaultOption.length) {
                defaultOption.addClass('active border-indigo-600 shadow-outline-indigo');
                defaultOption.find('input[type="radio"]').prop('checked', true);
                $('.column-settings-container').hide();
                console.log('تم تعيين التخطيط الافتراضي: عمودي');
            }
        }
    }

    // تهيئة الخيارات عند تحميل الصفحة
    initializeLayoutOptions();

    const initialSpacing = $('.spacing-option input[type="radio"]:checked').closest('.spacing-option');
    if (initialSpacing.length) {
        initialSpacing.addClass('active border-indigo-600 shadow-outline-indigo');
    }

    // --- زر العودة للأعلى ---
    const backToTop = $('<div class="back-to-top"><span class="dashicons dashicons-arrow-up-alt2"></span></div>');
    $('body').append(backToTop);

    $(window).on('scroll', function() {
        if ($(this).scrollTop() > 300) {
            backToTop.addClass('visible');
        } else {
            backToTop.removeClass('visible');
        }
    });

    backToTop.on('click', function() {
        $('html, body').animate({ scrollTop: 0 }, 500);
    });

    // --- تفعيل السحب والإفلات ---
    if ($.fn.sortable) {
        $('#form-elements-sortable-list, #form-elements-order-list').sortable({
            handle: '.handle, .element-handle',
            placeholder: 'ui-sortable-placeholder',
            axis: 'y',
            opacity: 0.7,
            cursor: 'grabbing',
            update: function(event, ui) {
                // تحديث ترتيب العناصر وتحديث الحقول المخفية


                // تحديث الحقول المخفية لترتيب العناصر
                if ($(this).attr('id') === 'form-elements-order-list') {
                    // إعادة ترقيم الحقول المخفية
                    $(this).find('input[name="settings[elements_order][]"]').remove();

                    $(this).find('.form-element-item').each(function(index) {
                        var elementId = $(this).data('element-id');
                        $(this).append('<input type="hidden" name="settings[elements_order][]" value="' + elementId + '">');
                    });

                    // طباعة الترتيب الجديد للتصحيح
                    var newOrder = [];
                    $(this).find('.form-element-item').each(function() {
                        newOrder.push($(this).data('element-id'));
                    });
                    console.log('الترتيب الجديد للعناصر:', newOrder);
                }
            }
        });
    }

    // --- تحسين تجربة المستخدم للحقول ---
    $('.field-actions .edit-field').on('click', function() {
        const fieldId = $(this).data('field-id');
        // يمكن إضافة كود هنا لفتح نافذة تعديل الحقل
        console.log('تعديل الحقل: ' + fieldId);
    });

    $('.field-actions .clone-field').on('click', function() {
        const fieldId = $(this).data('field-id');
        // يمكن إضافة كود هنا لنسخ الحقل
        console.log('نسخ الحقل: ' + fieldId);
    });

    $('.field-actions .toggle-field').on('click', function() {
        const fieldId = $(this).data('field-id');
        const isVisible = $(this).data('visible') === 1;

        // تبديل حالة الرؤية
        $(this).data('visible', isVisible ? 0 : 1);

        // تحديث الأيقونة والنص
        if (isVisible) {
            $(this).removeClass('dashicons-visibility').addClass('dashicons-hidden');
            $(this).closest('.form-field-row').find('.visible-field').removeClass('visible-field').addClass('hidden-field').text('مخفي');
        } else {
            $(this).removeClass('dashicons-hidden').addClass('dashicons-visibility');
            $(this).closest('.form-field-row').find('.hidden-field').removeClass('hidden-field').addClass('visible-field').text('مرئي');
        }


    });

    $('.field-actions .delete-field').on('click', function() {
        const fieldId = $(this).data('field-id');

        if (confirm('هل أنت متأكد من حذف هذا الحقل؟')) {
            // يمكن إضافة كود هنا لحذف الحقل
            $(this).closest('.form-field-row').fadeOut(300, function() {
                $(this).remove();
            });

        }
    });

    $('#add-field-button').on('click', function() {
        // يمكن إضافة كود هنا لفتح نافذة إضافة حقل جديد

    });

    // تفعيل القسم النشط عند تحميل الصفحة
    function setActiveSection() {
        var hash = window.location.hash;
        if (hash && $(hash).length) {
            // إخفاء جميع الأقسام
            sections.hide();

            // إظهار القسم المستهدف فقط
            $(hash).show();

            // تنشيط الرابط المناسب
            navLinks.removeClass('active');
            $('.settings-navigation a[href="' + hash + '"]').addClass('active');
        } else {
            // إذا لم يكن هناك هاش، إظهار القسم الافتراضي حسب الصفحة
            sections.hide();
            if ($('#form-fields').length) {
                $('#form-fields').show();
                navLinks.removeClass('active');
                $('a[href="#form-fields"]').addClass('active');
            } else if ($('#general-settings').length) {
                $('#general-settings').show();
                navLinks.removeClass('active');
                $('a[href="#general-settings"]').addClass('active');
            }
        }
    }

    // تحديث القسم النشط عند تغيير عنوان URL
    $(window).on('hashchange', function() {
        setActiveSection();
    });

    // تنفيذ عند تحميل الصفحة
    setActiveSection();

    // التأكد من إظهار القسم الأول عند تحميل الصفحة (إصلاح إضافي)
    $(window).on('load', function() {
        if (!window.location.hash) {
            sections.hide();
            if ($('#form-fields').length) {
                $('#form-fields').show();
                navLinks.removeClass('active');
                $('a[href="#form-fields"]').addClass('active');
            } else if ($('#general-settings').length) {
                $('#general-settings').show();
                navLinks.removeClass('active');
                $('a[href="#general-settings"]').addClass('active');
            }
        }
    });

    // --- تفعيل منتقي الألوان ---
    if ($.fn.wpColorPicker) {
        $('.color-picker').wpColorPicker({
            change: function(event, ui) {
                // تحديث المعاينة عند تغيير اللون
                updateButtonPreview();
                updateShippingPreview();
            }
        });


    }

    // --- تفعيل خيارات ارتفاع الحقول ---
    $('.field-height-options input[type="radio"]').on('change', function() {
        $('.field-height-options label').removeClass('selected');
        $(this).closest('label').addClass('selected');
        console.log('تم تغيير ارتفاع الحقول إلى:', $(this).val());
    });

    // تطبيق الحالة الأولية لخيارات ارتفاع الحقول
    $('.field-height-options input[type="radio"]:checked').closest('label').addClass('selected');

    // --- تفعيل خيارات التدرج اللوني للزر ---
    $('#button_gradient').on('change', function() {
        if ($(this).is(':checked')) {
            $('#gradient-options').slideDown(300);
        } else {
            $('#gradient-options').slideUp(300);
        }
        updateButtonPreview();
    });

    // --- تفعيل خيارات أيقونة الزر ---
    $('#button_use_icon').on('change', function() {
        if ($(this).is(':checked')) {
            $('#icon-options').slideDown(300);
        } else {
            $('#icon-options').slideUp(300);
        }
        updateButtonPreview();
    });

    // --- تحديث معاينة الزر ---
    function updateButtonPreview() {
        const $preview = $('.button-preview');
        const buttonText = $('#button_text').val() || 'إرسال الطلب';
        const buttonColor = $('#button_color').val() || '#3730a3';
        const textColor = $('#button_text_color').val() || '#ffffff';
        const borderRadius = $('#button_border_radius').val() || 'medium';
        const buttonSize = $('#button_size').val() || 'medium';
        const useGradient = $('#button_gradient').is(':checked');
        const gradientColor = $('#button_gradient_color').val() || '#4f46e5';
        const gradientDirection = $('#button_gradient_direction').val() || 'to-right';
        const useIcon = $('#button_use_icon').is(':checked');
        const iconType = $('#button_icon').val() || 'cart';
        const iconPosition = $('#button_icon_position').val() || 'right';
        const hoverEffect = $('#button_hover_effect').val() || 'darken';

        // تحديث النص
        let buttonHtml = '';

        // إضافة الأيقونة حسب الموضع
        if (useIcon && iconPosition === 'right') {
            buttonHtml += `<span class="dashicons dashicons-${iconType} ml-1"></span>`;
        }

        // إضافة النص
        buttonHtml += buttonText;

        // إضافة الأيقونة حسب الموضع
        if (useIcon && iconPosition === 'left') {
            buttonHtml += `<span class="dashicons dashicons-${iconType} mr-1"></span>`;
        }

        $preview.html(buttonHtml);

        // تحديث الألوان والتنسيق
        $preview.css({
            'background-color': buttonColor,
            'color': textColor
        });

        // تحديث استدارة الحواف
        let radiusValue = '0.375rem'; // medium (default)
        if (borderRadius === 'none') radiusValue = '0';
        else if (borderRadius === 'small') radiusValue = '0.25rem';
        else if (borderRadius === 'large') radiusValue = '0.5rem';
        else if (borderRadius === 'full') radiusValue = '9999px';

        $preview.css('border-radius', radiusValue);

        // تحديث حجم الزر
        let paddingValue = '0.5rem 1rem'; // medium (default)
        let fontSizeValue = '0.875rem'; // medium (default)

        if (buttonSize === 'small') {
            paddingValue = '0.25rem 0.75rem';
            fontSizeValue = '0.75rem';
        } else if (buttonSize === 'large') {
            paddingValue = '0.75rem 1.5rem';
            fontSizeValue = '1rem';
        }

        $preview.css({
            'padding': paddingValue,
            'font-size': fontSizeValue
        });

        // تحديث التدرج اللوني
        if (useGradient) {
            let gradientValue = `linear-gradient(${gradientDirection}, ${buttonColor}, ${gradientColor})`;
            $preview.css('background-image', gradientValue);
        } else {
            $preview.css('background-image', 'none');
        }

        // تحديث تأثير التحويم
        $preview.removeClass('hover:bg-indigo-700 hover:scale-105 hover:shadow-lg hover:bg-opacity-90');

        if (hoverEffect === 'darken') {
            $preview.addClass('hover:bg-opacity-90');
        } else if (hoverEffect === 'scale') {
            $preview.addClass('hover:scale-105');
        } else if (hoverEffect === 'shadow') {
            $preview.addClass('hover:shadow-lg');
        }
    }

    // --- تحديث معاينة طرق التوصيل ---
    function updateShippingPreview() {
        const $previewContainer = $('.shipping-preview-grid');
        const $previewItems = $('.shipping-preview-item');

        const bgColor = $('#shipping_bg_color').val() || '#ffffff';
        const borderColor = $('#shipping_border_color').val() || '#e2e8f0';
        const titleColor = $('#shipping_title_color').val() || '#1e293b';
        const priceColor = $('#shipping_price_color').val() || '#3730a3';
        const selectedBgColor = $('#shipping_selected_bg_color').val() || '#f0f6fc';
        const selectedBorderColor = $('#shipping_selected_border_color').val() || '#3730a3';

        // تحديث الخيار العادي
        $previewItems.eq(0).css({
            'background-color': bgColor,
            'border-color': borderColor
        });

        $previewItems.eq(0).find('.shipping-title').css('color', titleColor);
        $previewItems.eq(0).find('.shipping-price').css('color', priceColor);

        // تحديث الخيار المحدد
        $previewItems.eq(1).css({
            'background-color': selectedBgColor,
            'border-color': selectedBorderColor
        });

        $previewItems.eq(1).find('.shipping-title').css('color', titleColor);
        $previewItems.eq(1).find('.shipping-price').css('color', priceColor);
    }

    // معالج تغيير نوع حدود النموذج
    $('#form-border-style').on('change', function() {
        const borderStyle = $(this).val();
        if (borderStyle === 'none') {
            $('.border-settings').slideUp(300);
        } else {
            $('.border-settings').slideDown(300);
        }
    });

    // تحديث المعاينات عند تغيير أي إعداد
    $('.settings-field select, .settings-field input[type="text"]').on('change input', function() {
        if ($(this).closest('#submit-button').length) {
            updateButtonPreview();
        } else if ($(this).closest('#shipping-methods').length) {
            updateShippingPreview();
        }
    });

    // تحديث المعاينات عند تحميل الصفحة
    $(window).on('load', function() {
        updateButtonPreview();
        updateShippingPreview();

        // تحديث حالة إعدادات السلة
        if ($('#cart_system_enabled').is(':checked')) {
            $('.cart-button-options').show();
            $('.cart-floating-options').show();
            $('.cart-advanced-options').show();
        } else {
            $('.cart-button-options').hide();
            $('.cart-floating-options').hide();
            $('.cart-advanced-options').hide();
        }
    });
});
